# MaidProfit BE

This project is a Booking Addon Service built with NestJS. It provides various endpoints to manage booking addons.

## Technologies Used

- TypeScript
- NestJS
- MongoDB

## Installation

1. Clone the repository:
    ```bash
    git clone https://gitlab.com/maidprofit/maidprofit-be.git
    cd maidprofit-be
    ```

2. Install dependencies:
    ```bash
    yarn install
    ```

## Usage

1. Start the development server:
    ```bash
    yarn start:dev
    ```

2. The server will be running at `http://localhost:3000`.

## API Endpoints

### Booking Addon Controller

### API Endpoints

### 🗓️ Booking Addon Controller
| Method | Endpoint | Description |
|--------|----------|-------------|
| GET    | `/booking-addon/` | Get all booking addons |
| GET    | `/booking-addon/listing` | Get a list of booking addons by IDs |
| GET    | `/booking-addon/:id` | Get a booking addon by ID |
| GET    | `/booking-addon/type/:typed` | Get booking addons by type |
| POST   | `/booking-addon/create` | Create a new booking addon |
| POST   | `/booking-addon/create/multiple` | Create multiple booking addons |
| PATCH  | `/booking-addon/:id` | Update a booking addon by ID |
| DELETE | `/booking-addon/:id` | Delete a booking addon by ID |

### 📅 Booking Time Arrival Controller
| Method | Endpoint | Description |
|--------|----------|-------------|
| GET    | `/booking-time-arrival/` | Get all booking time arrivals |
| GET    | `/booking-time-arrival/:id` | Get a booking time arrival by ID |
| POST   | `/booking-time-arrival/create` | Create a new booking time arrival |
| PATCH  | `/booking-time-arrival/:id` | Update a booking time arrival by ID |
| DELETE | `/booking-time-arrival/:id` | Delete a booking time arrival by ID |

### 📊 Booking Types Controller
| Method | Endpoint | Description |
|--------|----------|-------------|
| GET    | `/booking-types/` | Get all booking types |
| GET    | `/booking-types/:id` | Get a booking type by ID |
| POST   | `/booking-types/create` | Create a new booking type |
| PATCH  | `/booking-types/:id` | Update a booking type by ID |
| DELETE | `/booking-types/:id` | Delete a booking type by ID |

### 📆 Calendar Controller
| Method | Endpoint | Description |
|--------|----------|-------------|
| GET    | `/calendar/` | Get all calendar events |
| GET    | `/calendar/:id` | Get a calendar event by ID |
| POST   | `/calendar/create` | Create a new calendar event |
| PATCH  | `/calendar/:id` | Update a calendar event by ID |
| DELETE | `/calendar/:id` | Delete a calendar event by ID |

---

## Financial Operations

### 💰 Invoices Controller
| Method | Endpoint | Description |
|--------|----------|-------------|
| GET    | `/invoices/` | Get all invoices |
| GET    | `/invoices/:id` | Get an invoice by ID |
| POST   | `/invoices/create` | Create a new invoice |
| PATCH  | `/invoices/:id` | Update an invoice by ID |
| DELETE | `/invoices/:id` | Delete an invoice by ID |

### 💼 Quotes Controller
| Method | Endpoint | Description |
|--------|----------|-------------|
| GET    | `/quotes/` | Get all quotes |
| GET    | `/quotes/:id` | Get a quote by ID |
| POST   | `/quotes/create` | Create a new quote |
| PATCH  | `/quotes/:id` | Update a quote by ID |
| DELETE | `/quotes/:id` | Delete a quote by ID |

### 🧾 Tax Controller
| Method | Endpoint | Description |
|--------|----------|-------------|
| GET    | `/tax/` | Get all taxes |
| GET    | `/tax/:id` | Get a tax by ID |
| POST   | `/tax/create` | Create a new tax |
| PATCH  | `/tax/:id` | Update a tax by ID |
| DELETE | `/tax/:id` | Delete a tax by ID |

### 💳 Payment Controller
| Method | Endpoint | Description |
|--------|----------|-------------|
| GET    | `/payment/` | Get all payments |
| GET    | `/payment/:id` | Get a payment by ID |
| POST   | `/payment/create` | Create a new payment |
| PATCH  | `/payment/:id` | Update a payment by ID |
| DELETE | `/payment/:id` | Delete a payment by ID |

### 💱 Transactions Controller
| Method | Endpoint | Description |
|--------|----------|-------------|
| GET    | `/transactions/` | Get all transactions |
| GET    | `/transactions/:id` | Get a transaction by ID |
| POST   | `/transactions/create` | Create a new transaction |
| PATCH  | `/transactions/:id` | Update a transaction by ID |
| DELETE | `/transactions/:id` | Delete a transaction by ID |

### 📋 Billing Controller
| Method | Endpoint | Description |
|--------|----------|-------------|
| GET    | `/billing/` | Get all billing records |
| GET    | `/billing/:id` | Get a billing record by ID |
| POST   | `/billing/create` | Create a new billing record |
| PATCH  | `/billing/:id` | Update a billing record by ID |
| DELETE | `/billing/:id` | Delete a billing record by ID |

### 💲 Payment Method Controller
| Method | Endpoint | Description |
|--------|----------|-------------|
| GET    | `/payment-method/` | Get all payment methods |
| GET    | `/payment-method/:id` | Get a payment method by ID |
| POST   | `/payment-method/create` | Create a new payment method |
| PATCH  | `/payment-method/:id` | Update a payment method by ID |
| DELETE | `/payment-method/:id` | Delete a payment method by ID |

---

## User Management

### 👥 Clients Data Controller
| Method | Endpoint | Description |
|--------|----------|-------------|
| GET    | `/clients-data/` | Get all clients data |
| GET    | `/clients-data/:id` | Get a client data by ID |
| POST   | `/clients-data/create` | Create a new client data |
| PATCH  | `/clients-data/:id` | Update a client data by ID |
| DELETE | `/clients-data/:id` | Delete a client data by ID |

### 🏠 Address Controller
| Method | Endpoint | Description |
|--------|----------|-------------|
| GET    | `/address/` | Get all addresses |
| GET    | `/address/:id` | Get an address by ID |
| POST   | `/address/create` | Create a new address |
| PATCH  | `/address/:id` | Update an address by ID |
| DELETE | `/address/:id` | Delete an address by ID |

### 🔐 Iam Controller
| Method | Endpoint | Description |
|--------|----------|-------------|
| GET    | `/iam/` | Get all IAM records |
| GET    | `/iam/:id` | Get an IAM record by ID |
| POST   | `/iam/create` | Create a new IAM record |
| PATCH  | `/iam/:id` | Update an IAM record by ID |
| DELETE | `/iam/:id` | Delete an IAM record by ID |

---

## Communication

### 📢 Notification Controller
| Method | Endpoint | Description |
|--------|----------|-------------|
| GET    | `/notification/` | Get all notifications |
| GET    | `/notification/:id` | Get a notification by ID |
| POST   | `/notification/create` | Create a new notification |
| PATCH  | `/notification/:id` | Update a notification by ID |
| DELETE | `/notification/:id` | Delete a notification by ID |

### 📧 Send Email Controller
| Method | Endpoint | Description |
|--------|----------|-------------|
| POST   | `/send-email/` | Send an email |

---

## System Operations

### 🏢 Workspace Controller
| Method | Endpoint | Description |
|--------|----------|-------------|
| GET    | `/workspace/` | Get all workspaces |
| GET    | `/workspace/:id` | Get a workspace by ID |
| POST   | `/workspace/create` | Create a new workspace |
| PATCH  | `/workspace/:id` | Update a workspace by ID |
| DELETE | `/workspace/:id` | Delete a workspace by ID |

### 📦 Packages Controller
| Method | Endpoint | Description |
|--------|----------|-------------|
| GET    | `/packages/` | Get all packages |
| GET    | `/packages/:id` | Get a package by ID |
| POST   | `/packages/create` | Create a new package |
| PATCH  | `/packages/:id` | Update a package by ID |
| DELETE | `/packages/:id` | Delete a package by ID |

### 🔄 Subscription Controller
| Method | Endpoint | Description |
|--------|----------|-------------|
| GET    | `/subscription/` | Get all subscriptions |
| GET    | `/subscription/:id` | Get a subscription by ID |
| POST   | `/subscription/create` | Create a new subscription |
| PATCH  | `/subscription/:id` | Update a subscription by ID |
| DELETE | `/subscription/:id` | Delete a subscription by ID |

### 🎫 Token Storage Controller
| Method | Endpoint | Description |
|--------|----------|-------------|
| GET    | `/token-storage/` | Get all tokens |
| GET    | `/token-storage/:id` | Get a token by ID |
| POST   | `/token-storage/create` | Create a new token |
| PATCH  | `/token-storage/:id` | Update a token by ID |
| DELETE | `/token-storage/:id` | Delete a token by ID |

### 📄 Generate PDF Controller
| Method | Endpoint | Description |
|--------|----------|-------------|
| POST   | `/generate-pdf/` | Generate a PDF |


## POSTMAN Collection

- [MaidProfit BE](https://universal-capsule-705870.postman.co/workspace/CRM-SNB-Web-App~e806f749-bd91-471a-8c0b-816c1dd17d4e/collection/********-2d41fd0e-fe4d-4491-8d25-7db9b5a8df6c?action=share&source=copy-link&creator=********&active-environment=48446a18-8f2e-450b-827f-a4da1695c3dc)
- Account:  `SNBDeveloper / Password5%`

## Deployment

This project uses CircleCI for continuous integration and deployment. The deployment process is automatically triggered when changes are merged from the `develop` branch to the `development` branch.

### CircleCI Configuration

Our CircleCI configuration is defined in the `.circleci/config.yml` file in the root of the repository. Here's an overview of the deployment process:

1. When a pull request is merged from `develop` to `development`, CircleCI automatically detects the change.
2. CircleCI runs the defined jobs in the configuration file, which typically include:
   - Building the project
   - Running tests
   - Deploying to the development environment

### Deployment Process

1. **Merge to Development**: Create a pull request from `develop` to `development` and merge it.
2. **Automatic Trigger**: CircleCI automatically detects the merge and initiates the deployment pipeline.
3. **Build**: The project will be built on circle ci .
4. **Deployment**: If all the build is completed, the application is automatically deployed to the development environment.

### Monitoring Deployments

You can monitor the status of your deployments in the CircleCI dashboard. Look for the build associated with the latest commit on the `development` branch.

### Troubleshooting

If a deployment fails:

1. Check the CircleCI logs for error messages.
2. Verify that all tests are passing locally.
3. Ensure that your CircleCI configuration file (`.circleci/config.yml`) is properly formatted and includes all necessary steps.

For more detailed information on our CircleCI setup, please refer to the `.circleci/config.yml` file in this repository.

### TODO: Production Deployment Flow

- [ ] Create a production deployment pipeline
   - [ ] Define the production branch (e.g.,  `production`)
   - [ ] Set up environment-specific configurations for production
   - [ ] Implement additional security measures for production deployments
   - [ ] Create a separate CircleCI job for production deployment
   - [ ] Set up approval gates or manual triggers for production deployments
   - [ ] Implement a rollback strategy for failed production deployments
   - [ ] Update the README with production deployment instructions