# Stage 1: Build the application
FROM node:22-alpine AS builder
LABEL authors="huynguyen"

# Set the working directory
WORKDIR /app

# Copy package.json and yarn.lock
COPY package.json yarn.lock ./

# Install dependencies
RUN yarn install --production=false

# Install @nestjs/cli as a development dependency
RUN yarn add @nestjs/cli

# Copy the rest of the application code
COPY . .

# Build the application using npx
RUN npx nest build

# Stage 2: Create the final image
FROM node:22-alpine

# Set the working directory
WORKDIR /app

# Copy the built application from the builder stage
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/public ./public
COPY --from=builder /app/src ./src
COPY --from=builder /app/package.json ./package.json
COPY --from=builder /app/yarn.lock ./yarn.lock

# Install production dependencies
#RUN yarn install --production

# Expose the application port
EXPOSE 3000

# Start the application
CMD ["sh", "-c", "yarn add firebase-admin && node ./dist/main"]