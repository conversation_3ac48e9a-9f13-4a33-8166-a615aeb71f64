version: 2.1

jobs:
  build_dev:
    docker:
      - image: cimg/deploy:2024.08-node
    steps:
      - setup_remote_docker
      - checkout
      - run:
          name: Checkout development branch
          command: |
            git checkout development
            git pull origin development
      - run:
          name: Build Docker image
          command: |
            docker build -t maidprofit-be -f Dockerfile .
      - run:
          name: Save Docker image to tar file
          command: |
            docker save maidprofit-be -o maidprofit-be.tar
      - persist_to_workspace:
          root: .
          paths:
            - maidprofit-be.tar

  deploy_dev:
    docker:
      - image: cimg/base:stable
    steps:
      - setup_remote_docker
      - attach_workspace:
          at: .
      - run:
          name: Load image to docker
          command: |
            docker load -i maidprofit-be.tar
      - run:
          name: Login to docker hub
          command: |
            docker login -u ${DOCKER_USER} -p ${DOCKER_PASS}
      - run:
          name: Push to docker hub
          command: |
            docker tag maidprofit-be:latest ${DOCKER_USER}/maidprofit-be:beta-latest
            docker push ${DOCKER_USER}/maidprofit-be:beta-latest
      - add_ssh_keys
      - run:
          name: Add the server to known hosts
          command: |
            ssh-keyscan -H ${SERVER_DEV} >> ~/.ssh/known_hosts
      - run:
          name: Run migration and restart server
          command: |
            ssh root@${SERVER_DEV} "sudo docker login -u ${DOCKER_USER} -p ${DOCKER_PASS} && cd maidprofit-crm && sudo docker compose pull && sudo docker compose build && sudo docker compose up -d"


  build_prod:
    docker:
      - image: cimg/deploy:2024.08-node
    steps:
      - setup_remote_docker
      - checkout
      - run:
          name: Checkout prod branch
          command: |
            git checkout prod
            git pull origin prod
      - run:
          name: Build Docker image
          command: |
            docker build -t maidprofit-be -f Dockerfile .
      - run:
          name: Save Docker image to tar file
          command: |
            docker save maidprofit-be -o maidprofit-be.tar
      - persist_to_workspace:
          root: .
          paths:
            - maidprofit-be.tar

  deploy_prod:
    docker:
      - image: cimg/base:stable
    steps:
      - setup_remote_docker
      - attach_workspace:
          at: .
      - run:
          name: Load image to docker
          command: |
            docker load -i maidprofit-be.tar
      - run:
          name: Login to docker hub
          command: |
            docker login -u ${DOCKER_USER} -p ${DOCKER_PASS}
      - run:
          name: Push to docker hub
          command: |
            docker tag maidprofit-be:latest ${DOCKER_USER}/maidprofit-be:prod-latest
            docker push ${DOCKER_USER}/maidprofit-be:prod-latest
      - add_ssh_keys
      - run:
          name: Add the server to known hosts
          command: |
            ssh-keyscan -H ${SERVER_PROD} >> ~/.ssh/known_hosts
      - run:
          name: Run migration and restart server
          command: |
            ssh root@${SERVER_PROD} "sudo docker login -u ${DOCKER_USER} -p ${DOCKER_PASS} && cd maidprofit && sudo docker compose pull && sudo docker compose build && sudo docker compose up -d"


workflows:
  build-and-deploy-dev:
    jobs:
      - build_dev:
          filters:
            branches:
              only: development
      - deploy_dev:
          requires:
            - build_dev
          filters:
            branches:
              only: development

  build-and-deploy-prod:
    jobs:
      - build_prod:
          filters:
            branches:
              only: prod
      - deploy_prod:
          requires:
            - build_prod
          filters:
            branches:
              only: prod