import {
  OnGatewayDisconnect, OnGatewayInit,
  SubscribeMessage,
  WebSocketGateway,
} from "@nestjs/websockets";
import { Server, Socket } from "socket.io";
import { InfoService } from "./info.service";

@WebSocketGateway({
  cors: {
    origin: "*"
  },
  namespace: "notification"
})
export class InfoGateway implements OnGatewayInit, OnGatewayDisconnect {
  constructor(private readonly infoService: InfoService) {
  }

  afterInit(server: Server) {
    this.infoService.socketServer = server;
  }

  handleDisconnect(client: Socket) {
    this.infoService.handleDisconnect(client);
  }

  @SubscribeMessage("register")
  handleRegister(client: Socket, workspaceId: string): boolean {
    console.log("Registering client", workspaceId);
    this.infoService.handleConnection(client, workspaceId);
    return true;
  }
}
