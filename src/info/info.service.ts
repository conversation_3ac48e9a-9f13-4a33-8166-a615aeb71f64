import { Injectable } from "@nestjs/common";
import { Server, Socket } from "socket.io";

@Injectable()
export class InfoService {
  public socketServer: Server;
  private readonly connectedClients: Map<string, string[]> = new Map();

  handleConnection(client: Socket, workspaceId: string): void {
    const clientId = client.id;
    if (this.connectedClients.has(workspaceId)) {
      const clients = this.connectedClients.get(workspaceId);
      if (!clients.includes(clientId)) {
        clients.push(clientId);
      }
    } else {
      this.connectedClients.set(workspaceId, [clientId]);
    }
  }

  handleDisconnect(client: Socket): void {
    const clientId = client.id;
    for (const [workspaceId, clients] of this.connectedClients.entries()) {
      const index = clients.indexOf(clientId);
      if (index !== -1) {
        clients.splice(index, 1);
        if (clients.length === 0) {
          this.connectedClients.delete(workspaceId);
        }
        break;
      }
    }
  }

  sendNotificationToWorkspace(workspaceId: string, message: string): void {
    console.log("Sending notification to workspace", workspaceId);
    console.log("Connected clients", this.connectedClients);
    if (this.connectedClients.has(workspaceId)) {
      const clientIds = this.connectedClients.get(workspaceId);
      this.socketServer.to(clientIds).emit("notification", message);
    }
  }
}