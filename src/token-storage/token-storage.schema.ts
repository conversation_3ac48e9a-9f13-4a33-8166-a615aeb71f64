import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';
@Schema()
export class TokenStorage extends Document{

    _id: MongooseSchema.Types.ObjectId;

    @Prop({ required: true})
    token: string;

    @Prop({
        required: true,
        default: () => new Date(Date.now() + 7*24*60*60*1000)
    })
    expires: Date;

    @Prop({
        required: true,
        default: new Date()
    })
    dateCreated: Date;

}
export const TokenStorageSchema = SchemaFactory.createForClass(TokenStorage);