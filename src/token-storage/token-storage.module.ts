import { Modu<PERSON> } from '@nestjs/common';
import { TokenStorage, TokenStorageSchema } from './token-storage.schema';
import { MongooseModule } from '@nestjs/mongoose';
import { TokenStorageService } from './token-storage.service';

@Module({
    imports: [
        MongooseModule.forFeature([{ name: TokenStorage.name, schema: TokenStorageSchema }]),
    ],
    providers: [TokenStorageService],
    exports: [TokenStorageService],
})
export class TokenStorageModule {}
