import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { TokenStorage } from './token-storage.schema';
import CreateTokenStorage from './Dto/createTokenStorage';

@Injectable()
export class TokenStorageService {
    constructor(
        @InjectModel(TokenStorage.name) private tokenStorageModel: Model<TokenStorage>,
    ) {}


    async createTokenStorage(tokenStorage: CreateTokenStorage): Promise<TokenStorage> {
        const createdTokenStorage = new this.tokenStorageModel(tokenStorage);
        return createdTokenStorage.save();
    }

    async checkValidTokenStorage(token: string): Promise<boolean> {
        const tokenObj = await this.tokenStorageModel.findOne({ token }).exec();
        if (tokenObj) {
            if (tokenObj.expires > new Date()) {
                return true;
            }
        }
        return false;
    }
}
