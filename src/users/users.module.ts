import { forwardRef, Module } from "@nestjs/common";
import { UsersService } from './users.service';
import { MongooseModule } from '@nestjs/mongoose';
import { Users, UsersSchema } from './users.schema';
import { Roles, RolesSchema } from 'src/roles/roles.schema';
import {
  WorkingHours,
  WorkingHoursSchema,
} from 'src/working-hours/working-hours.schema';
import { SendEmailModule } from "../send-email/send-email.module";
import { WorkspaceModule } from "../workspace/workspace.module";

@Module({
  imports: [
    forwardRef(() => SendEmailModule),
    forwardRef(() => WorkspaceModule),
    MongooseModule.forFeature([
      { name: Users.name, schema: UsersSchema },
      { name: Roles.name, schema: RolesSchema },
      { name: WorkingHours.name, schema: WorkingHoursSchema },
    ]),
  ],
  providers: [UsersService],
  exports: [UsersService],
})
export class UsersModule {}
