import { BadRequestException, HttpException, HttpStatus, Injectable } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Users, UsersDocument, UserSocial } from "./users.schema";
import mongoose, { <PERSON>, Schema } from "mongoose";
import { Roles, RolesDocument } from "src/roles/roles.schema";
import { CreateUserDto } from "src/auth/Dto/CreateUserDto";
import { CollectionDto, DocumentCollector } from "@forlagshuset/nestjs-mongoose-paginate";
import { WorkingHours, WorkingHoursDocument } from "src/working-hours/working-hours.schema";
import UpdateUserDto from "src/auth/Dto/UpdateUserDto";
import { genSalt, hash } from "bcrypt";
import { users } from "@clerk/clerk-sdk-node";
import { SendEmailService } from "../send-email/send-email.service";
import * as process from "node:process";
import { WorkspaceService } from "../workspace/workspace.service";
import {v4} from 'uuid';

// This should be a real class/interface representing a user entity
export type User = any;

@Injectable()
export class UsersService {
  constructor(
    @InjectModel(Users.name) private userModel: Model<UsersDocument>,
    @InjectModel(Roles.name) private roleModel: Model<RolesDocument>,
    @InjectModel(WorkingHours.name)
    private workingHoursModel: Model<WorkingHoursDocument>,
    private readonly sendEmailService: SendEmailService,
    private readonly workspaceService: WorkspaceService
  ) {
  }

  async create(createUser: {
    userId: string;
    firstName: string;
    lastName: string;
    username: string;
    email: string;
    createdAt: Date;
  }): Promise<User> {
    const newUser = new this.userModel(createUser);
    return newUser.save();
  }

  async createUser(createUserDto: CreateUserDto): Promise<User> {
    const existedRole = await this.roleModel.findOne({
      _id: createUserDto.role
    });

    if (!existedRole) {
      throw new BadRequestException();
    }

    const existedUser = await this.userModel.findOne({
      email: createUserDto.email,
      workspace: new mongoose.Types.ObjectId(createUserDto.workspace)
    });

    if (existedUser) {
      throw new HttpException(
        "User has already existed",
        HttpStatus.BAD_REQUEST
      );
    }
    const salt = await genSalt();
    // Create User on database
    const newUser = new this.userModel({
      firstName: createUserDto.firstName,
      lastName: createUserDto.lastName,
      email: createUserDto.email,
      social: createUserDto.social,
      password: createUserDto.password ? await hash(createUserDto.password, salt) : "",
      createdAt: new Date(),
      workspace: createUserDto?.workspace ? new mongoose.Types.ObjectId(createUserDto.workspace) : null,
      roles: [createUserDto.role],
      phoneNumber: createUserDto.phoneNumber,
      address: createUserDto.address,
      dateOfBirth: createUserDto.dateOfBirth
    });

    return await newUser.save();
  }

  async findOne(userId: string, workspace?: string): Promise<User> {
    if (!workspace) {
      return this.userModel.findById(userId).select("-password").exec();
    }
    return this.userModel
      .findOne({
        _id: new mongoose.Types.ObjectId(userId),
        workspace: new mongoose.Types.ObjectId(workspace)
      })
      .populate("workspace")
      .select("-password")
      .exec();
  }


  async findOwnerByWorkspace(workspace: string): Promise<User> {
    return this.userModel
      .findOne({
        workspace: new mongoose.Types.ObjectId(workspace),
        roles: { $in: ["6533908eb9fb8bb784027f0c"] }
      })
      .select("-password")
      .exec();
  }

  async findOneByEmail(email: string, workspace?: string): Promise<User> {
    if (!workspace) {
      return this.userModel
        .findOne({
          email
        })
        .populate("workspace")
        .select("+password")
        .exec();
    }
    return this.userModel
      .findOne({
        email,
        workspace: new mongoose.Types.ObjectId(workspace)
      })
      .populate("workspace")
      .select("+password")
      .exec();
  }

  async findOneByPhoneNumber(phoneNumber: string, workspace?: string): Promise<User> {
    if (!workspace) {
      return this.userModel
        .findOne({
          phoneNumber
        })
        .populate("workspace")
        .select("-password")
        .exec();
    }
    return this.userModel
      .findOne({
        phoneNumber,
        workspace: new mongoose.Types.ObjectId(workspace)
      })
      .populate("workspace")
      .select("-password")
      .exec();
  }

  async queryAll(collectionDto: CollectionDto): Promise<any> {
    const collector = new DocumentCollector<UsersDocument>(this.userModel);
    //
    // if (collectionDto?.filter) {
    //   // @ts-ignore
    //   collectionDto.filter = JSON.parse(collectionDto.filter);
    // }
    //
    if (collectionDto.filter?.workspace) {
      collectionDto.filter.workspace = new mongoose.Types.ObjectId(
        collectionDto.filter.workspace as string
      );
    }

    return collector.find(collectionDto);
  }

  async updateOne(
    id: string,
    updateUserDto: Partial<UpdateUserDto>
  ): Promise<Users> {

    let updateUserData: any = {
      ...updateUserDto
    };

    if (updateUserDto.workspace) {
      updateUserData.workspace = new mongoose.Types.ObjectId(
        updateUserDto.workspace
      );
    }

    if (updateUserDto.role) {
      updateUserData.roles = [updateUserDto.role];
    }

    return this.userModel.findByIdAndUpdate(id, updateUserData, {
      new: true
    }).populate("workspace")
      .select("-password");
  }

  async createWorkspaceOne(
    id: string,
    workspace: string
  ): Promise<Users> {


    return this.userModel.findByIdAndUpdate(id, {
      workspace: new mongoose.Types.ObjectId(workspace)
    }, { new: true });
  }

  async deleteOne(userId: string, workspace: string): Promise<any> {
    await users.deleteUser(userId);

    return this.userModel
      .findOneAndRemove({
        userId: userId,
        workspace: new mongoose.Types.ObjectId(workspace)
      })
      .exec();
  }

  async findOneByPhone(phone: string): Promise<User> {
    return this.userModel
      .findOne({
        phoneNumber: phone
      })
      .exec();
  }

  async findMany(listUserId: string[]): Promise<User[]> {
    return this.userModel.find({
      _id: { $in: listUserId }
    });
  }

  async inviteUser(emails: string[], workspace: string, userId: string): Promise<User[]> {
    const existingUser = await this.userModel.findOne({
      email: { $in: emails }
    }).exec();
    const existingWorkspace = await this.workspaceService.findOne(workspace);

    if (existingUser) {
      throw new HttpException(
        "One or more users have already existed",
        HttpStatus.BAD_REQUEST
      );
    }
    if (!existingWorkspace) {
      throw new HttpException(
        "Workspace does not exist",
        HttpStatus.BAD_REQUEST
      );
    }
    const frontendUrl = process.env.FRONTEND_URL;
    try {
      const createdUsers = await Promise.all(emails.map(async (email) => {
        const invitationToken = v4();
        await this.sendEmailService.sendEmailGoogle({
          toEmail: email,
          subject: "Invitation to join workspace",
          body: `
                        <div>You have been invited to join workspace ${existingWorkspace.name}</div>
                        <div>Visit <a href="${frontendUrl}/authentication/sign-up?invitationToken=${invitationToken}">MaidProfit</a> to register</div>
                    `
        }, workspace);

        return await this.userModel.create({
          email,
          workspace: new mongoose.Types.ObjectId(workspace),
          createdAt: new Date(),
          roles: ["64b735ddd967af92ea9da2de"], // Staff
          userStatus: "invited",
          invitedAt: new Date(),
          invitedBy: new mongoose.Types.ObjectId(userId),
          invitationToken,
        });
      }));

      return createdUsers;
    } catch (err) {
      throw new HttpException(
        err?.message || "Invite users failed",
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  async revokeInviteUser(email: string, workspace: string, userId: string) {
    const existingUser = await this.userModel.findOne({
      email,
      workspace: new mongoose.Types.ObjectId(workspace),
      userStatus: "invited"
    }).exec();

    if (!existingUser) {
      throw new HttpException(
        "User does not exist or has already registered",
        HttpStatus.BAD_REQUEST
      );
    }

    return this.userModel.findOneAndRemove({
      email,
      workspace: new mongoose.Types.ObjectId(workspace),
      userStatus: "invited"
    }).exec();
  }

  async updateRole(userId: string, role: string): Promise<User> {
    const existedRole = await this.roleModel.findOne({
      _id: role
    });
    const existedUser = await this.userModel.findOne({
      _id: userId
    }).populate("roles").exec();

    if (!existedRole || !existedUser) {
      throw new BadRequestException();
    }
    return this.userModel.findByIdAndUpdate(userId, {
      roles: [role]
    }, { new: true }).exec();
  }
}
