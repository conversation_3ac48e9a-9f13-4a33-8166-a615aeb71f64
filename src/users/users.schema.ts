import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema, Types } from 'mongoose';
import { Roles } from 'src/roles/roles.schema';
import { Workspace } from '../workspace/workspace.schema';
import { Exclude, Type } from 'class-transformer';
import { WorkingHours } from 'src/working-hours/working-hours.schema';
import { Address } from '../address/address.schema';

export enum UserSocial {
    GOOGLE = 'google',
    FACEBOOK = 'facebook',
    APPLE = 'apple',
    MANUAL = 'manual'
}


export enum EDateFormat {
    DDMMYYYY = 'DD/MM/YYYY',
    MMDDYYYY = 'MM/DD/YYYY',
}

export enum ETimeFormat {
    TWELVE = '12',
    TWENTYFOUR = '24',
}

export enum ELang {
    EN = 'en',
    ES = 'es',
}

@Schema()
export class Users {
    _id: MongooseSchema.Types.ObjectId;

    @Prop({ required: false, default: '' })
    profileImage: string;

    @Prop({required: false, default: ''})
    firstName: string;

    @Prop({required: false, default: ''})
    lastName: string;

    @Prop({ required: true })
    email: string;

    @Prop({required: false, default: ''})
    @Exclude()
    password: string;

    @Prop({required: false, default: ''})
    phoneNumber: string;

    @Prop({ required: false, default: UserSocial.MANUAL})
    social: UserSocial;

    @Prop({required: false, type: Types.ObjectId, ref: Address.name })
    address: Address;

    @Prop({required: false, default: false})
    isFullInformation: boolean;

    @Prop({required: false, type: Types.ObjectId, ref: WorkingHours.name })
    workingHours: WorkingHours;

    @Prop({ required: true, type: Types.ObjectId, ref: Roles.name })
    roles: string[];

    @Prop({ required: false, type: Boolean })
    gender: boolean;

    @Prop({ required: false, type: Date })
    dateOfBirth: Date;

    @Prop({ required: true })
    createdAt: Date;

    @Prop({ required: false, default: EDateFormat.DDMMYYYY})
    dateFormat: string;

    @Prop({ required: false, default: ETimeFormat.TWELVE})
    timeFormat: string;

    @Prop({ required: false, default: ELang.EN})
    lang: string

    @Prop({ required: false, type: Types.ObjectId, ref: Workspace.name })
    workspace: Workspace;

    @Prop({ required: false, type: String })
    deviceToken?: string;

    @Prop({ required: false, type: String })
    deviceType?: string;

    @Prop({ required: false, type: String, enum: ['invited', 'registered', 'banned'] })
    userStatus?: string;

    @Prop({ required: false, type: Date })
    invitedAt?: Date;

    @Prop({ required: false, type: Types.ObjectId, ref: Users.name })
    invitedBy?: Users;

    @Prop({ required: false, type: String })
    invitationToken?: string;
}

export type UsersDocument = Users & Document;
const UsersSchema = SchemaFactory.createForClass(Users);

UsersSchema.index({ firstName: 'text', lastName: 'text', username: 'text', email: 'text' });

export { UsersSchema }