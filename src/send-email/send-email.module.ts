import { Module, forwardRef } from '@nestjs/common';
import { SendEmailService } from './send-email.service';
import { MongooseModule } from '@nestjs/mongoose';
import { SendEmail, SendEmailSchema } from './send-email.schema';
import { SendEmailController } from './send-email.controller';
import { UsersModule } from '../users/users.module';
import { ClientsModule } from '../clients/clients.module';
import { SmtpConfigModule } from "../smtp-config/smtp-config.module";
import { QuotesModule } from 'src/quotes/quotes.module';
import { JobsModule } from 'src/jobs/jobs.module';
import { IamModule } from 'src/iam/iam.module';
import { WorkspaceModule } from 'src/workspace/workspace.module';

@Module({
    imports: [
        forwardRef(() => ClientsModule),
        forwardRef(() => UsersModule),
        forwardRef(() => SmtpConfigModule),
        MongooseModule.forFeature([
            { name: SendEmail.name, schema: SendEmailSchema },
        ]),
        QuotesModule,
        forwardRef(() => JobsModule),
        forwardRef(() => IamModule),
        forwardRef(() => WorkspaceModule),
    ],
    providers: [SendEmailService],
    exports: [SendEmailService],
    controllers: [SendEmailController],
})
export class SendEmailModule {}
