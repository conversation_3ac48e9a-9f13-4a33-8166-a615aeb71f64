import { <PERSON><PERSON>, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose'
import { Document, Schema as MongooseSchema, Types } from "mongoose";
import { Workspace } from "../workspace/workspace.schema";
import { Type } from "class-transformer";

@Schema()
export class SendEmail{
  _id: MongooseSchema.Types.ObjectId;

  @Prop({ required: true })
  fromEmailName: string;

  @Prop({ required: true })
  subject: string;

  @Prop({ required: true })
  body: string;

  @Prop({ required: true })
  emailAddressesId: string;

  @Prop({ required: true })
  status: 'success' | 'failed';

  @Prop()
  message: string;

  @Prop({ required: true })
  dateCreated: Date;

  @Prop({ required: true, type: Types.ObjectId, ref: Workspace.name })
  @Type(() => Workspace)
  workspace: Workspace;

}

export type SendEmailDocument = SendEmail & Document
export const SendEmailSchema = SchemaFactory.createForClass(SendEmail)