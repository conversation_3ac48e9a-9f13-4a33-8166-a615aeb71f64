interface BookingRequest {
  firstName: string;
  lastName: string;
  phone: string;
  email: string;
  address: string;
  service: string;
  video?: string;
  attachments?: string[];
}

interface Issue {
  _id: string;
}

interface BookingConfirmation {
  firstName: string;
  lastName: string;
  phone: string;
  email: string;
  address: string;
  service: string;
  suitableDate: string;
  frequency: string;
  price: number;
  companyName: string;
  companyLogo: string;
  workspace: string;
}

export const BookingConfirmationEmail = (booking: BookingConfirmation) => {
  const public_url = `${process.env.SPACES_URL}/${process.env.SPACES_FOLDER_NAME}/`;
  const companyLogo = booking.companyLogo ? public_url + booking.companyLogo : '';
  
  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Booking Confirmation</title>
    </head>
    <body style="margin: 0; padding: 0; font-family: Arial, sans-serif; background-color: #f4f4f4;">
      <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff; box-shadow: 0 0 10px rgba(0,0,0,0.1);">
        <!-- Header with Logo -->
        <div style="text-align: center; padding: 20px; background-color: #5E17EB; border-radius: 4px 4px 0 0;">
          ${companyLogo ? `<img src="${companyLogo}" alt="${booking.companyName}" style="max-width: 150px; height: auto;">` : ''}
          <h1 style="color: #ffffff; margin: ${companyLogo ? '10px 0 0' : '0'}; font-size: 24px;">${booking.companyName}</h1>
        </div>
        
        <!-- Main Content -->
        <div style="padding: 30px 20px;">
          <p style="color: #666; font-size: 16px; margin-bottom: 20px;">
            Dear ${booking.firstName} ${booking.lastName},
          </p>
          <p style="color: #666; font-size: 16px; margin-bottom: 30px;">
            Thank you for booking with ${booking.companyName}. Your booking has been received and is being processed.
          </p>
          
          <!-- Booking Details Box -->
          <div style="background-color: #f8f9fa; border-radius: 8px; padding: 20px; margin-bottom: 30px;">
            <h2 style="color: #333; font-size: 20px; margin: 0 0 20px;">Booking Details</h2>
            <table style="width: 100%; border-collapse: collapse;">
              <tr>
                <td style="padding: 12px 0; color: #666; width: 140px;"><strong>Service:</strong></td>
                <td style="padding: 12px 0; color: #333;">${booking.service}</td>
              </tr>
              <tr>
                <td style="padding: 12px 0; color: #666;"><strong>Date & Time:</strong></td>
                <td style="padding: 12px 0; color: #333;">${booking.suitableDate}</td>
              </tr>
              <tr>
                <td style="padding: 12px 0; color: #666;"><strong>Frequency:</strong></td>
                <td style="padding: 12px 0; color: #333;">${booking.frequency}</td>
              </tr>
              <tr>
                <td style="padding: 12px 0; color: #666;"><strong>Address:</strong></td>
                <td style="padding: 12px 0; color: #333;">${booking.address}</td>
              </tr>
              <tr>
                <td style="padding: 12px 0; color: #666;"><strong>Price:</strong></td>
                <td style="padding: 12px 0; color: #333;">$${booking.price.toFixed(2)}</td>
              </tr>
            </table>
          </div>
          
          <!-- Contact Information -->
          <div style="background-color: #f8f9fa; border-radius: 8px; padding: 20px;">
            <h3 style="color: #333; font-size: 18px; margin: 0 0 15px;">Need to make changes?</h3>
            <p style="color: #666; margin: 0 0 10px;">If you need to modify your booking or have any questions, please contact us:</p>
            <p style="color: #666; margin: 0 0 5px;">
              <strong>Phone:</strong> <a href="tel:${booking.phone}" style="color: #5E17EB; text-decoration: none;">${booking.phone}</a>
            </p>
            <p style="color: #666; margin: 0;">
              <strong>Email:</strong> <a href="mailto:${booking.email}" style="color: #5E17EB; text-decoration: none;">${booking.email}</a>
            </p>
          </div>
        </div>
        
        <!-- Footer -->
        <div style="text-align: center; padding: 20px; background-color: #f4f4f4; border-radius: 0 0 4px 4px;">
          <p style="color: #999; margin: 0; font-size: 14px;">
            © ${new Date().getFullYear()} ${booking.companyName}
          </p>
        </div>
      </div>
    </body>
    </html>
  `;
};

export const NotificationEmailAdminBookingRequest = (bookingRequest:BookingRequest) => {
  let attachments = bookingRequest.attachments?.map(attachment => {
    return `<li><a href="${attachment}">${attachment}</a></li>`
  }).join('\n')
  return `
    <h1>Hi you have new booking Request, here is booking request information:</h1>
    <p>First Name: ${bookingRequest.firstName}</p>
    <p>Last Name: ${bookingRequest.lastName}</p>
    <p>Phone: <a href="tel:${bookingRequest.phone}">${bookingRequest.phone}</a></p>
    <p>Email: <a href="mailto:${bookingRequest.email}">${bookingRequest.email}</a></p>
    <p>Address: ${bookingRequest.address}</p>
    <p>Service: ${bookingRequest.service}</p>
    <p>Attachments:</p>
    <ul>
      ${attachments}
    </ul>
  `;
}

export const NotificationEmailAdminIssue = (issue:Issue) => {
  const linkIcon = `${process.env.SPACES_URL}/${process.env.SPACES_FOLDER_NAME}/icons8-link-64.png`
  const linkIssue = `${process.env.WEB_APP_URL}/issues/${issue._id}`
  return `
    <h3>Hi you have new Issues, here is issue information:</h3>
    <p>
      <a style="vertical-align:middle" href="${linkIssue}"><img src="${linkIcon}"  width="30" height="30" alt="icon" /></a>
      <span>:</span>
      <a href="${linkIssue}">${linkIssue}</a>
    </p>
  `;
}