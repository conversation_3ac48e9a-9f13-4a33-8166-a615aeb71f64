import { Body, Controller, HttpCode, HttpStatus, Post, Req } from "@nestjs/common";
import { SendEmailService } from './send-email.service';
import { SendEmailDto } from './Dto/SendEmailDto';
import ROLE_RESOURCES from '../@core/constants/roles.resources';
import { SendEmailDtoGmail } from './Dto/SendEmailDtoGmail';
import { ApiTags } from '@nestjs/swagger';
import jwt from "jsonwebtoken";
import { UsersService } from "../users/users.service";
import { SendQuoteJobEmailDto } from "./Dto/send-quote-job-email.dto";

@Controller(ROLE_RESOURCES.SEND_EMAIL)
@ApiTags('Send-email')
export class SendEmailController {
  constructor(private readonly sendEmailService: SendEmailService, private readonly usersService: UsersService) {
  }

  @HttpCode(HttpStatus.OK)
  @Post('/')
  async sendEmail(@Body() sendEmailDto: SendEmailDto) {
    return await this.sendEmailService.sendEmail(sendEmailDto);
  }

  @HttpCode(HttpStatus.OK)
  @Post('/gmail')
  async sendEmailGmail(@Req() req: Request, @Body() sendEmailDtoGmail: SendEmailDtoGmail) {
    let workspace = '';
    //@ts-ignore
    const token = req.headers?.authorization;
    if (token) {
      const decoded = jwt.decode(token);
      const user = await this.usersService.findOne(decoded["id"]);
      workspace = user?.workspace?.toString();
    }
    return await this.sendEmailService.sendEmailGoogle(sendEmailDtoGmail, workspace);
  }

  @HttpCode(HttpStatus.OK)
  @Post('/quote-job-email')
  async sendQuoteJobEmail(@Body() sendEmailDtoGmail: SendQuoteJobEmailDto) {
    return await this.sendEmailService.sendQuoteJobEmail(sendEmailDtoGmail)
  }
}
