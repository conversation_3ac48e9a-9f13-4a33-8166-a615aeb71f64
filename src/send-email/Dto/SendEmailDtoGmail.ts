import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsNotEmpty, IsOptional, IsString } from "class-validator";


class EmailAttachment{
  @ApiProperty()
  @IsString()
  readonly filename: string;

  @ApiProperty()
  @IsNotEmpty()
  readonly content: Buffer;
}

export class SendEmailDtoGmail{

  @ApiProperty()
  @IsEmail()
  readonly toEmail: string;

  @ApiProperty()
  @IsString()
  readonly subject: string;

  @ApiProperty()
  @IsString()
  readonly body: string;

  @ApiProperty()
  @IsOptional()
  readonly attachments?: Array<EmailAttachment>;

  @ApiProperty()
  @IsOptional()
  readonly from?: string;

  @ApiProperty()
  @IsOptional()
  @IsEmail({}, { each: true })
  readonly cc?: string[];

}