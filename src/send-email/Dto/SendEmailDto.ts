import { ApiProperty } from '@nestjs/swagger';
import {IsString} from "class-validator";

export class SendEmailDto{

  @ApiProperty()
  @IsString()
  readonly fromEmailName: string;

  @ApiProperty()
  @IsString()
  readonly subject: string;

  @ApiProperty()
  @IsString()
  readonly body: string;

  @ApiProperty()
  @IsString()
  readonly userID: string;

  @ApiProperty()
  @IsString()
  readonly workspace: string;

}