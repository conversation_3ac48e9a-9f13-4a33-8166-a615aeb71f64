import { BadRequestException, forwardRef, HttpStatus, Inject, Injectable, NotFoundException } from "@nestjs/common";
import mongoose, { Model } from "mongoose";
import { InjectModel } from "@nestjs/mongoose";
import { SendEmail, SendEmailDocument } from "./send-email.schema";
import { emails, users } from "@clerk/clerk-sdk-node";
import { SendEmailDto } from "./Dto/SendEmailDto";
import { google } from "googleapis";
import { SendEmailDtoGmail } from "./Dto/SendEmailDtoGmail";
import { TypeTemplate } from "../@core/constants/enums";
import { readFile } from "node:fs/promises";
import * as path from "path";
import * as nodemailer from "nodemailer";
import { Options } from "nodemailer/lib/mailer";
import { SmtpConfigService } from "../smtp-config/smtp-config.service";
import { SendQuoteJobEmailDto } from "./Dto/send-quote-job-email.dto";
import { QuotesService } from "src/quotes/quotes.service";
import { JobsService } from "src/jobs/jobs.service";
import { AuthenticationService } from "src/iam/authentication/authentication.service";
import { WorkspaceService } from "src/workspace/workspace.service";
import { Quote_Email } from "src/quotes/templates/quote_email";
import { Job_Email } from "src/quotes/templates/job_email";

const MailComposer = require("nodemailer/lib/mail-composer");


@Injectable()
export class SendEmailService {
  constructor(
    @InjectModel(SendEmail.name) private sendEmailModel: Model<SendEmailDocument>,
    private readonly smtpConfig: SmtpConfigService,
    @Inject(forwardRef(() => AuthenticationService))
    private AuthenticationService: AuthenticationService,
    @Inject(forwardRef(() => WorkspaceService))
    private WorkspaceService: WorkspaceService,
    private readonly quotesService: QuotesService,
    private readonly jobsService: JobsService,
  ) {
  }

  async sendEmail(createSendEmail: SendEmailDto): Promise<any> {

    const userID = createSendEmail.userID;
    const user = await users.getUser(userID);
    const emailAddresses = user.primaryEmailAddressId;

    let emailLog = null;
    let statusCode = HttpStatus.OK;
    let message = "Email sent successfully";
    try {
      const email = await emails.createEmail({
        fromEmailName: createSendEmail.fromEmailName,
        subject: createSendEmail.subject,
        body: createSendEmail.body,
        emailAddressId: emailAddresses
      });
      emailLog = await this.sendEmailModel.create({
        fromEmailName: createSendEmail.fromEmailName,
        subject: createSendEmail.subject,
        body: createSendEmail.body,
        emailAddressesId: emailAddresses,
        status: "success",
        workspace: new mongoose.Types.ObjectId(createSendEmail.workspace),
        message: JSON.stringify(email),
        dateCreated: new Date()
      });
    } catch (e) {
      statusCode = HttpStatus.BAD_REQUEST;
      message = "Email sent failed";
      emailLog = e;
    }


    return {
      statusCode: statusCode,
      message,
      data: emailLog
    };
  }

  private async configGoogleService() {
    const credentialsPath = path.join(__dirname, "..", "..", "public", "google", "credentials.json");
    const tokenPath = path.join(__dirname, "..", "..", "public", "google", "token.json");
    const CONFIG = await Promise.all([readFile(credentialsPath, { encoding: "utf8" }), readFile(tokenPath, { encoding: "utf8" })]);
    let credentials = CONFIG[0];
    let token = CONFIG[1];
    const { client_secret, client_id, redirect_uris } = JSON.parse(credentials).web;

    const OAuth2 = google.auth.OAuth2;
    const oauth2Client = new OAuth2(client_id, client_secret, redirect_uris[0]);
    oauth2Client.setCredentials(JSON.parse(token));
    return google.gmail({ version: "v1", auth: oauth2Client });
  }

  private async configureSmtpTransport(smtpConfig: any) {
    return nodemailer.createTransport({
      host: smtpConfig.host,
      port: smtpConfig.port,
      secure: smtpConfig.secure || smtpConfig.port === 465, // true for 465, false for other ports
      auth: {
        user: smtpConfig.username,
        pass: smtpConfig.password,
      },
      tls: {
        rejectUnauthorized: false,
      },
    });
  }

  private async getSMTPConfig(workspace: string) {
    return await this.smtpConfig.findLatest(workspace);
  }

  encodeMessage = (message: Buffer) => {
    return Buffer.from(message).toString("base64").replace(/\+/g, "-").replace(/\//g, "_").replace(/=+$/, "");
  };

  createMail = async (options: Options) => {
    const mailComposer = new MailComposer(options);
    const message = await mailComposer.compile().build();
    return this.encodeMessage(message);
  };

  async sendEmailGoogle(sendEmailDtoGmail:SendEmailDtoGmail, workspace?: string): Promise<any> {
    const mailOptions = await this.generateMailOptions(sendEmailDtoGmail, process.env.EMAIL);
    if (workspace) {
      const smtpConfig = await this.getSMTPConfig(workspace);

      const newMailOptions = {
        ...mailOptions,
        from: smtpConfig?.fromEmail || sendEmailDtoGmail.from || process.env.EMAIL,
      }

      if (smtpConfig) {
        const transport = await this.configureSmtpTransport(smtpConfig);
        return await transport.sendMail(newMailOptions);
      }
    }

    const gmail = await this.configGoogleService();
    const rawMessage = await this.createMail({
      ...mailOptions,
      textEncoding: "base64",
    });
    return await gmail.users.messages.send(
      {
        userId: "me",
        requestBody: {
          raw: rawMessage,
        },
      }
    );
  }

  private async generateMailOptions(sendEmailDtoGmail: SendEmailDtoGmail, userEmail: string) {
    return {
      from: sendEmailDtoGmail.from ? sendEmailDtoGmail.from + '<' + userEmail + '>' : 'MaidProfit<' + userEmail + '>',
      to: sendEmailDtoGmail.toEmail,
      cc: sendEmailDtoGmail.cc,
      subject: sendEmailDtoGmail.subject,
      html: sendEmailDtoGmail.body,
      attachments: sendEmailDtoGmail?.attachments,
    };
  }

  async convertParamsInTemplate(template: string, params: { [key: string]: string},typeTemplate: TypeTemplate) {
      switch (typeTemplate) {
        case TypeTemplate.database:
          for (let key in params) {
            template = template.replace(`{{${key}}}`, params[key]);
          }
          break;
        case TypeTemplate.file:
          let filePath = template;
          let htmlContent = await readFile(filePath, { encoding: 'utf8' });

          for (let key in params) {
            htmlContent = htmlContent.replace(`{{${key}}}`, params[key]);
          }

          return htmlContent;
        default:
          return template;
      }
  }

  async sendQuoteJobEmail(sendEmailDtoGmail: SendQuoteJobEmailDto) {
    const { type, quoteId, workspace, jobId} = sendEmailDtoGmail;
    let emailData = null;
    let toEmail = '';
    let subject = '';
    let emailBody = '';
    let fromEmail = '';

    switch (type) {
      case 'quote': {
          emailData = await this.quotesService.findOne(quoteId, workspace);
          if (!emailData) throw new NotFoundException('Quote not found');
          if (!emailData.bookingRequest) throw new NotFoundException('Quote booking request not found');
          if (!emailData.bookingRequest.service) throw new NotFoundException('Quote service not found');
          if (!emailData.workspace) throw new NotFoundException('Quote workspace not found');

          toEmail = emailData.bookingRequest.email;
          subject = `Quote | ${emailData.bookingRequest.service?.name || 'N/A'} | ${emailData.workspace?.name || 'N/A'} | ${emailData.quoteNumber || 'N/A'}`;

          const genQuoteNumber = this.quotesService.genQuoteNumber(emailData, emailData._id);

          emailBody = await Quote_Email({
              data: emailData,
              token: await this.AuthenticationService.generateToken({ email: toEmail }),
              workspace: emailData.workspace,
              logo: emailData.workspace?.logo || '',
              quoteNumber: genQuoteNumber,
          });

          fromEmail = emailData.workspace?.name || 'System'

          break;
      }

      case 'job': {
          emailData = await this.jobsService.findOne(jobId, workspace);
          if (!emailData) throw new NotFoundException('Job not found');
          if (!emailData.bookingRequest) throw new NotFoundException('Job booking request not found');
          if (!emailData.bookingRequest.service) throw new NotFoundException('Job service not found');
          if (!emailData.workspace) throw new NotFoundException('Job workspace not found');

          const genJobNumber = this.jobsService.genJobNumber(emailData, emailData._id);

          toEmail = emailData.bookingRequest.email;
          subject = `Job | ${emailData.bookingRequest.service?.name || 'N/A'} | ${emailData.workspace?.name || 'N/A'} | ${genJobNumber}`;

          emailBody = await Job_Email({
              data: emailData,
              token: await this.AuthenticationService.generateToken({ email: toEmail }),
              workspace: emailData.workspace,
              logo: emailData.workspace?.logo || '',
              jobNumber: genJobNumber,
          });

          fromEmail = emailData.workspace?.name || 'System'

          break;
      }

      default:
          throw new BadRequestException('Invalid email type');
    }

    const result = await this.sendEmailGoogle({
        from: fromEmail,
        toEmail,
        body: emailBody,
        subject,
    }, workspace);

    if(result['status'] == 200) {
      return {
        status: result['status'],
        message: "Sent email successfully!"
      }
    } 

    return {
      status: 400,
      message: "Sent email failed!"
    }
  }
}
