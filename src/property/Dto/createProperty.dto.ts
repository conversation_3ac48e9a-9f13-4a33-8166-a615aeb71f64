import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsPositive, IsString } from 'class-validator';

class Address {
    @ApiProperty()
    @IsString()
    readonly street_one: string;

    @ApiProperty()
    @IsOptional()
    readonly street_two?: string;

    @ApiProperty()
    @IsString()
    readonly city: string;

    @ApiProperty()
    @IsString()
    readonly state: string;

    @ApiProperty()
    @IsOptional()
    readonly zip?: string;

    @ApiProperty()
    @IsNotEmpty()
    readonly coordinates: {
        lat: number;
        lng: number;
    };
}

export class CreatePropertyDto {
    @ApiProperty()
    @IsNotEmpty()
    readonly address: Address;

    @ApiProperty()
    @IsNotEmpty()
    readonly bathroom: number;

    @ApiProperty()
    @IsNotEmpty()
    readonly bedroom: number;

    @ApiProperty()
    @IsOptional()
    // @IsPositive()
    readonly half_bath: number;

    @ApiProperty()
    @IsNotEmpty()
    @IsPositive()
    readonly square_ft: number;

    @ApiProperty()
    @IsNotEmpty()
    readonly clientId: string;

    @ApiProperty()
    @IsOptional()
    readonly clientType: string;

    @ApiProperty()
    @IsOptional()
    readonly service?: string;
}
