import {
    ConflictException,
    forwardRef,
    HttpException,
    HttpStatus,
    Inject,
    Injectable,
} from '@nestjs/common';
import { Property } from './property.schema';
import mongoose, { Model } from 'mongoose';
import { InjectModel } from '@nestjs/mongoose';
import { CreatePropertyDto } from './Dto/createProperty.dto';
import { AddressService } from '../address/address.service';
import { UpdatePropertyDto } from './Dto/updateProperty.dto';
import { BookingTypesService } from '../booking-types/booking-types.service';
import { Address } from 'src/address/address.schema';

@Injectable()
export class PropertyService {
    constructor(
        @InjectModel(Property.name) private propertyModel: Model<Property>,
        @Inject(forwardRef(() => AddressService))
        private addressService: AddressService,
        private bookingTypeService: BookingTypesService,
        @InjectModel(Address.name) private addressModel: Model<Address>,
    ) {}

    async createProperty(
        property: CreatePropertyDto,
        workspace: string,
    ): Promise<Property> {
        let address = await this.findAddressByCoordinates({
            lng: property.address.coordinates.lng,
            lat: property.address.coordinates.lat,
        });
    
        if (!address) {
            address = await this.addressService.create({
                ...property.address,
                coordinates: [
                    property.address.coordinates.lng,
                    property.address.coordinates.lat,
                ],
            });
        }
    
        let serviceId = property.service
            ? new mongoose.Types.ObjectId(property.service)
            : undefined;
    
        const newProperty = new this.propertyModel({
            ...property,
            address: address._id,
            workspace: new mongoose.Types.ObjectId(workspace),
            clientType: property.clientType
                ? new mongoose.Types.ObjectId(property.clientType)
                : undefined,
            service: serviceId,
        });
    
        await newProperty.save();
    
        return this.propertyModel
            .findById(newProperty._id)
            .populate('clientType address workspace service')
            .exec();
    }

    async findAddressByCoordinates(
        coordinates: { lng: number; lat: number }
    ): Promise<Address | null> {
        return this.addressModel.findOne({
            'coordinates.0': coordinates.lng,
            'coordinates.1': coordinates.lat,
        }).exec();
    }

    async findPropertyByAddress(
        address: { street_one: string; city: string; state: string },
        workspace: string,
        clientId: string,
    ): Promise<Property> {
        const addressObj = await this.addressService.getAddressByStreet(
            address,
        );
        if (addressObj) {
            return this.propertyModel
                .findOne({
                    address: addressObj._id,
                    clientId: clientId,
                    workspace: new mongoose.Types.ObjectId(workspace),
                })
                .populate('address')
                .populate('clientType')
                .exec();
        }
        return null;
    }

    async createProperties(
        properties: CreatePropertyDto[],
        workspace: string,
    ): Promise<any> {
        return await Promise.all(
            properties.map(async (property) => {
                const createdProperty = await this.createProperty(
                    property,
                    workspace,
                );
                // Assuming createProperty handles the address object to string transformation internally
                // or you can add additional logic here if needed to transform the createdProperty
                const address = await this.addressService.getAddress(
                    createdProperty.address.id,
                );
                return {
                    ...createdProperty,
                    address:
                        address.street_one +
                        ', ' +
                        address.city +
                        ', ' +
                        address.state +
                        ' ' +
                        address.zip,
                };
            }),
        );
    }

    async getProperties(
        clientId: string,
        workspace: string,
    ): Promise<Property[]> {
        return await this.propertyModel
            .aggregate([
                {
                    $match: {
                        clientId: clientId,
                        workspace: new mongoose.Types.ObjectId(workspace),
                    },
                },
                {
                    $lookup: {
                        from: 'addresses', // Assuming 'addresses' is the collection name of Address documents
                        localField: 'address',
                        foreignField: '_id',
                        as: 'addressDetails',
                    },
                },
                {
                    $lookup: {
                        from: 'clienttypes',
                        localField: 'clientType',
                        foreignField: '_id',
                        as: 'clientType',
                    },
                },
                {
                    $lookup: {
                        from: 'bookingtypes',
                        localField: 'service',
                        foreignField: '_id',
                        as: 'service',
                    },
                },
                {
                    $unwind: '$addressDetails',
                },
                {
                    $addFields: {
                        address: {
                            $concat: [
                                '$addressDetails.street_one',
                                ', ',
                                '$addressDetails.city',
                                ', ',
                                '$addressDetails.state',
                                ' ',
                                '$addressDetails.zip',
                            ],
                        },
                    },
                },
                // {
                //     $project: {
                //         addressDetails: 0, // Exclude addressDetails from the final output
                //     },
                // },
            ])
            .exec();
    }

    async getProperty(id: string, workspace: string): Promise<Property> {
        const properties: Property[] = await this.propertyModel
            .aggregate([
                {
                    $match: {
                        _id: new mongoose.Types.ObjectId(id),
                        workspace: new mongoose.Types.ObjectId(workspace),
                    },
                },
                {
                    $lookup: {
                        from: 'addresses', // Assuming 'addresses' is the collection name of Address documents
                        localField: 'address',
                        foreignField: '_id',
                        as: 'addressDetails',
                    },
                },
                {
                    $lookup: {
                        from: 'clienttypes',
                        localField: 'clientType',
                        foreignField: '_id',
                        as: 'clientType',
                    },
                },
                {
                    $lookup: {
                        from: 'bookingtypes',
                        localField: 'service',
                        foreignField: '_id',
                        as: 'service',
                    },
                },
                {
                    $unwind: '$addressDetails',
                },
                {
                    $addFields: {
                        address: {
                            $concat: [
                                '$addressDetails.street_one',
                                ', ',
                                '$addressDetails.city',
                                ', ',
                                '$addressDetails.state',
                                ' ',
                                '$addressDetails.zip',
                            ],
                        },
                    },
                },
                // {
                //     $project: {
                //         addressDetails: 0, // Exclude addressDetails from the final output
                //     },
                // },
            ])
            .exec();

        if (properties.length === 0) {
            throw new HttpException('Property not found', HttpStatus.NOT_FOUND);
        }

        return properties[0];
    }

    async updateProperty(
        id: string,
        property: UpdatePropertyDto,
        workspace: string,
    ): Promise<Property> {
        let serviceId = property.service
            ? new mongoose.Types.ObjectId(property.service)
            : undefined;

        if (property.service && property.clientType) {
            const service = await this.bookingTypeService.getBookingType(
                property.service,
            );
            // @ts-ignore
            if (service.clientType._id.toString() !== property.clientType) {
                throw new HttpException(
                    'Service and Client Type do not match',
                    HttpStatus.BAD_REQUEST,
                );
            }
        }

        if (property.address && property.clientType) {
            const address = await this.addressService.create({
                ...property.address,
                coordinates: [
                    property.address.coordinates.lng,
                    property.address.coordinates.lat,
                ],
            });

            return this.propertyModel.findOneAndUpdate(
                {
                    _id: id,
                    workspace: new mongoose.Types.ObjectId(workspace),
                },
                {
                    ...property,
                    address: address._id,
                    service: serviceId,
                    clientType: new mongoose.Types.ObjectId(
                        property.clientType,
                    ),
                },
                {
                    new: true,
                },
            );
        }

        if (property.address) {
            const address = await this.addressService.create({
                ...property.address,
                coordinates: [
                    property.address.coordinates.lng,
                    property.address.coordinates.lat,
                ],
            });

            return this.propertyModel.findOneAndUpdate(
                {
                    _id: id,
                    workspace: new mongoose.Types.ObjectId(workspace),
                },
                {
                    ...property,
                    service: serviceId,
                    address: address._id,
                },
                {
                    new: true,
                },
            );
        }

        return this.propertyModel.findOneAndUpdate(
            {
                _id: id,
                workspace: new mongoose.Types.ObjectId(workspace),
            },
            {
                ...property,
                service: serviceId,
            },
            {
                new: true,
            },
        );
    }

    async deleteProperty(id: string, workspace: string): Promise<any> {
        return this.propertyModel.findOneAndDelete({
            _id: id,
            workspace: new mongoose.Types.ObjectId(workspace),
        });
    }
}
