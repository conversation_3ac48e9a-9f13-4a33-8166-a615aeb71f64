import { forwardRef, Module } from '@nestjs/common';
import { PropertyService } from './property.service';
import { MongooseModule } from '@nestjs/mongoose';
import { Property, PropertySchema } from './property.schema';
import { UsersModule } from '../users/users.module';
import { ClientsModule } from '../clients/clients.module';
import { PropertyController } from './property.controller';
import { AddressModule } from '../address/address.module';
import { BookingTypesModule } from "../booking-types/booking-types.module";
import { Address, AddressSchema } from 'src/address/address.schema';

@Module({
  imports: [
    MongooseModule.forFeature([
        { name: Property.name, schema: PropertySchema },
        { name: Address.name, schema: AddressSchema }
    ]),
    forwardRef(() => UsersModule),
    forwardRef(() => ClientsModule),
    forwardRef(()=> AddressModule),
    forwardRef(()=> BookingTypesModule)
  ],
  providers: [PropertyService],
  exports: [PropertyService],
  controllers: [PropertyController]
})
export class PropertyModule {}
