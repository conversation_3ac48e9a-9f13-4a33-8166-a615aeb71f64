import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema, Types } from 'mongoose';
import { Workspace } from '../workspace/workspace.schema';
import { Type } from 'class-transformer';
import { Address } from '../address/address.schema';
import { ClientTypes } from '../client-type/client-type.schema';
import { BookingTypes } from "../booking-types/booking-types.schema";

@Schema()
export class Property extends Document {

    _id: MongooseSchema.Types.ObjectId;

    @Prop({ type: Types.ObjectId, ref: Address.name, required: true})
    address: Address;

    @Prop({ required: true })
    bathroom: number;

    @Prop({ required: true })
    bedroom: number;

    @Prop({ required: true })
    half_bath: number;

    @Prop({ required: true })
    square_ft: number;

    // Client Data ID
    @Prop({ required: true})
    clientId: string;

    @Prop({ required: false, type: Types.ObjectId, ref: ClientTypes.name })
    @Type(() => ClientTypes)
    clientType: ClientTypes;

    @Prop({ default: Date.now })
    createdAt: Date;

    @Prop({ required: true, type: Types.ObjectId, ref: Workspace.name })
    @Type(() => Workspace)
    workspace: Workspace;

    @Prop({ required: false, type: Types.ObjectId, ref: BookingTypes.name })
    @Type(() => BookingTypes)
    service: BookingTypes;
}

export const PropertySchema = SchemaFactory.createForClass(Property);