import {
    Body,
    Controller,
    Delete,
    Get,
    HttpCode,
    HttpStatus,
    Param,
    Patch,
    Post,
    Query,
    Req,
    UseGuards,
} from '@nestjs/common';
import { PropertyService } from './property.service';
import { CreatePropertyDto } from './Dto/createProperty.dto';
import { Property } from './property.schema';
import ROLE_RESOURCES from '../@core/constants/roles.resources';
import { AuthGuard } from '../auth/auth.guard';
import { ACGuard, UseRoles } from 'nest-access-control';
import { UpdatePropertyDto } from "./Dto/updateProperty.dto";

@Controller(ROLE_RESOURCES.PROPERTY)
export class PropertyController {
    constructor(
        private propertyService: PropertyService
    ) {}


    @HttpCode(HttpStatus.OK)
    @UseGuards(AuthGuard, ACGuard)
    @UseRoles({
        resource: ROLE_RESOURCES.PROPERTY,
        action: 'read',
        possession: 'any',
    })
    @Get('/')
    async getProperties(
        @Req () req: Request,
        @Query('clientId') clientId: string,
    ): Promise<{
        status: number,
        message: string,
        data: Property[]
    }> {
        //@ts-ignore
        const workspace = req.user.workspace;

        return {
            status: HttpStatus.OK,
            message: 'Properties fetched successfully',
            data: await this.propertyService.getProperties(clientId,workspace)
        };
    }

    @HttpCode(HttpStatus.CREATED)
    @UseGuards(AuthGuard, ACGuard)
    @UseRoles({
        resource: ROLE_RESOURCES.PROPERTY,
        action: 'create',
        possession: 'any',
    })
    @Post('/')
    async createProperty(
        @Req () req: Request,
        @Body() property: CreatePropertyDto[]
    ): Promise<{
        status: number,
        message: string,
        data: Property[]
    }> {
        //@ts-ignore
        const workspace = req.user.workspace;

        const properties = await this.propertyService.createProperties(property,workspace)

        if(properties.length === 0){
            return {
                status: HttpStatus.CREATED,
                message: 'Property is already created',
                data: properties
            };
        }else{
            return {
                status: HttpStatus.CREATED,
                message: 'Property created successfully',
                data: properties
            };
        }
    }

    @HttpCode(HttpStatus.OK)
    @UseGuards(AuthGuard, ACGuard)
    @UseRoles({
        resource: ROLE_RESOURCES.PROPERTY,
        action: 'read',
        possession: 'any',
    })
    @Get(':id')
    async getProperty(
        @Req () req: Request,
        @Param('id') id: string,
    ): Promise<{
        status: number,
        message: string,
        data: Property
    }> {

        //@ts-ignore
        const workspace = req.user.workspace;

        return {
            status: HttpStatus.OK,
            message: 'Property fetched successfully',
            data: await this.propertyService.getProperty(id,workspace)
        };
    }

    @HttpCode(HttpStatus.OK)
    @UseGuards(AuthGuard, ACGuard)
    @UseRoles({
        resource: ROLE_RESOURCES.PROPERTY,
        action: 'update',
        possession: 'any',
    })
    @Patch(':id')
    async updateProperty(
        @Param('id') id: string,
        @Req () req: Request,
        @Body() property: UpdatePropertyDto
    ): Promise<{
        status: number,
        message: string,
        data: Property
    }> {
        //@ts-ignore
        const workspace = req.user.workspace;

        return {
            status: HttpStatus.OK,
            message: 'Property updated successfully',
            data: await this.propertyService.updateProperty(id,property,workspace)
        };
    }


    @HttpCode(HttpStatus.OK)
    @UseGuards(AuthGuard, ACGuard)
    @UseRoles({
        resource: ROLE_RESOURCES.PROPERTY,
        action: 'delete',
        possession: 'any',
    })
    @Delete(':id')
    async deleteProperty(
        @Req () req: Request,
        @Param('id') id: string,
    ): Promise<{
        status: number,
        message: string,
        data: Property
    }> {
        //@ts-ignore
        const workspace = req.user.workspace;
        return {
            status: HttpStatus.OK,
            message: 'Property deleted successfully',
            data: await this.propertyService.deleteProperty(id,workspace)
        };
    }
}
