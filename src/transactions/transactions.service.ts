import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Transaction, TransactionDocument } from './transactions.schema';
import { Model } from 'mongoose';
import { CreateTransactionDto } from './dto/createTransaction.dto';

/**
 * TransactionsService is a class that provides methods for creating and retrieving transactions.
 */
@Injectable()
export class TransactionsService {
  constructor(
    @InjectModel(Transaction.name)
    private readonly transactionModel: Model<TransactionDocument>,
  ) {}

  /**
   * Creates a new transaction.
   *
   * @param {CreateTransactionDto} data - The transaction data.
   * @return {Promise<Transaction>} A promise that resolves to the created transaction.
   */
  async create(data: CreateTransactionDto): Promise<TransactionDocument> {
    console.log('create', data);
    const newTransaction = new this.transactionModel(data);
    return newTransaction.save();
  }

  /**
   * Finds all transactions with pagination options.
   *
   * @param {number} [page=1] - The page number.
   * @param {number} [limit=10] - The maximum number of transactions to return per page.
   * @param {Object} [sort={ dateCreated: 'desc' }] - The sort options for the transactions.
   * @returns {Promise<{
   *   data: Transaction[];
   *   pagination: {
   *     pageSize: number;
   *     pageNum: number;
   *     totalItems: number;
   *     totalPages: number;
   *   };
   * }>} - A Promise that resolves to an object containing the transactions array
   * and pagination details.
   */
  async findAll(
    page: number = 1,
    limit: number = 10,
    sort: { [key: string]: 'asc' | 'desc' } = { dateCreated: 'desc' },
  ): Promise<{
    data: Transaction[];
    pagination: {
      pageSize: number;
      pageNum: number;
      totalItems: number;
      totalPages: number;
    };
  }> {
    const totalItems = await this.transactionModel.countDocuments();
    const totalPages = Math.ceil(totalItems / limit);
    const transactions = await this.transactionModel
      .find()
      .skip((page - 1) * limit)
      .limit(limit)
      .sort(sort)
      .exec();

    return {
      data: transactions,
      pagination: {
        pageSize: limit,
        pageNum: page,
        totalItems,
        totalPages,
      },
    };
  }

  async getDetail(id: string): Promise<Transaction> {
    return this.transactionModel.findById(id).exec();
  }

  async getDetailByInvoice(invoice: string): Promise<Transaction> {
    return this.transactionModel.findOne({ invoice }).exec();
  }
}
