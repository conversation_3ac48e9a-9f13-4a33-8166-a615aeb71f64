import {
  Body,
  Controller,
  Get,
  HttpCode,
  HttpException,
  HttpStatus,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { TransactionsService } from './transactions.service';
import ROLE_RESOURCES from '../@core/constants/roles.resources';
import { AuthGuard } from '../auth/auth.guard';
import { ACGuard, UseRoles } from 'nest-access-control';
import { CreateTransactionDto } from './dto/createTransaction.dto';
import { ApiTags } from '@nestjs/swagger';

@Controller(ROLE_RESOURCES.TRANSACTION)
@ApiTags('Transaction')
export class TransactionsController {
  constructor(private readonly transactionsService: TransactionsService) {}

  /**
   * Retrieves all transactions.
   *
   * @returns {Promise<Array<Transaction>>} A Promise that resolves to an array of transactions.
   */
  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @Get('/')
  async findAll(
    @Query('page') page: number,
    @Query('limit') limit: number,
    @Query('sort') sort: string,
  ) {
    let sorter: { [x: string]: 'desc' | 'asc' };
    try {
      sorter = JSON.parse(sort);
    } catch (e) {
      sorter = { dateCreated: 'desc' };
    }

    page = page || 1;
    limit = limit || 10;

    return this.transactionsService.findAll(page, limit, sorter);
  }

  /**
   * Find a transaction by ID
   *
   * @param {string} id - The ID of the transaction to find
   * @returns {Object} - The found transaction
   * @throws {HttpException} - If the ID is not provided, or if the transaction is not found
   */
  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @Get('/:id')
  async findOne(id: string) {
    if (!id)
      throw new HttpException(
        'Transaction ID is required',
        HttpStatus.BAD_REQUEST,
      );

    const transaction = await this.transactionsService.getDetail(id);

    if (!transaction)
      throw new HttpException('Transaction is not found', HttpStatus.NOT_FOUND);

    return {
      message: 'Get transaction success',
      data: transaction,
      status: HttpStatus.OK,
    };
  }

  /**
   * Creates a new transaction.
   *
   * @param {CreateTransactionDto} data - The data for creating the transaction.
   *
   * @returns {Object} - The newly created transaction.
   * @httpcode 200 - OK
   * @guard AuthGuard, ACGuard
   */
  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @Post('/')
  async create(@Body() data: CreateTransactionDto) {
    const transaction = await this.transactionsService.create(data);

    return {
      message: 'Create transaction success',
      data: transaction,
      status: HttpStatus.OK,
    };
  }

  /**
   * Finds a transaction by invoice.
   *
   * @param {string} invoice - The invoice number of the transaction.
   *
   * @returns {Promise<{ message: string, data: any, status: HttpStatus }>} Returns an object with a success message, transaction data, and HTTP status code.
   *
   * @throws {HttpException} Throws an exception with a error message and HTTP status code if the invoice is not provided.
   * @throws {HttpException} Throws an exception with a error message and HTTP status code if the transaction is not found.
   *
   * @HttpCode(HttpStatus.OK)
   * @UseGuards(AuthGuard, ACGuard)
   * @Get('/invoice/:invoice')
   */
  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @Get('/invoice/:invoice')
  async findByInvoice(invoice: string) {
    if (!invoice)
      throw new HttpException('Invoice is required', HttpStatus.BAD_REQUEST);

    const transaction = await this.transactionsService.getDetailByInvoice(
      invoice,
    );

    if (!transaction)
      throw new HttpException('Transaction is not found', HttpStatus.NOT_FOUND);

    return {
      message: 'Get transaction success',
      data: transaction,
      status: HttpStatus.OK,
    };
  }
}
