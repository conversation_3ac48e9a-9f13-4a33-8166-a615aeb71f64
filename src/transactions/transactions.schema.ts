import { Schem<PERSON>, <PERSON>p, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema, Types } from 'mongoose';
import { Invoices } from '../invoices/invoices.schema';
import { Workspace } from 'src/workspace/workspace.schema';

export type TransactionDocument = Transaction & Document;

export const TransactionTypes = {
  STRIPE: 'STRIPE',
  OTHER: 'OTHER',
} as const;

export type TransactionType =
  (typeof TransactionTypes)[keyof typeof TransactionTypes];

/**
 * Represents a transaction.
 * @module Transaction
 */
@Schema()
export class Transaction {
  @Prop({ type: MongooseSchema.Types.ObjectId, auto: true })
  _id: Types.ObjectId;

  @Prop({ required: true })
  transactionID: string;

  @Prop({ type: Types.ObjectId, ref: Invoices.name, required: true })
  invoice: Invoices;

  static readonly DEFAULT_TRANSACTION_TYPE = TransactionTypes.STRIPE;
  @Prop({
    required: true,
    enum: TransactionTypes,
    default: Transaction.DEFAULT_TRANSACTION_TYPE,
  })
  transactionType: TransactionType;

  @Prop({ required: true, type: Types.ObjectId, ref: Workspace.name })
  workspace: Workspace;

  @Prop({ default: Date.now })
  dateCreated: Date;
}

/**
 * Represents the schema definition for the Transaction object.
 *
 * @class
 * @memberof module:database
 * @property {Schema} TransactionSchema - The schema definition for the Transaction object.
 */
export const TransactionSchema = SchemaFactory.createForClass(Transaction);
