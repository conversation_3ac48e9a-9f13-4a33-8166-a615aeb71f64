import { Injectable } from '@nestjs/common';
import { BookingTimeArrival, BookingTimeArrivalDocument } from "./booking-time-arrival.schema";
import { InjectModel } from "@nestjs/mongoose";
import mongoose, { Model } from "mongoose";
import BookingTimeArrivalDto from "./Dto/BookingTimeArrivalDto";
import UpdateBookingTimeArrivalDto from "./Dto/UpdateBookingTimeArrivalDto";

@Injectable()
export class BookingTimeArrivalService {
    constructor(
        @InjectModel(BookingTimeArrival.name) private bookingTimeArrivalModel: Model<BookingTimeArrivalDocument>,
    ) {}

    async findAll(workspace:string): Promise<BookingTimeArrival[]> {
        return this.bookingTimeArrivalModel.find({
            workspace: workspace
        }).exec();
    }

    async findOne(id: string, workspace:string): Promise<BookingTimeArrival> {
        return this.bookingTimeArrivalModel.findOne({
            _id: new mongoose.Types.ObjectId(id),
            workspace: new mongoose.Types.ObjectId(workspace)
        }).exec();
    }

    async create(bookingTimeArrival: BookingTimeArrivalDto): Promise<BookingTimeArrival> {

        let checkExist = await this.bookingTimeArrivalModel.findOne({
            ...bookingTimeArrival,
        })

        if(checkExist){
            return checkExist;
        }else{
            return this.bookingTimeArrivalModel.create({
                ...bookingTimeArrival
            });
        }

    }

    async update(id: string, bookingTimeArrival: UpdateBookingTimeArrivalDto): Promise<BookingTimeArrival> {
        return this.bookingTimeArrivalModel.findByIdAndUpdate(id, {
            ...bookingTimeArrival,
        }).exec();
    }

    async delete(id: string): Promise<BookingTimeArrival> {
        return this.bookingTimeArrivalModel.findOneAndRemove({
            _id: new mongoose.Types.ObjectId(id)
        }).exec();
    }
}
