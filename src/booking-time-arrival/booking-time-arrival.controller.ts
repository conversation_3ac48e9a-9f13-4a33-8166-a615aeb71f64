import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
} from '@nestjs/common';
import { BookingTimeArrivalService } from './booking-time-arrival.service';
import BookingTimeArrivalDto from './Dto/BookingTimeArrivalDto';
import { Patch } from '@nestjs/common/decorators/http/request-mapping.decorator';
import UpdateBookingTimeArrivalDto from './Dto/UpdateBookingTimeArrivalDto';
import ROLE_RESOURCES from '../@core/constants/roles.resources';
import { ApiTags } from '@nestjs/swagger';

@Controller(ROLE_RESOURCES.BOOKING_TIME_ARRIVAL)
@ApiTags('Booking-time-arrival')
export class BookingTimeArrivalController {
  constructor(
    private readonly bookingTimeArrivalService: BookingTimeArrivalService,
  ) {}

  @HttpCode(HttpStatus.OK)
  @Get('/')
  async findAll(@Body('workspace') workspace: string) {
    return {
      statusCode: HttpStatus.OK,
      message: 'Data retrieved successfully',
      data: await this.bookingTimeArrivalService.findAll(workspace),
    };
  }

  @HttpCode(HttpStatus.OK)
  @Get('/:id')
  async findOne(@Param('id') id: string, @Body('workspace') workspace: string) {
    if (!id) {
      return {
        statusCode: HttpStatus.BAD_REQUEST,
        message: 'Please provide an ID',
      };
    }

    const data = await this.bookingTimeArrivalService.findOne(id, workspace);

    if (!data) {
      return {
        statusCode: HttpStatus.NOT_FOUND,
        message: 'Data not found',
      };
    }

    return {
      statusCode: HttpStatus.OK,
      message: 'Data retrieved successfully',
      data,
    };
  }

  @HttpCode(HttpStatus.OK)
  @Post('/')
  async create(@Body() bookingTimeArrival: BookingTimeArrivalDto) {
    const dataCreated = await this.bookingTimeArrivalService.create(
      bookingTimeArrival,
    );
    if (!dataCreated) {
      return {
        statusCode: HttpStatus.BAD_REQUEST,
        message: 'Data not created',
      };
    }

    return {
      statusCode: HttpStatus.OK,
      message: 'Data created successfully',
      data: dataCreated,
    };
  }

  @HttpCode(HttpStatus.OK)
  @Patch('/:id')
  async update(
    @Param('id') id: string,
    @Body() bookingTimeArrival: UpdateBookingTimeArrivalDto,
  ) {
    if (!id) {
      return {
        statusCode: HttpStatus.BAD_REQUEST,
        message: 'Please provide an ID',
      };
    }

    const dataUpdated = await this.bookingTimeArrivalService.update(
      id,
      bookingTimeArrival,
    );

    if (!dataUpdated) {
      return {
        statusCode: HttpStatus.NOT_FOUND,
        message: 'Data not found',
      };
    }

    return {
      statusCode: HttpStatus.OK,
      message: 'Data updated successfully',
    };
  }

  @HttpCode(HttpStatus.OK)
  @Delete('/:id')
  async delete(@Param('id') id: string, @Body('workspace') workspace: string) {
    if (!id) {
      return {
        statusCode: HttpStatus.BAD_REQUEST,
        message: 'Please provide an ID',
      };
    }

    const dataDeleted = await this.bookingTimeArrivalService.delete(
      id,
    );

    if (!dataDeleted) {
      return {
        statusCode: HttpStatus.NOT_FOUND,
        message: 'Data not found',
      };
    }

    return {
      statusCode: HttpStatus.OK,
      message: 'Data deleted successfully',
    };
  }
}
