import { <PERSON><PERSON>, Schema, SchemaFactory } from '@nestjs/mongoose'
import { Document, Schema as MongooseSchema, Types } from "mongoose";
import { Workspace } from "../workspace/workspace.schema";
import { Type } from "class-transformer";

@Schema()
export class BookingTimeArrival{
    _id: MongooseSchema.Types.ObjectId;

    @Prop({ required: true })
    timeStart: number;

    @Prop({ required: true })
    timeEnd: number;

}

export type BookingTimeArrivalDocument = BookingTimeArrival & Document

export const BookingTimeArrivalSchema = SchemaFactory.createForClass(BookingTimeArrival)