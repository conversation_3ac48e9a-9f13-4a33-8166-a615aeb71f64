import { Module, forwardRef } from '@nestjs/common';
import { BookingTimeArrivalService } from './booking-time-arrival.service';
import { BookingTimeArrivalController } from './booking-time-arrival.controller';
import { MongooseModule } from '@nestjs/mongoose';
import {
    BookingTimeArrival,
    BookingTimeArrivalSchema,
} from './booking-time-arrival.schema';
import { UsersModule } from '../users/users.module';
import { ClientsModule } from '../clients/clients.module';

@Module({
    imports: [
        forwardRef(() => ClientsModule),
        forwardRef(() => UsersModule),
        MongooseModule.forFeature([
            { name: BookingTimeArrival.name, schema: BookingTimeArrivalSchema },
        ]),
    ],
    exports: [BookingTimeArrivalService],
    providers: [BookingTimeArrivalService],
    controllers: [BookingTimeArrivalController],
})
export class BookingTimeArrivalModule {}
