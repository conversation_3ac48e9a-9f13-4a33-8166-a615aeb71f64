import { Module, forwardRef } from '@nestjs/common';
import { ClientsDataService } from './clients-data.service';
import { MongooseModule } from '@nestjs/mongoose';
import { ClientData, ClientsDataSchema } from './client-data.schema';
import { AddressModule } from '../address/address.module';
import { Address, AddressSchema } from '../address/address.schema';

@Module({
    imports: [
        MongooseModule.forFeature([
            { name: ClientData.name, schema: ClientsDataSchema },
        ]),
        MongooseModule.forFeature([
            { name: Address.name, schema: AddressSchema },
        ]),
        forwardRef(() => AddressModule),
    ],
    providers: [ClientsDataService],
    exports: [ClientsDataService],
})
export class ClientsDataModule {}
