import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import mongoose, { Model } from 'mongoose';
import { ClientData, ClientsDataDocument } from './client-data.schema';
import { InjectModel } from '@nestjs/mongoose';
import CreateClientDataDto from './dto/createClientData.dto';
import UpdateClientDataDto from './dto/updateClientData.dto';
import { AddressService } from '../address/address.service';
import { GetWorkspaceDtoDto } from '../clients/dto/getWorkspaceDto.dto';
import { GetWorkspaceDto } from '../clients/dto/getClientWorkspace.dto';
import { Address } from '../address/address.schema';
import { DeleteResult } from 'mongodb';

@Injectable()
export class ClientsDataService {
  constructor(
    @InjectModel(ClientData.name) private clientDataModel: Model<ClientsDataDocument>,
    @InjectModel(Address.name) private addressModel: Model<Address>,
    private readonly addressService: AddressService
  ) {}

  async getClientsByWorkspace(getWorkspaceDto: GetWorkspaceDto): Promise<any> {
    const {workspace, pageSize,pageNum} = getWorkspaceDto;
    const totalCount = await this.clientDataModel.countDocuments({workspace: workspace});
    const ObjectQuery ={
        workspace: new mongoose.Types.ObjectId(workspace)
    }
    const clients = await this.clientDataModel.find(ObjectQuery)
      .populate(['address','workspace','client'])
      .sort({createdAt: -1, updatedAt: -1})
      .skip(pageSize * (pageNum - 1))
      .limit(pageSize)
    return {
      clients: clients,
      pagination: {
        pageSize: pageSize,
        pageNum: pageNum,
        totalItems: totalCount,
        totalPages: Math.ceil(totalCount / pageSize)
      }
    };
  }

  async getNearestClientsByWorkspace(workspace: string,lat: number,lng: number): Promise<any> {
    if (!workspace || !this.validateCoordinates(lng, lat) || !this.validateGeoJSONPoint([lng, lat])) {
      throw new HttpException('Invalid input', HttpStatus.BAD_REQUEST);
    }
    const addresses = await this.addressModel.aggregate([
      {
        $geoNear: {
          near: { type: "Point", coordinates: [ lng , lat ] },
          distanceField: "dist.calculated",
          maxDistance: 100000,
          spherical: true
        }
      }
    ]);
    return this.clientDataModel.find({
      workspace: workspace,
      address: { $in: addresses.map(a => a._id) }
    }).select(['address','firstName','lastName','clientType']).populate(['address','clientType']);
  }

  async searchClientsByWorkspace(getWorkspaceDto: GetWorkspaceDto): Promise<any> {
    const {workspace, pageSize,pageNum, query} = getWorkspaceDto;
    let filter:object = { workspace: workspace };
    if (query) filter = { ...filter, $or: [
        { "phone": { $elemMatch: { "item": { $regex: query, $options: 'i' }, "type": "main" } } },
        { "email": { $elemMatch: { "item": { $regex: query, $options: 'i' }, "type": "main" } } },
        { "firstName": { $regex: query, $options: 'i' } },
        { "lastName": { $regex: query, $options: 'i' } }
      ]};
    const totalCount = await this.clientDataModel.countDocuments(filter);
    const clients = await this.clientDataModel.find(filter)
      .populate(['address','workspace','clientType','client'])
      .select(['-client.password'])
      .sort({createdAt: -1, updatedAt: -1})
      .skip(pageSize * (pageNum - 1))
      .limit(pageSize)
    return {
      clients: clients,
      pagination: {
        pageSize: pageSize,
        pageNum: pageNum,
        totalItems: totalCount,
        totalPages: Math.ceil(totalCount / pageSize)
      }
    };
  }

  async getWorkspaceClients(data: GetWorkspaceDtoDto): Promise<any> {
    const {client, pageSize,pageNum} = data;
    const totalCount = await this.clientDataModel.countDocuments({client: client});
    const clients = await this.clientDataModel.find({client: client})
        .populate(['address','workspace'])
        .select(['-client.password'])
        .sort({createdAt: -1, updatedAt: -1})
        .skip(pageSize * (pageNum - 1))
        .limit(pageSize)
    return {
      clients: clients,
      pagination: {
        pageSize: pageSize,
        pageNum: pageNum,
        totalItems: totalCount,
        totalPages: Math.ceil(totalCount / pageSize)
      }
    }
  }

  async create(data: CreateClientDataDto): Promise<ClientData> {
    const existingData = await this.clientDataModel.findOne({ client: data.client, workspace: data.workspace });
    if (existingData) {
      throw new HttpException('Client already exist', HttpStatus.CONFLICT);
    }

    const newClientData = new this.clientDataModel({
      ...data,
      workspace: new mongoose.Types.ObjectId(data.workspace),
      createdAt: new Date(),
      updatedAt: new Date(),
    });
    return newClientData.save();
  }

  async findDataByClientAndWorkspace(clientId: string,workspace: string): Promise<ClientData> {
    const clientData = await this.clientDataModel
        .findOne({ client: clientId, workspace: new mongoose.Types.ObjectId(workspace) })
        .populate(['address','clientType'])
        .select(['-client.password']);
    if(!clientData) throw new HttpException('Client data not found', HttpStatus.NOT_FOUND);
    return clientData;
  }

  async update(data: UpdateClientDataDto,workspace:string): Promise<ClientData> {
    const clientData = await this.clientDataModel.findOne({
      client: new mongoose.Types.ObjectId(data.client),
      workspace: new mongoose.Types.ObjectId(workspace)
    })
    if(!clientData) throw new HttpException('Client data not found', HttpStatus.NOT_FOUND);


    function toObjectId(value) {
      if (Array.isArray(value)) {
        return value.map(v => new mongoose.Types.ObjectId(v));
      }
      return new mongoose.Types.ObjectId(value);
    }

    const objectUpdate = JSON.parse(JSON.stringify(data));

    [ 'workspace', 'client'].forEach(prop => {
      if (objectUpdate[prop]) {
        objectUpdate[prop] = toObjectId(objectUpdate[prop]);
      }
    });

    return this.clientDataModel.findByIdAndUpdate(clientData._id, {
      ...data,
      updatedAt: new Date(),
    }, { new: true }).exec();
  }

  async delete(clientId: string, workspace: string): Promise<DeleteResult> {
    return await this.clientDataModel.deleteOne({
      client: new mongoose.Types.ObjectId(clientId),
      workspace: new mongoose.Types.ObjectId(workspace)
    }, { new: true }).exec()
  }

  validateCoordinates(lng: number, lat: number): boolean {
    return !(lng < -180 || lng > 180 || lat < -90 || lat > 90);
  }

  validateGeoJSONPoint(coordinates: any): boolean {
    return Array.isArray(coordinates) && coordinates.length === 2 && typeof coordinates[0] === 'number' && typeof coordinates[1] === 'number';
  }
}