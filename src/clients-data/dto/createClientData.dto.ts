import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty, IsOptional, IsString, ValidateNested } from "class-validator";
import { Type } from "class-transformer";
import CreateAddressDto from "../../address/dto/createAddressDto.dto";


class Phone {
  @ApiProperty()
  @IsString()
  readonly type: string;

  @ApiProperty()
  @IsString()
  readonly item: string;
}

class Email {
  @ApiProperty()
  @IsString()
  readonly type: string;

  @ApiProperty()
  @IsString()
  readonly item: string;
}

export default class CreateClientDataDto {

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  readonly firstName: string;

  @ApiProperty()
  @IsNotEmpty()
  readonly lastName: string;

  @ApiProperty()
  @IsNotEmpty()
  readonly client: string;

  @ApiProperty()
  @ValidateNested({ each: true })
  @Type(() => Phone)
  readonly phone: Phone[];

  @ApiProperty()
  @ValidateNested({ each: true })
  @Type(() => Email)
  readonly email: Email[];

  @ApiProperty()
  @IsString()
  readonly workspace: string;
}