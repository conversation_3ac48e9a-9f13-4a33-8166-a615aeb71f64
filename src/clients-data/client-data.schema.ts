import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, SchemaFactory } from "@nestjs/mongoose";
import mongoose, { Document, Schema as MongooseSchema, Types } from "mongoose";
import { ClientTypes } from "../client-type/client-type.schema";
import { Type } from "class-transformer";
import { Workspace } from "../workspace/workspace.schema";
import { Clients } from "../clients/clients.schema";
import { Address } from "../address/address.schema";

@Schema()
export class ClientData {
  _id: MongooseSchema.Types.ObjectId;

  @Prop({ required: true })
  firstName: string;

  @Prop({ required: true })
  lastName: string;

  @Prop()
  phone: Array<{ type: string; item: string }>;

  @Prop()
  email: Array<{ type: string; item: string }>;

  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: Clients.name })
  @Type(()=> Clients )
  client: Clients;

  @Prop({ type: [{ type: mongoose.Schema.Types.ObjectId, ref: ClientTypes.name }] })
  @Type(()=> ClientTypes )
  clientType: ClientTypes[];

  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: Address.name })
  @Type(()=> Address )
  address: Address;

  @Prop({ required: true })
  createdAt: Date;

  /**
   * Represents the date and time when the entity was last updated.
   *
   * @typedef {Date} updatedAt
   */
  @Prop({ required: true })
  updatedAt: Date;

  @Prop({ required: true, type: Types.ObjectId, ref: Workspace.name })
  @Type(() => Workspace)
  workspace: Workspace;
}

export type ClientsDataDocument = ClientData & Document;
const ClientsDataSchema = SchemaFactory.createForClass(ClientData);

ClientsDataSchema.index({ firstName: 'text', lastName: 'text', "phone.item": "text", "email.item": "text", "address.coordinates": "2dsphere"})

export { ClientsDataSchema }