import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from "mongoose";
import { Workspace } from "../workspace/workspace.schema";

@Schema({ timestamps: true })
export class FrequencyDiscount {
  @Prop({ required: true })
  frequency: 'once' | 'daily' | 'weekly' | 'monthly' | 'yearly';

  @Prop({ required: false, default: 0 })
  interval?: number;

  @Prop({ required: true })
  discount: number;

  @Prop({ required: true, type: Types.ObjectId, ref: Workspace.name })
  workspace: Workspace;
}

export type FrequencyDiscountDocument = FrequencyDiscount & Document;

export const FrequencyDiscountSchema = SchemaFactory.createForClass(FrequencyDiscount);