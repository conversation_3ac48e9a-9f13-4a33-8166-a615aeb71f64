import { Test, TestingModule } from '@nestjs/testing';
import { FrequencyDiscountController } from './frequency-discount.controller';
import { FrequencyDiscountService } from './frequency-discount.service';

describe('FrequencyDiscountController', () => {
  let controller: FrequencyDiscountController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [FrequencyDiscountController],
      providers: [FrequencyDiscountService],
    }).compile();

    controller = module.get<FrequencyDiscountController>(FrequencyDiscountController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
