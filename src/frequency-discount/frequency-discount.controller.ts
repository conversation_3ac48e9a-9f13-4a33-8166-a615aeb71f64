import { ApiTags } from "@nestjs/swagger";
import { Controller, Get, Post, Body, Patch, Param, Delete, HttpStatus, UseGuards, Req } from "@nestjs/common";
import { FrequencyDiscountService } from './frequency-discount.service';
import { CreateFrequencyDiscountDto } from './dto/create-frequency-discount.dto';
import { UpdateFrequencyDiscountDto } from './dto/update-frequency-discount.dto';
import ROLE_RESOURCES from "../@core/constants/roles.resources";
import { AuthGuard } from "../auth/auth.guard";
import { ACGuard, UseRoles } from "nest-access-control";

@Controller(ROLE_RESOURCES.FREQUENCY_DISCOUNT)
@ApiTags('Frequency-discount')
export class FrequencyDiscountController {
  constructor(private readonly frequencyDiscountService: FrequencyDiscountService) {}

  @Post()
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.FREQUENCY_DISCOUNT,
    action: 'create',
    possession: 'any',
  })
  async create(@Req() req: Request, @Body() createFrequencyDiscountDto: CreateFrequencyDiscountDto) {
    //@ts-ignore
    const workspace = req.user.workspace;
    return {
      statusCode: HttpStatus.OK,
      data: await this.frequencyDiscountService.create({
        ...createFrequencyDiscountDto,
        workspace,
      }),
      message: 'Frequency discount created successfully',
    };
  }

  @Get()
  async findAll() {
    return {
      statusCode: HttpStatus.OK,
      data: await this.frequencyDiscountService.findAll(),
      message: 'Frequency discount found successfully',
    };
  }

  @Get(':id')
  async findOne(@Param('id') id: string) {
    return {
      statusCode: HttpStatus.OK,
      data: await this.frequencyDiscountService.findOne(id),
      message: 'Frequency discount detail found successfully',
    }
  }

  @Patch(':id')
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.FREQUENCY_DISCOUNT,
    action: 'update',
    possession: 'any',
  })
  async update(@Req() req: Request, @Param("id") id: string, @Body() updateFrequencyDiscountDto: UpdateFrequencyDiscountDto) {
    //@ts-ignore
    const workspace = req.user.workspace;
    return {
      statusCode: HttpStatus.OK,
      data: await this.frequencyDiscountService.update(id, {
        ...updateFrequencyDiscountDto,
        workspace,
      }),
      message: 'Frequency discount updated successfully',
    }
  }

  @Delete(':id')
  async remove(@Param('id') id: string) {
    return {
      statusCode: HttpStatus.OK,
      data: await this.frequencyDiscountService.remove(id),
      message: 'Frequency discount removed successfully',
    }
  }
}
