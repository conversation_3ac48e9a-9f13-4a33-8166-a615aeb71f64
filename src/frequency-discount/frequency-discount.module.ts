import { forwardRef, <PERSON><PERSON><PERSON> } from "@nestjs/common";
import { FrequencyDiscountService } from './frequency-discount.service';
import { FrequencyDiscountController } from './frequency-discount.controller';
import { MongooseModule } from "@nestjs/mongoose";
import { FrequencyDiscount, FrequencyDiscountSchema } from "./frequency-discount.schema";
import { UsersModule } from "../users/users.module";
import { ClientsModule } from "../clients/clients.module";

@Module({
  imports: [
    forwardRef(() => ClientsModule),
    forwardRef(() => UsersModule),
    MongooseModule.forFeature([
      { name: FrequencyDiscount.name, schema: FrequencyDiscountSchema },
    ]),
  ],
  controllers: [FrequencyDiscountController],
  providers: [FrequencyDiscountService],
  exports: [FrequencyDiscountService]
})
export class FrequencyDiscountModule {}
