import { Injectable } from '@nestjs/common';
import { CreateFrequencyDiscountDto } from './dto/create-frequency-discount.dto';
import { UpdateFrequencyDiscountDto } from './dto/update-frequency-discount.dto';
import { FrequencyDiscount, FrequencyDiscountDocument } from "./frequency-discount.schema";
import mongoose, { Model } from "mongoose";
import { InjectModel } from "@nestjs/mongoose";

@Injectable()
export class FrequencyDiscountService {
  constructor(
    @InjectModel(FrequencyDiscount.name)
    private frequencyDiscountModel: Model<FrequencyDiscountDocument>,
  ) {
  }

  create(createFrequencyDiscountDto: CreateFrequencyDiscountDto) {
    return this.frequencyDiscountModel.create({
      ...createFrequencyDiscountDto,
      workspace: new mongoose.Types.ObjectId(createFrequencyDiscountDto.workspace)
    });
  }

  createMany(createFrequencyDiscountDto: CreateFrequencyDiscountDto[]) {
    return Promise.all(createFrequencyDiscountDto.map(async (frequencyDiscount) => {
      return await this.frequencyDiscountModel.create({
        ...frequencyDiscount,
        workspace: new mongoose.Types.ObjectId(frequencyDiscount.workspace),
      });
    }))
  }

  findAll() {
    return this.frequencyDiscountModel.find();
  }

  findOne(id: string) {
    return this.frequencyDiscountModel.findById(id);
  }

  update(id: string, updateFrequencyDiscountDto: UpdateFrequencyDiscountDto) {
    return this.frequencyDiscountModel.findByIdAndUpdate(id, {
      ...updateFrequencyDiscountDto,
      workspace: new mongoose.Types.ObjectId(updateFrequencyDiscountDto.workspace),
    }, { new: true });
  }

  remove(id: string) {
    return this.frequencyDiscountModel.findByIdAndDelete(id);
  }
}
