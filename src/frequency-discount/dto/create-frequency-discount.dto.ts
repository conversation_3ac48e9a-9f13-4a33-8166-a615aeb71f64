import { ApiProperty } from "@nestjs/swagger";
import { IsEnum, IsNumber, IsOptional, IsPositive } from "class-validator";

export class CreateFrequencyDiscountDto {
  @ApiProperty()
  @IsEnum(['once', 'daily', 'weekly', 'monthly', 'yearly'])
  frequency: 'once' | 'daily' | 'weekly' | 'monthly' | 'yearly';

  @ApiProperty()
  @IsNumber()
  discount: number;

  @ApiProperty()
  @IsPositive()
  @IsOptional()
  interval?: number;

  @ApiProperty({ required: false, readOnly: true })
  workspace?: string;
}
