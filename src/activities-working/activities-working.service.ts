import { Injectable } from '@nestjs/common';
import mongoose, { Model } from "mongoose";
import { InjectModel } from "@nestjs/mongoose";
import { ActivitiesWorking, ActivitiesWorkingDocument } from './activities-working.schema';
import { CollectionDto, DocumentCollector } from "@forlagshuset/nestjs-mongoose-paginate";

@Injectable()
export class ActivitiesWorkingService {
    constructor(
        @InjectModel(ActivitiesWorking.name) private activitiesWorkingModel: Model<ActivitiesWorkingDocument>,
    ) {}

    async findAll(collectionDto: CollectionDto): Promise<any> {
        const collector = new DocumentCollector<ActivitiesWorkingDocument>(this.activitiesWorkingModel)
        return collector.find(collectionDto)
    }

    async findOne(id: string): Promise<ActivitiesWorking> {
        let item = await this.activitiesWorkingModel.findById(id).populate('userId')
        if (!item) {
            throw new Error('Data Activities Working not existed');
        }
        return item
    }

    async delete(id: string,workspace:string): Promise<ActivitiesWorking> {
        return this.activitiesWorkingModel.findOneAndRemove({
            _id: new mongoose.Types.ObjectId(id),
            workspace: new mongoose.Types.ObjectId(workspace)
        })
    }
}
