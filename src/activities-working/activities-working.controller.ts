import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpException,
  HttpStatus,
  Param,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ActivitiesWorkingService } from './activities-working.service';
import {
  CollectionDto,
  CollectionResponse,
} from '@forlagshuset/nestjs-mongoose-paginate';
import { ActivitiesWorking } from './activities-working.schema';
import mongoose from 'mongoose';
import { AuthGuard } from '../auth/auth.guard';
import { ACGuard, UseRoles } from 'nest-access-control';
import ROLE_RESOURCES from '../@core/constants/roles.resources';
import { ApiTags } from '@nestjs/swagger';

@Controller(ROLE_RESOURCES.ACTIVITIES_WORKING)
@ApiTags('Activities-working')
export class ActivitiesWorkingController {
  constructor(private activitiesWorkingService: ActivitiesWorkingService) {}

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.ACTIVITIES_WORKING,
    action: 'read',
    possession: 'any',
  })
  @Get('/')
  async getListActivitiesWorking(
    @Query()
    collectionDto: CollectionDto,
    @Body('workspace') workspace: string,
  ): Promise<CollectionResponse<ActivitiesWorking>> {
    collectionDto.filter['workspace'] = {
      $eq: new mongoose.Types.ObjectId(workspace),
    };
    return this.activitiesWorkingService.findAll(collectionDto);
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.ACTIVITIES_WORKING,
    action: 'read',
    possession: 'any',
  })
  @Get(':id')
  async getActivitiesWorkingById(
    @Param('id') id: string,
    @Body('workspace') workspace: string,
  ) {
    let activitiesWorking = await this.activitiesWorkingService.findOne(id);
    if (!activitiesWorking) {
      throw new HttpException(
        'Data Activities Working not existed',
        HttpStatus.BAD_REQUEST,
      );
    }
    if (activitiesWorking.workspace.toString() !== workspace) {
      throw new HttpException(
        'Data Activities Working not existed',
        HttpStatus.BAD_REQUEST,
      );
    }
    return {
      statusCode: HttpStatus.OK,
      message: 'Get Activities Working success',
      data: activitiesWorking,
    };
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.ACTIVITIES_WORKING,
    action: 'delete',
    possession: 'any',
  })
  @Delete('/:id')
  async deleteActivitiesWorking(
    @Param('id') id: string,
    @Body('workspace') workspace: string,
  ) {
    let itemDeleted = await this.activitiesWorkingService.delete(id, workspace);
    if (!itemDeleted) {
      throw new HttpException(
        'Can not delete Activities Working',
        HttpStatus.BAD_REQUEST,
      );
    }

    return {
      statusCode: HttpStatus.OK,
      message: 'Delete Activities Working successfully',
      data: itemDeleted,
    };
  }
}
