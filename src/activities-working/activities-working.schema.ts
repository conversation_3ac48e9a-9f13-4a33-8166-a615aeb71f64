import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose, { Document, Schema as MongooseSchema, Types } from "mongoose";
import { Users } from "../users/users.schema";
import { Type } from "class-transformer";
import { Workspace } from "../workspace/workspace.schema";

export enum ACTION {
    join = 'join',
    finish = 'finish',
}

@Schema()
export class ActivitiesWorking {
  _id: MongooseSchema.Types.ObjectId;

  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: Users.name })
  @Type(() => Users)
  userId: Users;

  @Prop({ required: true })
  jobId: string;

  @Prop({ default: ACTION.join, enum: ACTION })
  action: string;

  @Prop({ required: true, type: Types.ObjectId, ref: Workspace.name })
  @Type(() => Workspace)
  workspace: Workspace;

  @Prop({ default: Date.now })
  dateCreated: Date;
}

export type ActivitiesWorkingDocument = ActivitiesWorking & Document

export const ActivitiesWorkingSchema = SchemaFactory.createForClass(ActivitiesWorking)