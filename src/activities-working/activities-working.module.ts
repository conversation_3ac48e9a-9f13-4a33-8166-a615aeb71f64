import { Module, forwardRef } from '@nestjs/common';
import { ActivitiesWorkingController } from './activities-working.controller';
import { ActivitiesWorkingService } from './activities-working.service';
import { MongooseModule } from '@nestjs/mongoose';
import {
    ActivitiesWorking,
    ActivitiesWorkingSchema,
} from './activities-working.schema';
import { UsersModule } from '../users/users.module';
import { ClientsModule } from '../clients/clients.module';

@Module({
    imports: [
        forwardRef(() => ClientsModule),
        forwardRef(() => UsersModule),
        MongooseModule.forFeature([
            { name: ActivitiesWorking.name, schema: ActivitiesWorkingSchema },
        ]),
    ],
    controllers: [ActivitiesWorkingController],
    providers: [ActivitiesWorkingService],
    exports: [ActivitiesWorkingService],
})
export class ActivitiesWorkingModule {}
