import { CallHand<PERSON>, ExecutionContext, Injectable, NestInterceptor } from '@nestjs/common';
import { map, Observable } from 'rxjs';
import Stripe from 'stripe';
import { Packages, Period } from './packages.schema';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY)
@Injectable()
export class PackagesInterceptor implements NestInterceptor {
    intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
        return next.handle()
            .pipe(map(async (data) => {
                let result = data.data;

                result = await Promise.all(result.map(async (item: Packages) => {

                    const prices = await Promise.all([
                        stripe.products.retrieve(item.baseProductStripeID),
                        stripe.products.retrieve(item.memberProductStripeID)
                    ])

                    return {
                        ...item.toObject(),
                        baseStripePriceId: item.period === Period.MONTHLY ? prices[0].default_price : parseFloat(prices[0].default_price as string) / 12,
                        memberStripePriceId: item.period === Period.MONTHLY ? prices[1].default_price : parseFloat(prices[1].default_price as string) / 12,
                    }
                }));
                return { ...data, data: result }
            }));
    }
}
