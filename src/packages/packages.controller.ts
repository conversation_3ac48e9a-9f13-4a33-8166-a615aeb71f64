import {
    Body,
    Controller,
    Delete,
    Get,
    HttpCode,
    HttpStatus,
    Param,
    Patch,
    Post, Query,
    UseInterceptors,
} from '@nestjs/common';
import { PackagesService } from './packages.service';
import CreatePackageDto from './DTO/CreatePackage.dto';
import { Packages, Period } from './packages.schema';
import { UseRoles } from 'nest-access-control';
import ROLE_RESOURCES from '../@core/constants/roles.resources';
import { PackagesInterceptor } from './packages.interceptor';

@Controller(ROLE_RESOURCES.PACKAGE)
export class PackagesController {
    constructor(
        private readonly packagesService: PackagesService
    ) {}


    @HttpCode(HttpStatus.CREATED)
    @Post('/')
    @UseRoles({
        resource: ROLE_RESOURCES.PACKAGE,
        action: 'create',
        possession: 'any',
    })
    async createPackage(
        @Body() data: CreatePackageDto
    ): Promise<{
        message: string;
        data: Packages;
        statusCode: number;
    }> {
        let item: Packages;

        try {
            item = await this.packagesService.createPackage(data);
        }catch (error) {
            throw new Error(`Error: ${error.message}`);
        }

        return {
            message: 'Package created successfully',
            data: item,
            statusCode: HttpStatus.CREATED
        };
    }

    @HttpCode(HttpStatus.OK)
    @Get('/')
    @UseInterceptors(PackagesInterceptor)
    async getAllPackages(
        @Query('type') typePackage: 'office_management' | 'field_operations' | 'customer_experience',
        @Query('period') period: Period
    ): Promise<{
        message: string;
        data: Packages[];
        statusCode: number;
    }> {
        let items: Packages[];

        try {
            items = await this.packagesService.getAllPackages(typePackage,period);
        }catch (error) {
            throw new Error(`Error: ${error.message}`);
        }

        return {
            message: 'Packages retrieved successfully',
            data: items,
            statusCode: HttpStatus.OK
        };
    }

    @HttpCode(HttpStatus.OK)
    @Get('/:id')
    @UseRoles({
        resource: ROLE_RESOURCES.PACKAGE,
        action: 'read',
        possession: 'any',
    })
    async getPackageById(
        @Param('id') id: string
    ): Promise<{
        message: string;
        data: Packages;
        statusCode: number;
    }> {
        return {
            message: 'Package retrieved successfully',
            data: await this.packagesService.getPackageById(id),
            statusCode: HttpStatus.OK
        }
    }

    @HttpCode(HttpStatus.OK)
    @Patch('/:id')
    @UseRoles({
        resource: ROLE_RESOURCES.PACKAGE,
        action: 'update',
        possession: 'any',
    })
    async updatePackage(
        @Param('id') id: string,
        @Body() data: CreatePackageDto
    ): Promise<{
        message: string;
        data: Packages;
        statusCode: number;
    }> {
        return {
            message: 'Package updated successfully',
            data: await this.packagesService.updatePackage(id, data),
            statusCode: HttpStatus.OK
        }
    }

    @HttpCode(HttpStatus.OK)
    @Delete('/:id')
    @UseRoles({
        resource: ROLE_RESOURCES.PACKAGE,
        action: 'delete',
        possession: 'any',
    })
    async deletePackage(
        @Param('id') id: string
    ): Promise<{
        message: string;
        data: Packages;
        statusCode: number;
    }> {
        return {
            message: 'Package deleted successfully',
            data: await this.packagesService.deletePackage(id),
            statusCode: HttpStatus.OK
        }
    }

}
