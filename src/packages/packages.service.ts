import Stripe from 'stripe';
import { HttpException, Injectable } from '@nestjs/common';
import { Packages, Period } from './packages.schema';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import CreatePackageDto from './DTO/CreatePackage.dto';
import UpdatePackageDto from './DTO/UpdatePackage.dto';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY)

@Injectable()
export class PackagesService {
    constructor(
        @InjectModel(Packages.name) private readonly packagesModel: Model<Packages>
    ) {
    }

    async findById(id: string): Promise<Packages> {
        return this.packagesModel.findById(id);
    }

    async createProduct(data: CreatePackageDto): Promise<{
        baseProduct: Stripe.Product;
        memberProduct: Stripe.Product;
    }> {
        let interval: Stripe.PriceCreateParams.Recurring.Interval;
        switch (data.period) {
            case 'monthly':
                interval = 'month';
                break;
            case 'yearly':
                interval = 'year';
                break;
            default:
                throw new Error(`Invalid period: ${data.period}`);
        }
        return {
            baseProduct: await stripe.products.create({
                name: data.name,
                default_price_data:{
                    currency: 'usd',
                    recurring: {
                        interval: interval,
                    },
                    unit_amount_decimal: (data.basePrice * 100).toString()
                }
            }),
            memberProduct: await stripe.products.create({
                name: data.name + ' (Member)',
                unit_label: 'Members',
                default_price_data:{
                    currency: 'usd',
                    recurring: {
                        interval: interval,
                    },
                    unit_amount_decimal: (data.priceMember* 100).toString()
                }
            }),
        };
    }

    async updateBaseProduct(baseProductStripeID:string,data: UpdatePackageDto): Promise<void> {
        const product = await stripe.products.retrieve(baseProductStripeID);

        const priceID = product.default_price;

        await stripe.prices.update(priceID as string, {
            currency_options:{
                usd:{
                    unit_amount_decimal: (data.basePrice * 100).toString()
                }
            }
        })

        await stripe.products.update(baseProductStripeID, {
            name: data.name
        })
    }

    async updateMemberProduct(memberProductStripeID:string,data: UpdatePackageDto): Promise<void> {
        const product = await stripe.products.retrieve(memberProductStripeID);

        const priceID = product.default_price;

        await stripe.prices.update(priceID as string, {
            currency_options:{
                usd:{
                    unit_amount_decimal: (data.priceMember*100).toString()
                }
            }
        })

        await stripe.products.update(memberProductStripeID, {
            name: data.name + ' (Member)'
        })
    }

    async createPackage(data: CreatePackageDto): Promise<Packages> {

        const { baseProduct, memberProduct } = await this.createProduct(data);

        return await this.packagesModel.create({...data, baseProductStripeID: baseProduct.id, memberProductStripeID: memberProduct.id});
    }

    async getAllPackages(
        typePackage: 'office_management' | 'field_operations' | 'customer_experience',
        period: Period
                         ): Promise<Packages[]> {
        let query = { period };

        if (typePackage === 'office_management') {
            query['name'] = { $regex: 'Office Management', $options: 'i' };
        } else if (typePackage === 'field_operations') {
            query['name'] = { $regex: 'Field Operations', $options: 'i' };
        } else if (typePackage === 'customer_experience') {
            query['name'] = { $regex: 'Customer Experience', $options: 'i' };
        }

        return await this.packagesModel.find(query).select(['-grants','-createdAt','-__v']).exec();
    }

    async getPackageById(id: string): Promise<Packages> {
        return await this.packagesModel.findById(id).select(['-grants','-createdAt','-__v']).exec();
    }

    async getPackageByProductId(productId: string): Promise<Packages> {
        return await this.packagesModel.findOne({
            $or: [{ baseProductStripeID: productId }, { memberProductStripeID: productId }]
        }).select(["-grants", "-createdAt", "-__v"]).exec();
    }

    async updatePackage(id: string, data: UpdatePackageDto): Promise<Packages> {
        const packageData = await this.getPackageById(id);

        if (data.name || data.basePrice) {
            await this.updateBaseProduct(packageData.baseProductStripeID, data);
        }

        if (data.name || data.priceMember) {
            await this.updateMemberProduct(packageData.memberProductStripeID, data);
        }

        if (data.period){
            throw new HttpException('Period cannot be updated', 400)
        }

        return await this.packagesModel.findByIdAndUpdate(id, data, {
            new: true,
            runValidators: true
        }).select(['-grants','-createdAt','-__v']).exec();
    }

    async deletePackage(id: string): Promise<Packages> {
        const product = await this.getPackageById(id);

        await stripe.products.del(product.baseProductStripeID);
        await stripe.products.del(product.memberProductStripeID);

       return await this.packagesModel.findByIdAndDelete(id).exec();
    }
}
