import { IsBoolean, IsEnum, IsNotEmpty, IsPositive, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Period } from '../packages.schema';

export default class CreatePackageDto
{
    @ApiProperty()
    @IsString()
    @IsNotEmpty()
    readonly name: string;

    @ApiProperty()
    @IsNotEmpty()
    @IsPositive()
    readonly basePrice: number;

    @ApiProperty()
    @IsNotEmpty()
    @IsPositive()
    readonly priceMember: number;

    @ApiProperty()
    @IsNotEmpty()
    @IsBoolean()
    readonly isPopular: boolean;

    @ApiProperty()
    @IsNotEmpty()
    @IsEnum(Period)
    readonly period: string;

    @ApiProperty()
    @IsNotEmpty()
    @IsString({each: true})
    readonly features: string[];

    @ApiProperty()
    @IsNotEmpty()
    @IsString({each: true})
    readonly grants: string[];
}