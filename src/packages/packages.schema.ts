import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema, Types } from 'mongoose';
import { Grants } from '../grants/grants.schema';


export enum Period {
    MONTHLY = 'monthly',
    YEARLY = 'yearly'
}

@Schema()
export class Packages extends Document{
    _id: MongooseSchema.Types.ObjectId;

    @Prop({ required: true })
    name: string;

    @Prop({ required: true })
    basePrice: number;

    @Prop({ required: true })
    priceMember: number;

    @Prop({ required: true })
    baseProductStripeID: string;

    @Prop({ required: true })
    memberProductStripeID: string;

    @Prop({ required: false, default: false})
    isPopular: boolean;

    @Prop({ required: true })
    features: string[];

    @Prop({ required: true, type: Types.ObjectId, ref:Grants.name })
    grants: Grants[];

    @Prop({ required: true, default: Period.MONTHLY, enum: Period })
    period: Period;

    @Prop({ required: false, default: new Date()})
    createdAt: Date;
}

export const PackagesSchema = SchemaFactory.createForClass(Packages);