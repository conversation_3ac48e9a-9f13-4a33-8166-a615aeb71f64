import {
  Body,
  Controller,
  Get,
  Delete,
  HttpCode,
  HttpException,
  HttpStatus,
  Param,
  Post,
  Query,
  Patch,
  BadRequestException,
  UseInterceptors,
  UseGuards,
} from '@nestjs/common';
import {
  CollectionDto,
  CollectionResponse,
} from '@forlagshuset/nestjs-mongoose-paginate';
import { InvoicesService } from './invoices.service';
import { Invoices } from './invoices.schema';
import CreateInvoiceDto from './Dto/CreateInvoiceDto';
import UpdateInvoiceDto from './Dto/UpdateInvoiceDto';
import mongoose from 'mongoose';
import { AuthGuard } from '../auth/auth.guard';
import { ACGuard, UseRoles } from 'nest-access-control';
import ROLE_RESOURCES from '../@core/constants/roles.resources';
import { ApiTags } from '@nestjs/swagger';
import { InvoicesInterceptor } from "./invoices.interceptor";

@Controller(ROLE_RESOURCES.INVOICES)
@ApiTags('Invoices')
export class InvoicesController {
  constructor(private invoicesService: InvoicesService) {}

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseInterceptors(InvoicesInterceptor)
  @UseRoles({
    resource: ROLE_RESOURCES.INVOICES,
    action: 'read',
    possession: 'any',
  })
  @Get('/')
  async getInvoices(
    @Query()
    collectionDto: CollectionDto,
    @Body('workspace') workspace: string,
  ): Promise<CollectionResponse<Invoices>> {
    if (!collectionDto?.filter) {
      collectionDto = { ...collectionDto, filter: {} };
    }
    collectionDto.filter['workspace'] = {
      $eq: new mongoose.Types.ObjectId(workspace),
    };
    return this.invoicesService.findAll(collectionDto);
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.INVOICES,
    action: 'read',
    possession: 'any',
  })
  @Get('/clients/:id')
  async getInvoicesByClientId(
    @Query()
    collectionDto: CollectionDto,
    @Param('id') id: string,
    @Body('workspace') workspace: string,
  ): Promise<CollectionResponse<Invoices>> {
    return this.invoicesService.findAllByClientId(id, workspace, collectionDto);
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseInterceptors(InvoicesInterceptor)
  @UseRoles({
    resource: ROLE_RESOURCES.INVOICES,
    action: 'read',
    possession: 'any',
  })
  @Get(':id')
  async getInvoice(
    @Param('id') id: string,
    @Body('workspace') workspace: string,
  ) {
    let quote = await this.invoicesService.findOne(id, workspace);
    if (!quote) {
      throw new HttpException('Invoice is not existed', HttpStatus.BAD_REQUEST);
    }
    return {
      statusCode: HttpStatus.OK,
      message: 'Get invoice success',
      data: quote,
    };
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseInterceptors(InvoicesInterceptor)
  @UseRoles({
    resource: ROLE_RESOURCES.INVOICES,
    action: 'create',
    possession: 'any',
  })
  @Post('/')
  async createInvoice(@Body() invoice: CreateInvoiceDto) {
    let itemCreated = await this.invoicesService.create(invoice);
    if (!itemCreated) {
      throw new HttpException('Can not create Invoice', HttpStatus.BAD_REQUEST);
    }
    return {
      statusCode: HttpStatus.OK,
      message: 'Create invoice successfully',
      data: itemCreated,
    };
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseInterceptors(InvoicesInterceptor)
  @UseRoles({
    resource: ROLE_RESOURCES.INVOICES,
    action: 'create',
    possession: 'any',
  })
  @Post('/schedule')
  async createSchedule(@Body() invoice: CreateInvoiceDto) {
    const itemCreated = await this.invoicesService.createSchedule(invoice);
    if (!itemCreated) {
      throw new HttpException('Failed to schedule Invoice', HttpStatus.BAD_REQUEST);
    }

    // Populate job data
    await itemCreated.populate({
      path: 'job',
      populate: [
        { path: "bookingRequest", populate: { path: "address" } },
        { path: "service" },
        { path: "addons", populate: { path: "addon" } },
        { path: "extras", populate: { path: "extra" } },
        { path: "service", populate: { path: "clientType" } },
        { path: "frequency", populate: { path: "discountFrequency" } },
        { path: "staffMedias", populate: { path: "medias" } },
        { path: "staffs" }
      ]
    });

    return {
      statusCode: HttpStatus.OK,
      message: 'Invoice scheduling successful',
      data: itemCreated,
    };
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.INVOICES,
    action: 'update',
    possession: 'any',
  })
  @Patch('/:id')
  async updateInvoice(
    @Param('id') id: string,
    @Body() invoice: UpdateInvoiceDto,
  ) {
    try {
      let itemUpdated = await this.invoicesService.update(id, invoice);
      if (!itemUpdated) {
        throw new HttpException(
          'Can not update Invoice',
          HttpStatus.BAD_REQUEST,
        );
      }

      return {
        statusCode: HttpStatus.OK,
        message: 'Update invoice successfully',
        data: itemUpdated,
      };
    } catch (err) {
      throw new BadRequestException(err.message);
    }
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.INVOICES,
    action: 'delete',
    possession: 'any',
  })
  @Delete('/:id')
  async deleteInvoice(
    @Param('id') id: string,
    @Body('workspace') workspace: string,
  ) {
    let itemDeleted = await this.invoicesService.delete(id, workspace);
    if (!itemDeleted) {
      throw new HttpException('Can not delete Invoice', HttpStatus.BAD_REQUEST);
    }

    return {
      statusCode: HttpStatus.OK,
      message: 'Delete invoice successfully',
      data: itemDeleted,
    };
  }
}
