import { <PERSON>p, Schem<PERSON>, <PERSON>hemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema, Types } from 'mongoose';
import { Workspace } from "../workspace/workspace.schema";
import { Type } from "class-transformer";
import { Medias } from "../medias/medias.schema";
import { Users } from "../users/users.schema";
import { Jobs } from "../jobs/jobs.schema";

export enum EInvoiceSchedulingCondition {
  EACH_JOB_VISIT = 'each_job_visit'
}

export enum EInvoiceSchedulingTiming {
  ON_JOB_COMPLETE = 'on_job_complete',
  BY_DATE = 'by_date',
}

@Schema()
export class Invoices {
  _id: MongooseSchema.Types.ObjectId;

  @Prop({ type: String })
  invoiceNumber?: string;

  @Prop({ required: true })
  dateCreated: Date;

  @Prop({ default: 'waiting', type: String, enum: ['waiting', 'overdue', 'paid'] })
  status: string;

  @Prop({ required: true, type: Types.ObjectId, ref: Workspace.name })
  @Type(() => Workspace)
  workspace: Workspace;

  @Prop({required: true, default: 0})
  price: number;

  @Prop({required: true, default: 0})
  hours: number;

  @Prop({required: false, type: Types.ObjectId, ref: Medias.name })
  invoiceFile: string;

  @Prop({ required: false, default: null })
  discountCode: string;

  @Prop({ required: true, type: MongooseSchema.Types.ObjectId, ref: Jobs.name })
  job: Jobs;

  @Prop({ required: false, default: [], type: [String] })
  emailList?: string[];

  @Prop({ default: EInvoiceSchedulingCondition.EACH_JOB_VISIT, type: String, enum: EInvoiceSchedulingCondition })
  scheduleInvoiceCondition: string;

  @Prop({ default: EInvoiceSchedulingTiming.ON_JOB_COMPLETE, type: String, enum: EInvoiceSchedulingTiming })
  scheduleInvoiceTiming: string;

  @Prop({ required: false, default: false })
  scheduleSent: boolean;

  @Prop({ required: false, type: Date })
  scheduledSentAt?: Date;

  @Prop({ required: false, default: null })
  scheduleInvoiceDate?: string;

  @Prop({ default: 0 })
  tipAmount: number;
}

export type InvoicesDocument = Invoices & Document;

export const InvoicesSchema = SchemaFactory.createForClass(Invoices);
