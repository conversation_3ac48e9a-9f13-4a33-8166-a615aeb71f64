import { BadRequestException, forwardRef, Inject, Injectable } from '@nestjs/common';
import { EInvoiceSchedulingTiming, Invoices, InvoicesDocument } from './invoices.schema';
import { GeneratePdfService } from '../generate-pdf/generate-pdf.service';
import { SendEmailService } from '../send-email/send-email.service';
import { InvoiceReceived } from './templates/invoice-received';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import * as process from 'process';
import { JobsService } from 'src/jobs/jobs.service';
import Redis from 'ioredis';
import { RedisService } from 'src/redis/redis.service';
import { Cron, CronExpression } from '@nestjs/schedule';
import { MediasService } from 'src/medias/medias.service';
import { MediasDocument } from 'src/medias/medias.schema';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';

dayjs.extend(utc);

@Injectable()
export class InvoiceSchedulerService {
    private redisClient: Redis;

    constructor(
        @Inject(forwardRef(() => JobsService))
        private jobsService: JobsService,
        @Inject(forwardRef(() => GeneratePdfService))
        private generatePdfService: GeneratePdfService,
        @Inject(forwardRef(() => SendEmailService))
        private sendEmailService: SendEmailService,
        @Inject(forwardRef(() => RedisService))
        private readonly redisService: RedisService,
        @Inject(forwardRef(() => MediasService))
        private MediasService: MediasService,
        @InjectModel(Invoices.name)
        private invoicesModel: Model<InvoicesDocument>,
    ) {
        this.redisClient = this.redisService.getClient('COMMON_CACHE_NAME');
    }

    async scheduleInvoice(invoice: InvoicesDocument) {
        if (invoice.scheduleInvoiceTiming === EInvoiceSchedulingTiming.BY_DATE) {
            await this.redisClient.sadd('scheduled_by_date_invoices', JSON.stringify(invoice));
        } else if (invoice.scheduleInvoiceTiming === EInvoiceSchedulingTiming.ON_JOB_COMPLETE) {
            await this.redisClient.sadd('on_complete_invoices', JSON.stringify(invoice));
        }
    }

    async processInvoice(invoice: InvoicesDocument) {
        const jobInvoice = await this.jobsService.findOne(invoice?.job?._id.toString(), invoice?.workspace?._id.toString());
        if (!jobInvoice) {
            throw new BadRequestException(
                `Job is not existed`,
            );
        }

        const groupedStaffMedias = jobInvoice.staffMedias.reduce((acc,staffMedia) => {
            if (!acc[staffMedia.staff.toString()]) {
                acc[staffMedia.staff.toString()] = { before: [], after: [] };
            }
            acc[staffMedia.staff.toString()][staffMedia.beforeAfter].push(...staffMedia.medias);
            return acc;
        }, {});

        const transformedStaffMedias = await Promise.all(Object.entries(groupedStaffMedias as Record<string, { before: MediasDocument[], after: MediasDocument[] }>).map(async ([staffId, medias]) => {
            const staff = jobInvoice.staffs.find((staff) => staff._id.toString() === staffId);

            if (!staff) {
                return undefined;
            }

            const domain = `${process.env.API_ROOT_URL}/medias/`
            const beforeImageBuffer = medias.before.map((media) => {
              return domain + media.mediaId + `?workspaceId=${invoice.workspace._id.toString()}`;
            });
            const afterImageBuffer = medias.after.map((media) => {
              return domain + media.mediaId + `?workspaceId=${invoice.workspace._id.toString()}`;
            });

            return {
                staffName: staff.firstName + ' ' + staff.lastName,
                before: beforeImageBuffer,
                after: afterImageBuffer
            }
        }));

        const dateCreated = dayjs(invoice.dateCreated);
        const dueDate = dateCreated.add(2, 'day').format('MMM DD YYYY');
        const freqDiscount = jobInvoice.frequency?.discountFrequency?.discount || jobInvoice.frequency?.discount;
        const discount = (Math.round(((freqDiscount / 100) * invoice.price ) * 100 ) / 100)
        const public_url = `${process.env.SPACES_URL}/${process.env.SPACES_FOLDER_NAME}/`;

        const pdf = await this.generatePdfService.generatePdf({
            logo: public_url + jobInvoice.workspace.logo,
            invoice_bill_to: jobInvoice.bookingRequest.firstName + ' ' + jobInvoice.bookingRequest.lastName,
            invoice_date: dateCreated.format('MMM DD YYYY'),
            invoice_due_date: dueDate,
            //@ts-ignore
            street_one: jobInvoice.bookingRequest.address.street_one,
            //@ts-ignore
            city: jobInvoice.bookingRequest.address.city,
            //@ts-ignore
            state: jobInvoice.bookingRequest.address.state,
            //@ts-ignore
            zip: Number(jobInvoice.bookingRequest.zip || 0),
            invoice_payable_address: jobInvoice.workspace.address ? `${jobInvoice.workspace.address.street_one}, ${jobInvoice.workspace.address.city}, ${jobInvoice.workspace.address.state}, ${jobInvoice.workspace.address.zip}` : '',
            dateServiced: dayjs(jobInvoice.dateServiced).format('MMM DD YYYY'),
            invoice_items: [
                {
                    content: 'Service: ' + jobInvoice.service.name,
                    amount: jobInvoice.service.price ?  '$' +jobInvoice.service.price: ''
                },
                ...jobInvoice?.addons.map((addon) => {

                    if(addon.addon.typed === 'range'){
                        const item = addon.addon.range?.[addon.quantity] || addon.addon.range.find((range) => range.price == addon.quantity)
                        return {
                            content: addon.addon.name + ': ' + item.name,
                            amount: item.price ? '$' + item.price : ''
                        }
                    }

                    return {
                        content:  '' + addon.quantity + ' x ' + addon.addon.name,
                        amount: addon.addon.price ? '$' +addon.addon.price*addon.quantity : ''
                    }
                }),
                ...jobInvoice?.extras.map((extra) => {
                    return {
                        content: '' + extra.quantity + ' x ' + extra.extra.name,
                        amount: extra.extra.price ? '$' + extra.extra.price*extra.quantity: ''
                    }
                }),
                {
                    content: 'Frequency: ' + jobInvoice.frequency?.title,
                    amount: jobInvoice.service.name === 'Individual' ? '- $' + discount : ''
                }
            ],
            invoice_number: invoice.invoiceNumber,
            invoice_payable_to: invoice.workspace.name,
            invoice_price: invoice.price,
            staffMedias: transformedStaffMedias
        });

        const media = await this.MediasService.upload('Invoice-' + invoice.invoiceNumber + '.pdf', pdf, 'application/pdf');
        const mediaUrl = process.env.WEB_APP_URL + '/assets/' + media.mediaId;

        await this.sendEmailService.sendEmailGoogle({
            from: jobInvoice.workspace.name,
            toEmail: jobInvoice.bookingRequest.email,
            cc: invoice.emailList,
            subject: `Invoice | Service Date: ${dayjs(jobInvoice.dateServiced).format('DD.MM.YYYY')} | ${invoice.invoiceNumber}`,
            attachments: [{ filename: 'invoice.pdf', content: pdf }],
            body: InvoiceReceived({
                invoice_due_date: dueDate,
                invoice_number: invoice.invoiceNumber,
                recipient: jobInvoice.bookingRequest.firstName + ' ' + jobInvoice.bookingRequest.lastName,
                sender: jobInvoice.workspace.name,
                service: jobInvoice.service.name,
                invoiceLink: mediaUrl
            })
        });

        const invoiceToUpdate = await this.invoicesModel.findById(invoice._id);
        if (!invoiceToUpdate) {
            throw new Error('Invoice not found');
        }

        invoiceToUpdate.scheduleSent = true;
        invoiceToUpdate.scheduledSentAt = new Date();
        await invoiceToUpdate.save();
    }

    async processScheduledInvoices() {
        const invoices = await this.redisClient.smembers('scheduled_by_date_invoices');
        const todayUTC = dayjs.utc().format('YYYY-MM-DD');
    
        for (const invoiceData of invoices) {
            const invoice: InvoicesDocument = JSON.parse(invoiceData);
            if (invoice.scheduleInvoiceDate === todayUTC) {
                await this.processInvoice(invoice);
                await this.redisClient.srem('scheduled_by_date_invoices', invoiceData);
            }
        }
    }

    async processOnCompleteInvoices(jobId: string) {
        const invoices = await this.redisClient.smembers('on_complete_invoices');
    
        for (const invoiceData of invoices) {
            const invoice: InvoicesDocument = JSON.parse(invoiceData);
            if (invoice.job._id.toString() === jobId) {
                await this.processInvoice(invoice);
                await this.redisClient.srem('on_complete_invoices', invoiceData);
            }
        }
    }

    @Cron(CronExpression.EVERY_1ST_DAY_OF_MONTH_AT_NOON, { timeZone: 'UTC' })
    async handleMonthlyInvoices() {
        await this.processScheduledInvoices();
    }
}