import { Module, forwardRef } from '@nestjs/common';
import { InvoicesController } from './invoices.controller';
import { InvoicesService } from './invoices.service';
import { Invoices, InvoicesSchema } from './invoices.schema';
import { MongooseModule } from '@nestjs/mongoose';
import { BookingRequestModule } from 'src/booking-request/booking-request.module';
import { BookingTypesModule } from 'src/booking-types/booking-types.module';
import { BookingFrequencyModule } from 'src/booking-frequency/booking-frequency.module';
import { ClientsModule } from 'src/clients/clients.module';
import { UsersModule } from '../users/users.module';
import { GeneratePdfModule } from "../generate-pdf/generate-pdf.module";
import { MediasModule } from "../medias/medias.module";
import { SendEmailModule } from "../send-email/send-email.module";
import { WorkspaceModule } from '../workspace/workspace.module';
import { JobsModule } from "../jobs/jobs.module";
import { Medias, MediasSchema } from '../medias/medias.schema';
import { InvoiceSchedulerService } from './invoices-schedule.service';

@Module({
    imports: [
        forwardRef(() => UsersModule),
        forwardRef(() => MediasModule),
        forwardRef(() => BookingRequestModule),
        forwardRef(() => BookingTypesModule),
        forwardRef(() => BookingFrequencyModule),
        forwardRef(() => ClientsModule),
        forwardRef(() => GeneratePdfModule),
        forwardRef(() => SendEmailModule),
        forwardRef(()=> WorkspaceModule),
        forwardRef(()=> JobsModule),
        MongooseModule.forFeature([
            { name: Medias.name, schema: MediasSchema },
            { name: Invoices.name, schema: InvoicesSchema },
        ])
    ],
    controllers: [InvoicesController],
    providers: [InvoicesService, InvoiceSchedulerService],
    exports: [InvoicesService, InvoiceSchedulerService],
})
export class InvoicesModule {}
