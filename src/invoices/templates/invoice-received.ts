type InvoiceReceivedPrams = {
  recipient: string;
  invoice_number: string,
  service:string,
  invoice_due_date: string,
  sender: string,
  invoiceLink:string
}

export const InvoiceReceived = (data:InvoiceReceivedPrams) => {
  return `
    <h3>Hi ${data.recipient},</h3>
    <p>I hope you&rsquo;re well. Please see attached invoice number <strong>${data.invoice_number}</strong> for <strong>${data.service}</strong>, due on <strong>${data.invoice_due_date}</strong>. Don&rsquo;t hesitate to reach out if you have any questions.</p>
    <p>You can view the invoice <a href="${data.invoiceLink}">here</a>.</p>
    <p>Kind regards,</p>
    <p><strong>${data.sender}</strong></p>
  `
}

