import { <PERSON><PERSON><PERSON><PERSON>, ExecutionContext, Injectable, NestInterceptor } from '@nestjs/common';
import { map, Observable } from "rxjs";
import { Invoices } from './invoices.schema';

@Injectable()
export class InvoicesInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    return next.handle()
      .pipe(map( (data) => {
        let result = data.data;
        console.log('result', result);
        if (Array.isArray(result) && result?.length) {
          result = result.map((item:Invoices) => this.mapData(item));
        }
        return {
          ...data,
          data: result
        }
      }))
  }

  private mapData(item: Invoices) {
    return {
      _id: item._id,
      services: {
        name: item.job.service?.name,
        clientType:{
          // @ts-ignore
          colorPrimary: item.job.service?.clientType?.colorPrimary,
          // @ts-ignore
          colorSecondary: item.job.service?.clientType?.colorSecondary,
        },
        icon: item.job.service?.color,
      },
      firstName: item.job.bookingRequest?.firstName,
      lastName: item.job.bookingRequest?.lastName,
      status: item.status,
      dateCreated: item.dateCreated,
      workspace: item.workspace,
      frequency: item.job.frequency,
      clientType: item.job.service?.clientType,
      address: item.job.bookingRequest?.address,
      sentDate: item.job.bookingRequest?.dateCreated,
      price: item.price,
      suitableDate: item.job.bookingRequest?.suitableDate,
      hours: item?.hours,
    }
  }
}
