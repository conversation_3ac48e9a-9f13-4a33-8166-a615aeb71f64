import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import { CollectionDto } from '@forlagshuset/nestjs-mongoose-paginate';
import { BadRequestException, forwardRef, HttpException, HttpStatus, Inject, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import mongoose, { Model } from 'mongoose';
import { EInvoiceSchedulingTiming, Invoices, InvoicesDocument } from './invoices.schema';
import CreateInvoiceDto from './Dto/CreateInvoiceDto';
import UpdateInvoiceDto from './Dto/UpdateInvoiceDto';
import { ClientsService } from 'src/clients/clients.service';
import { Pagination } from '@forlagshuset/nestjs-mongoose-paginate/src/output.dto';
import { CounterDto } from '@forlagshuset/nestjs-mongoose-paginate/src/input.dto';
import { paginateWithCount } from 'src/utils/helpers';
import { GeneratePdfService } from '../generate-pdf/generate-pdf.service';
import { MediasService } from '../medias/medias.service';
import { SendEmailService } from '../send-email/send-email.service';
import { InvoiceReceived } from './templates/invoice-received';
import * as process from 'process';
import { WorkspaceService } from '../workspace/workspace.service';
import { JobsService } from "../jobs/jobs.service";
import { Medias, MediasDocument } from '../medias/medias.schema';
import { Quotes } from "../quotes/quotes.schema";
import { InvoiceSchedulerService } from './invoices-schedule.service';
import { UpdateInvoiceSchedulingDto } from './Dto/UpdateInvoiceSchedulingDto';

dayjs.extend(utc);

@Injectable()
export class InvoicesService {
    constructor(
        @InjectModel(Medias.name) private mediasModel: Model<MediasDocument>,
        @Inject(forwardRef(() => ClientsService))
        private ClientsService: ClientsService,
        @Inject(forwardRef(() => GeneratePdfService))
        private GeneratePdfService: GeneratePdfService,
        @Inject(forwardRef(() => MediasService))
        private MediasService: MediasService,
        @Inject(forwardRef(() => SendEmailService))
        private SendEmailService: SendEmailService,
        @Inject(forwardRef(() => WorkspaceService))
        private WorkspaceService: WorkspaceService,
        @Inject(forwardRef(() => JobsService))
        private jobsService: JobsService,
        @InjectModel(Invoices.name)
        private invoicesModel: Model<InvoicesDocument>,
        @Inject(forwardRef(() => InvoiceSchedulerService))
        private invoiceSchedulerService: InvoiceSchedulerService,
    ) {}

    async findAll(collectionDto: CollectionDto): Promise<any> {
        return {
            data: await this.invoicesModel
                .find(collectionDto.filter)
                .skip(collectionDto.page * collectionDto.limit)
                .limit(collectionDto.limit)
                .populate({
                      path: 'job',
                      populate: [
                          { path: "bookingRequest", populate: { path: "address" } },
                          { path: "service" },
                          { path: "addons", populate: { path: "addon" } },
                          { path: "extras", populate: { path: "extra" } },
                          { path: "service", populate: { path: "clientType" } },
                          { path: "frequency", populate: { path: "discountFrequency" } },
                          { path: "staffMedias", populate: { path: "medias" } },
                          { path: "staffs" }
                      ]
                  })
                .sort(JSON.parse(collectionDto.sort || '{}'))
                .exec(),
            pagination: await this.paginate(collectionDto),
        };
    }

    private async paginate(query: CollectionDto) {
        const count: number = await this.count(query);
        const pagination: Pagination = {
            total: count,
            page: query.page,
            limit: query.limit,
            next:
                (query.page + 1) * query.limit >= count
                    ? undefined
                    : query.page + 1,
            prev: query.page == 0 ? undefined : query.page - 1,
        };

        return pagination;
    }

    async count(query: CounterDto): Promise<number> {
        return this.invoicesModel.countDocuments(query.filter).exec();
    }

    async findAllByClientId(
        id: string,
        workspace: string,
        collectionDto: CollectionDto,
    ): Promise<any> {
        const client = await this.ClientsService.getClient({ id, workspace });

        let mainEmail = client.email.find((i) => i.type == 'main').item;

        let invoices: any = await this.invoicesModel.aggregate([
            {
                $lookup: {
                    from: 'jobs',
                    localField: 'job',
                    foreignField: '_id',
                    as: 'job',
                },
            },
            {
                $unwind: '$job'
            },
            {
                $lookup: {
                    from: 'bookingtypes',
                    localField: 'job.service',
                    foreignField: '_id',
                    as: 'bookingTypes',
                },
            },
            {
                $lookup: {
                    from: 'bookingrequests',
                    localField: 'job.bookingRequest',
                    foreignField: '_id',
                    as: 'bookingRequest',
                },
            },
            {
                $lookup: {
                    from: 'bookingfrequencies',
                    localField: 'job.frequency',
                    foreignField: '_id',
                    as: 'frequency',
                },
            },
            {
                $addFields: {
                    addressId: {
                        $toObjectId:{
                            $first: '$bookingRequest.address'
                        }
                    }
                }
            },
            {
                $lookup: {
                    from: 'addresses',
                    localField: 'addressId',
                    foreignField: '_id',
                    as: 'addressContent',
                },
            },
            {
                $set: {
                    _id: '$_id',
                    title: {
                        $concat: [
                            { $first: '$addressContent.street_one' },
                            ", ",
                            { $first: '$addressContent.city' },
                            ", ",
                            { $first: '$addressContent.state' }
                        ]
                    },
                    service: { $first: '$bookingTypes.name' },
                    frequency: { $first: '$frequency.title' },
                    dateCreated: '$dateCreated',
                },
            },
            {
                $addFields: {
                    email: { $first: '$bookingRequest.email' },
                },
            },
            {
                $match: {
                    email: mainEmail,
                    workspace: new mongoose.Types.ObjectId(workspace),
                },
            },
            {
                $unset: [
                    'bookingRequest',
                    'addons',
                    'extras',
                    'video',
                    'attachments',
                    'suitableDate',
                    '__v',
                    'bookingTypes',
                    'notes',
                    'email',
                ],
            },
            {
                $facet: {
                    data: [
                        {
                            $skip:
                                (collectionDto?.page || 0) *
                                (collectionDto?.limit || 50),
                        },
                        { $limit: collectionDto?.limit || 50 },
                    ],
                    pagination: [
                        {
                            $count: 'total',
                        },
                    ],
                },
            },
            {
                $addFields: {
                    pagination: {
                        $first: '$pagination',
                    },
                },
            },
        ]);

        let result = invoices[0];

        if (!result.pagination) {
            result.pagination = {
                total: 0,
            };
        }

        let pagination = paginateWithCount(
            collectionDto,
            result.pagination.total,
        );

        return {
            ...result,
            pagination: {
                ...pagination,
            },
        };
    }

    async findOne(id: string, workspace: string): Promise<Invoices> {
        let item = await this.invoicesModel
            .findOne({
                _id: id,
                workspace: new mongoose.Types.ObjectId(workspace),
            })
            .populate({
                path: 'job',
                populate: [
                    { path: "bookingRequest", populate: { path: "address" } },
                    { path: "service" },
                    { path: "addons", populate: { path: "addon" } },
                    { path: "extras", populate: { path: "extra" } },
                    { path: "service", populate: { path: "clientType" } },
                    { path: "frequency", populate: { path: "discountFrequency" } },
                    { path: "staffMedias", populate: { path: "medias" } },
                    { path: "staffs" }
                ]
            })
        if (!item) {
            throw new Error('Invoice is not existed');
        }
        const data = item.toObject();
        let addons = data.job.addons;
        let extras = data.job.extras;
        let addonsQuery = [];
        let extrasQuery = [];

        if (addons) {
            addonsQuery = await Promise.all(addons.map(async (addon) => {
                const svgContent = await this.MediasService.getMedia(addon.addon.icon);
                addon.addon = {
                    ...addon.addon,
                    //@ts-ignore
                    iconSVG: await svgContent.Body.transformToString()
                }
                return {
                    ...addon
                }
            }));
        }

        if (extras) {
            extrasQuery = await Promise.all(extras.map(async (extra) => {
                const svgContent = await this.MediasService.getMedia(extra.extra.icon);
                extra.extra = {
                    ...extra.extra,
                    //@ts-ignore
                    iconSVG: await svgContent.Body.transformToString()
                }
                return {
                    ...extra
                }
            }));
        }

        data.job.addons = addonsQuery;
        data.job.extras = extrasQuery;

        return {
            ...data,
        };
    }

    async create(invoice: CreateInvoiceDto): Promise<InvoicesDocument> {
        const jobInvoice = await this.jobsService.findOne(invoice.job, invoice.workspace);
        if (!jobInvoice) {
            throw new BadRequestException(
                `Job is not existed`,
            );
        }
        const newId = new mongoose.Types.ObjectId();
        const genInvoiceNumber = `${jobInvoice.bookingRequest.firstName[0]}${jobInvoice.bookingRequest.lastName[0]}${newId.toString().slice(-6).toLocaleUpperCase()}`
        const created = await this.invoicesModel.create({
            _id: newId,
            workspace: new mongoose.Types.ObjectId(invoice.workspace),
            price: invoice.price,
            hours: invoice.hours,
            status: invoice?.status || 'waiting',
            dateCreated: new Date(),
            discountCode: invoice?.discountCode,
            job: new mongoose.Types.ObjectId(invoice.job),
            emailList: invoice?.emailList,
            invoiceNumber: genInvoiceNumber
        });

        await created.populate("workspace");

        const groupedStaffMedias = jobInvoice.staffMedias.reduce((acc,staffMedia) => {
            if (!acc[staffMedia.staff.toString()]) {
                acc[staffMedia.staff.toString()] = { before: [], after: [] };
            }
            acc[staffMedia.staff.toString()][staffMedia.beforeAfter].push(...staffMedia.medias);
            return acc;
        }, {});

        const transformedStaffMedias = await Promise.all(Object.entries(groupedStaffMedias as Record<string, { before: MediasDocument[], after: MediasDocument[] }>).map(async ([staffId, medias]) => {
            const staff = jobInvoice.staffs.find((staff) => staff._id.toString() === staffId);

            if (!staff) {
                return undefined;
            }

            const domain = `${process.env.API_ROOT_URL}/medias/`
            const beforeImageBuffer = medias.before.map((media) => {
              return domain + media.mediaId + `?workspaceId=${created.workspace._id.toString()}`;
            });
            const afterImageBuffer = medias.after.map((media) => {
              return domain + media.mediaId + `?workspaceId=${created.workspace._id.toString()}`;
            });

            return {
                staffName: staff.firstName + ' ' + staff.lastName,
                before: beforeImageBuffer,
                after: afterImageBuffer
            }
        }));

        // Send notification to Admins
        if (created) {
            const dateCreated = dayjs(created.dateCreated);
            const dueDate = dateCreated.add(2, 'day').format('MMM DD YYYY');
            const freqDiscount = jobInvoice.frequency?.discountFrequency?.discount || jobInvoice.frequency.discount;
            const discount = (Math.round(((freqDiscount / 100) * created.price ) * 100 ) / 100)
            const public_url = `${process.env.SPACES_URL}/${process.env.SPACES_FOLDER_NAME}/`
            const pdf = await this.GeneratePdfService.generatePdf({
                logo: public_url + created.workspace.logo,
                invoice_bill_to: jobInvoice.bookingRequest.firstName + ' ' + jobInvoice.bookingRequest.lastName,
                invoice_date: dateCreated.format('MMM DD YYYY'),
                invoice_due_date: dueDate,
                //@ts-ignore
                street_one: jobInvoice.bookingRequest.address.street_one,
                //@ts-ignore
                city: jobInvoice.bookingRequest.address.city,
                //@ts-ignore
                state: jobInvoice.bookingRequest.address.state,
                //@ts-ignore
                zip: Number(jobInvoice.bookingRequest.zip || 0),
                invoice_payable_address: created.workspace.address ?(created.workspace.address.street_one + ', ' + created.workspace.address.city + ', ' + created.workspace.address.state + ', ' + created.workspace.address.zip ):'',
                dateServiced: dayjs(jobInvoice.dateServiced).format('MMM DD YYYY'),
                invoice_items: [
                    {
                        content: 'Service: ' + jobInvoice.service.name,
                        amount: jobInvoice.service.price ?  '$' +jobInvoice.service.price: ''
                    },
                    ...jobInvoice?.addons.map((addon) => {

                        if(addon.addon.typed === 'range'){
                            const item = addon.addon.range?.[addon.quantity] || addon.addon.range.find((range) => range.price == addon.quantity)
                            return {
                                content: addon.addon.name + ': ' + item.name,
                                amount: item.price ? '$' + item.price : ''
                            }
                        }

                        return {
                            content:  '' + addon.quantity + ' x ' + addon.addon.name,
                            amount: addon.addon.price ? '$' +addon.addon.price*addon.quantity : ''
                        }
                    }),
                    ...jobInvoice?.extras.map((extra) => {
                        return {
                            content: '' + extra.quantity + ' x ' + extra.extra.name,
                            amount: extra.extra.price ? '$' + extra.extra.price*extra.quantity: ''
                        }
                    }),
                    {
                        content: 'Frequency: ' + jobInvoice.frequency.title,
                        amount: jobInvoice.service.name === 'Individual' ? '- $' + discount : ''
                    }
                ],
                invoice_number: genInvoiceNumber,
                invoice_payable_to: created.workspace.name,
                invoice_price: created.price,
                staffMedias: transformedStaffMedias
            });
            const media = await this.MediasService.upload('Invoice-' + genInvoiceNumber + '.pdf', pdf,'application/pdf');
            const mediaUrl = process.env.WEB_APP_URL + '/assets/' + media.mediaId;

            this.SendEmailService.sendEmailGoogle({
                from: created.workspace.name,
                toEmail:  jobInvoice.bookingRequest.email,  //created.bookingRequest.email
                cc: created.emailList,
                subject: `Invoice | Service Date: ${dayjs(jobInvoice.dateServiced).format('DD.MM.YYYY')} | ${genInvoiceNumber}`,
                attachments: [
                    {
                        filename: 'invoice.pdf',
                        content: pdf,
                    }
                ],
                body: InvoiceReceived({
                    invoice_due_date: dueDate,
                    invoice_number: genInvoiceNumber,
                    recipient: jobInvoice.bookingRequest.firstName + ' ' + jobInvoice.bookingRequest.lastName,
                    sender: created.workspace.name,
                    service: jobInvoice.service.name,
                    invoiceLink: mediaUrl
                })
            }, created.workspace._id.toString()).then(r  => false)

            created.invoiceFile = media.mediaId;
            await created.save();
            return created;
        }
        throw new BadRequestException(
            `Failed to create new invoice, try again`,
        );
    }

    async createSchedule(invoice: CreateInvoiceDto): Promise<InvoicesDocument> {
        const jobInvoice = await this.jobsService.findOne(invoice.job, invoice.workspace);
        if (!jobInvoice) {
            throw new BadRequestException(`Job is not existed`);
        }
    
        const newId = new mongoose.Types.ObjectId();
        const genInvoiceNumber = `${jobInvoice.bookingRequest.firstName[0]}${jobInvoice.bookingRequest.lastName[0]}${newId.toString().slice(-6).toUpperCase()}`;
    
        const scheduleDate = invoice.scheduleInvoiceTiming === EInvoiceSchedulingTiming.BY_DATE 
            ? dayjs.utc(invoice.scheduleInvoiceDate).format('YYYY-MM-DD')
            : null;
    
        const created = await this.invoicesModel.create({
            _id: newId,
            workspace: new mongoose.Types.ObjectId(invoice.workspace),
            job: new mongoose.Types.ObjectId(invoice.job),
            price: invoice.price,
            hours: invoice.hours,
            status: invoice?.status || 'waiting',
            dateCreated: dayjs.utc().toDate(),
            discountCode: invoice?.discountCode,
            emailList: invoice?.emailList,
            invoiceNumber: genInvoiceNumber,
            scheduleInvoiceCondition: invoice.scheduleInvoiceCondition,
            scheduleInvoiceTiming: invoice.scheduleInvoiceTiming,
            scheduleInvoiceDate: scheduleDate,
            scheduleSent: false
        });
    
        await created.populate({
            path: 'job',
            populate: [
                { path: "bookingRequest", populate: { path: "address" } },
                { path: "service" },
                { path: "addons", populate: { path: "addon" } },
                { path: "extras", populate: { path: "extra" } },
                { path: "service", populate: { path: "clientType" } },
                { path: "frequency", populate: { path: "discountFrequency" } },
                { path: "staffMedias", populate: { path: "medias" } },
                { path: "staffs" }
            ]
        });
        await created.populate('workspace');
    
        await this.invoiceSchedulerService.scheduleInvoice(created);
    
        return created;
    }

    async findByJobId(jobId: string, workspace: string): Promise<Invoices> {
        return await this.invoicesModel
            .findOne({
                job: new mongoose.Types.ObjectId(jobId),
                workspace: new mongoose.Types.ObjectId(workspace),
            })
            .populate({
                path: 'job',
                populate: [
                    { path: "bookingRequest", populate: { path: "address" } },
                    { path: "service" },
                    { path: "addons", populate: { path: "addon" } },
                    { path: "extras", populate: { path: "extra" } },
                    { path: "service", populate: { path: "clientType" } },
                    { path: "frequency", populate: { path: "discountFrequency" } },
                    { path: "staffMedias", populate: { path: "medias" } },
                    { path: "staffs" }
                ]
            })
            .populate('workspace')
            .exec();
    }

    async update(id: string, invoice: UpdateInvoiceDto): Promise<any> {
        const checkExist = await this.invoicesModel.findById(id).exec();
        if (!checkExist) {
            throw new HttpException(
                'Invoice is not existed',
                HttpStatus.CONFLICT,
            );
        }

        let updateData = { ...invoice };

        ['workspace', 'job'].forEach(key => {
            if (invoice?.[key]) {
                updateData[key] = new mongoose.Types.ObjectId(invoice[key]);
            }
        });

        return this.invoicesModel.findByIdAndUpdate(
            id,
            { ...updateData },
            { new: true, runValidators: true },
        ).populate('job');
    }

    async delete(id: string, workspace: string): Promise<Invoices> {
        return this.invoicesModel.findOneAndRemove({
            _id: id,
            workspace: new mongoose.Types.ObjectId(workspace),
        });
    }

    async updateScheduling(jobId: string, workspace: string, updateDto: UpdateInvoiceSchedulingDto): Promise<Invoices> {
        const invoice = await this.invoicesModel.findOne({
            job: new mongoose.Types.ObjectId(jobId),
            workspace: new mongoose.Types.ObjectId(workspace)
        });

        if (!invoice) {
            throw new HttpException('Invoice not found', HttpStatus.NOT_FOUND);
        }

        // Update scheduling fields
        if (updateDto.scheduleInvoiceCondition) {
            invoice.scheduleInvoiceCondition = updateDto.scheduleInvoiceCondition;
        }
        if (updateDto.scheduleInvoiceTiming) {
            invoice.scheduleInvoiceTiming = updateDto.scheduleInvoiceTiming;
        }
        if (updateDto.scheduleInvoiceDate) {
            invoice.scheduleInvoiceDate = dayjs.utc(updateDto.scheduleInvoiceDate).format('YYYY-MM-DD');
        }
        if (updateDto.emailList) {
            invoice.emailList = updateDto.emailList;
        }

        // Reset scheduling status
        invoice.scheduleSent = false;
        invoice.scheduledSentAt = null;

        const updated = await invoice.save();

        // Re-populate data
        await updated.populate({
            path: 'job',
            populate: [
                { path: "bookingRequest", populate: { path: "address" } },
                { path: "service" },
                { path: "addons", populate: { path: "addon" } },
                { path: "extras", populate: { path: "extra" } },
                { path: "service", populate: { path: "clientType" } },
                { path: "frequency", populate: { path: "discountFrequency" } },
                { path: "staffMedias", populate: { path: "medias" } },
                { path: "staffs" }
            ]
        });
        await updated.populate('workspace');

        // Re-schedule invoice
        await this.invoiceSchedulerService.scheduleInvoice(updated);

        return updated;
    }
}
