import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsArray,
  IsDate, IsEmail,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  ValidateNested
} from "class-validator";
import { Type } from 'class-transformer';
import CreateJobDto from "../../jobs/Dto/CreateJobDto";
import { EInvoiceSchedulingCondition, EInvoiceSchedulingTiming } from '../invoices.schema';

export default class CreateInvoiceDto {
  @ApiProperty()
  @IsOptional()
  @IsNumber()
  readonly price: number;

  @ApiProperty()
  @IsNotEmpty()
  readonly job: string;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  readonly hours: number;

  @ApiProperty()
  @IsOptional()
  @IsString()
  readonly workspace: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  readonly discountCode: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsEnum(['waiting', 'overdue', 'paid'])
  readonly status: 'waiting' | 'overdue' | 'paid';

  @ApiProperty()
  @IsOptional()
  @IsEmail({}, { each: true })
  readonly emailList?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  @IsEnum(EInvoiceSchedulingCondition)
  readonly scheduleInvoiceCondition?: EInvoiceSchedulingCondition;

  @ApiPropertyOptional()
  @IsOptional()
  @IsEnum(EInvoiceSchedulingTiming)
  readonly scheduleInvoiceTiming?: EInvoiceSchedulingTiming;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  readonly scheduleInvoiceDate?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  readonly tipAmount?: number;
}
