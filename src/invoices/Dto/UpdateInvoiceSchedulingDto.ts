import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString, IsEmail } from 'class-validator';
import { EInvoiceSchedulingCondition, EInvoiceSchedulingTiming } from '../invoices.schema';

export class UpdateInvoiceSchedulingDto {
  @ApiPropertyOptional()
  @IsOptional()
  @IsEnum(EInvoiceSchedulingCondition)
  scheduleInvoiceCondition?: EInvoiceSchedulingCondition;

  @ApiPropertyOptional()
  @IsOptional()
  @IsEnum(EInvoiceSchedulingTiming)
  scheduleInvoiceTiming?: EInvoiceSchedulingTiming;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  scheduleInvoiceDate?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsEmail({}, { each: true })
  emailList?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  workspace?: string;
} 