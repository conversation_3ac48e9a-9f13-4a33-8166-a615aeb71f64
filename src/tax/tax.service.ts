import { HttpStatus, HttpException, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Tax, TaxDocument } from './tax.schema';
import mongoose, { Model } from "mongoose";
import { CollectionDto, DocumentCollector } from "@forlagshuset/nestjs-mongoose-paginate";
import CreateTaxDto from './Dto/CreateTaxDto';
import UpdateTaxDto from './Dto/UpdateTaxDto';

@Injectable()
export class TaxService {
    constructor(
        @InjectModel(Tax.name) private taxModel: Model<TaxDocument>,
    ) {}

    async findAll(collectionDto: CollectionDto): Promise<any> {
        const collector = new DocumentCollector<TaxDocument>(this.taxModel)
        return collector.find(collectionDto)
    }

    async findOne(id: string,workspace:string): Promise<Tax> {
        let tax = await this.taxModel.findOne({
            _id: new mongoose.Types.ObjectId(id),
            workspace: new mongoose.Types.ObjectId(workspace)
        })
        if(!tax) {
            throw new Error('Data Tax not existed')
        }
        return tax
    }

    async create(createTaxDto: CreateTaxDto): Promise<Tax> {
        const checkExist = await this.taxModel.find({
            name: createTaxDto.name,
            workspace: new mongoose.Types.ObjectId(createTaxDto.workspace)
        })
        if(checkExist?.length) {
            throw new HttpException('Data Tax existed', HttpStatus.CONFLICT)
        }

        return await this.taxModel.create({
            name: createTaxDto.name,
            value: createTaxDto.value,
            status: createTaxDto?.status || 'active',
            workspace: new mongoose.Types.ObjectId(createTaxDto.workspace),
            dateCreated: new Date()
        })
    }

    async update(id: string, updateTaxDto: UpdateTaxDto): Promise<Tax> {
        const checkExist = await this.taxModel.findOne({
            _id: new mongoose.Types.ObjectId(id),
            workspace: new mongoose.Types.ObjectId(updateTaxDto.workspace)
        }).exec()
        if (!checkExist) {
          throw new HttpException('Data Tax not existed', HttpStatus.CONFLICT);
        }

        return this.taxModel.findByIdAndUpdate(id, {
            ...updateTaxDto,
            workspace: new mongoose.Types.ObjectId(updateTaxDto.workspace)
        })
    }

    async delete(id: string): Promise<Tax> {
        return this.taxModel.findByIdAndDelete(id)
    }
}
