import { Module, forwardRef } from '@nestjs/common';
import { TaxService } from './tax.service';
import { TaxController } from './tax.controller';
import { MongooseModule } from '@nestjs/mongoose';
import { Tax, TaxSchema } from './tax.schema';
import { UsersModule } from '../users/users.module';
import { ClientsModule } from '../clients/clients.module';

@Module({
    imports: [
        forwardRef(() => ClientsModule),
        forwardRef(() => UsersModule),
        MongooseModule.forFeature([{ name: Tax.name, schema: TaxSchema }]),
    ],
    providers: [TaxService],
    exports: [TaxService],
    controllers: [TaxController],
})
export class TaxModule {}
