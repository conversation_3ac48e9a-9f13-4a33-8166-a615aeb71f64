import { ApiProperty } from "@nestjs/swagger";
import {IsEnum, IsNotEmpty, IsNumber, IsOptional, IsString} from "class-validator";

export default class CreateTaxDto {
    @ApiProperty()
    @IsNotEmpty()
    @IsString()
    readonly name: string;

    @ApiProperty()
    @IsNotEmpty()
    @IsNumber()
    readonly value: number;

    @ApiProperty()
    @IsOptional()
    @IsEnum(['active', 'unactive'])
    readonly status: 'active' | 'unactive';

    @ApiProperty()
    @IsNotEmpty()
    @IsString()
    readonly workspace: string;
}
