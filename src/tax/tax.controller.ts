import {
  Controller,
  HttpCode,
  HttpException,
  HttpStatus,
  Get,
  Post,
  Patch,
  Delete,
  Query,
  Body,
  Param,
  UseGuards,
} from '@nestjs/common';
import { TaxService } from './tax.service';
import {
  CollectionDto,
  CollectionResponse,
} from '@forlagshuset/nestjs-mongoose-paginate';
import { Tax } from './tax.schema';
import CreateTaxDto from './Dto/CreateTaxDto';
import UpdateTaxDto from './Dto/UpdateTaxDto';
import mongoose from 'mongoose';
import { AuthGuard } from '../auth/auth.guard';
import { ACGuard, UseRoles } from 'nest-access-control';
import ROLE_RESOURCES from '../@core/constants/roles.resources';
import { ApiTags } from '@nestjs/swagger';

@Controller(ROLE_RESOURCES.TAX)
@ApiTags('Tax')
export class TaxController {
  constructor(private taxService: TaxService) {}

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.TAX,
    action: 'read',
    possession: 'any',
  })
  @Get('/')
  async getTax(
    @Query()
    collectionDto: CollectionDto,
    @Body('workspace') workspace: string,
  ): Promise<CollectionResponse<Tax>> {
    collectionDto.filter['workspace'] = {
      $eq: new mongoose.Types.ObjectId(workspace),
    };
    return this.taxService.findAll(collectionDto);
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.TAX,
    action: 'read',
    possession: 'any',
  })
  @Get(':id')
  async getTaxById(
    @Param('id') id: string,
    @Body('workspace') workspace: string,
  ) {
    let tax = await this.taxService.findOne(id, workspace);
    if (!tax) {
      throw new HttpException('Data Tax not existed', HttpStatus.BAD_REQUEST);
    }
    return {
      statusCode: HttpStatus.OK,
      message: 'Get Tax by Id success',
      data: tax,
    };
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.TAX,
    action: 'create',
    possession: 'any',
  })
  @Post('/create')
  async createTax(@Body() createTaxDto: CreateTaxDto) {
    let taxCreated = await this.taxService.create(createTaxDto);
    if (!taxCreated) {
      throw new HttpException('Can not create Tax', HttpStatus.BAD_REQUEST);
    }

    return {
      statusCode: HttpStatus.OK,
      message: 'Create tax successfully',
      data: taxCreated,
    };
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.TAX,
    action: 'update',
    possession: 'any',
  })
  @Patch(':id')
  async updateTax(@Param('id') id: string, @Body() updateTaxDto: UpdateTaxDto) {
    let taxUpdated = await this.taxService.update(id, updateTaxDto);
    if (!taxUpdated) {
      throw new HttpException('Can not update Tax', HttpStatus.BAD_REQUEST);
    }

    return {
      statusCode: HttpStatus.OK,
      message: 'Update tax item successfully',
      data: taxUpdated,
    };
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.TAX,
    action: 'delete',
    possession: 'any',
  })
  @Delete(':id')
  async deleteTax(@Param('id') id: string) {
    let taxDeleted = await this.taxService.delete(id);
    if (!taxDeleted) {
      throw new HttpException('Can not delete Tax', HttpStatus.BAD_REQUEST);
    }

    return {
      statusCode: HttpStatus.OK,
      message: 'Delete tax item successfully',
      data: taxDeleted,
    };
  }
}
