import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema, Types } from "mongoose";
import { Workspace } from "../workspace/workspace.schema";
import { Type } from "class-transformer";

export enum STATUS {
    active = 'active',
    unactive = 'unactive'
}

@Schema()
export class Tax {
    _id: MongooseSchema.Types.ObjectId;

    @Prop({ required: true})
    name: string;

    @Prop({ required: true})
    value: number;

    @Prop({ default: STATUS.active, enum: STATUS })
    status: string;

    @Prop({ required: true })
    dateCreated: Date;

    @Prop({ required: true, type: Types.ObjectId, ref: Workspace.name })
    @Type(() => Workspace)
    workspace: Workspace;
}

export type TaxDocument = Tax & Document;

export const TaxSchema = SchemaFactory.createForClass(Tax)