import { NotificationPlatform, NotificationPriority, NotificationType } from "../notifications.schema";

export class NotificationPayload {
  title: string;
  body: string;
  data: Record<string, any>;
  notificationType?: NotificationType;
  platform?: NotificationPlatform;
  priority?: NotificationPriority;

  constructor(title: string, body: string, data: Record<string, any>) {
    this.title = title;
    this.body = body;
    this.data = data;
    this.priority = NotificationPriority.NORMAL;
    this.notificationType = NotificationType.ALERT;
    this.platform = NotificationPlatform.ALL;
  }
}