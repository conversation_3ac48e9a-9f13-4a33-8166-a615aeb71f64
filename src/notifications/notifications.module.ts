import { forwardRef, <PERSON><PERSON><PERSON> } from "@nestjs/common";
import { NotificationsController } from "./notifications.controller";
import { NotificationsService } from "./notifications.service";
import { MongooseModule } from "@nestjs/mongoose";
import { Notification, NotificationSchema } from "./notifications.schema";
import { UsersModule } from "../users/users.module";
import { ClientsModule } from "../clients/clients.module";
import { InfoModule } from "../info/info.module";

@Module({
  imports: [
    forwardRef(() => UsersModule),
    forwardRef(() => ClientsModule),
    forwardRef(() => InfoModule),
    MongooseModule.forFeature([{ name: Notification.name, schema: NotificationSchema }]),
  ],
  controllers: [NotificationsController],
  providers: [NotificationsService],
  exports: [NotificationsService],
})
export class NotificationsModule {
}
