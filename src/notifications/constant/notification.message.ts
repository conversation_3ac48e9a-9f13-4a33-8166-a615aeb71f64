export const getNotiMessage = (lang: string) => {
  if (lang === "en") {
    return notiEnMessage
  }
  if (lang === "es") {
    return notiEsMessage
  }
  return notiEnMessage
}

const notiEnMessage = {
  "assigned-job": {
    title: "Job Assigned",
    body: "You have been assigned a job",
  },
  "unassigned-job": {
    title: "Job Unassigned",
    body: "You have been unassigned a job",
  },
  "other-complete-job": (userName: string) => ({
    title: "Job Completed",
    body: `${userName} has completed the job`,
  }),
  "other-start-job": (userName: string) => ({
    title: "Job Started",
    body: `${userName} has started the job`,
  }),
  "completed-job": {
    title: "Job Completed",
    body: "Your job has been completed",
  },
  "job-reminder-in-date": (date: string) => ({
    title: "Reminder",
    body: `You have a job scheduled on ${date}`,
  }),
  "job-reminder-in-time": (time: string) => ({
    title: "Reminder",
    body: `You have a job scheduled in ${time} from now`,
  }),
  "job-has-not-assigned": (jobName: string) => ({
    title: `Job Not Assigned - ${jobName}`,
    body: `${jobName} has not been assigned yet`,
  })

} as const
const notiEsMessage = {
  "assigned-job": {
    title: "Trabajo asignado",
    body: "Se le ha asignado un trabajo",
  },
  "unassigned-job": {
    title: "Trabajo no asignado",
    body: "Se le ha desasignado un trabajo",
  },
  "other-complete-job": (userName: string) => ({
    title: "Trabajo completado",
    body: `${userName} ha completado el trabajo`,
  }),
  "other-start-job": (userName: string) => ({
    title: "Trabajo iniciado",
    body: `${userName} ha iniciado el trabajo`,
  }),
  "completed-job": {
    title: "Trabajo completado",
    body: "Tu trabajo ha sido completado",
  },
  "job-reminder-in-date": (date: string) => ({
    title: "Recordatorio",
    body: `Tienes un trabajo programado para el ${date}`,
  }),
  "job-reminder-in-time": (time: string) => ({
    title: "Recordatorio",
    body: `Tienes un trabajo programado en ${time} desde ahora`,
  }),
  "job-has-not-assigned": (jobName: string) => ({
    title: `Trabajo no asignado - ${jobName}`,
    body: `${jobName} aún no ha sido asignado`,
  })
} as const