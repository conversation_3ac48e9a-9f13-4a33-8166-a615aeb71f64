import { forwardRef, Inject, Injectable } from "@nestjs/common";
import { getMessaging } from "firebase-admin/messaging";
import { InjectModel } from "@nestjs/mongoose";
import mongoose, { Model } from "mongoose";

import { UsersService } from "../users/users.service";
import { NotificationPayload } from "./DTO/notificationPayload";
import { Notification, NotificationDocument, NotificationPlatform, NotificationStatus } from "./notifications.schema";
import { CollectionDto } from "@forlagshuset/nestjs-mongoose-paginate";
import { CounterDto } from "@forlagshuset/nestjs-mongoose-paginate/src/input.dto";
import { InfoService } from "../info/info.service";


@Injectable()
export class NotificationsService {
  constructor(
    @InjectModel(Notification.name) private notificationModel: Model<NotificationDocument>,
    @Inject(forwardRef(() => UsersService))
    private readonly userService: UsersService,
    private readonly infoService: InfoService,
  ) {
  }

  async sendNotification(userId: string, payload: NotificationPayload) {
    const { data, title, body, priority, platform, notificationType } = payload;

    const user = await this.userService.findOne(userId);

    if (!user) {
      return false;
    }
    if (!user.deviceToken) {
      return false;
    }
    if (platform !== NotificationPlatform.ALL) {
      if (user.devicePlatform !== platform) {
        return false;
      }
    }

    const notiObj = await this.notificationModel.create({
      title,
      message: body,
      dataPayload: data,
      deviceToken: [user.deviceToken],
      platform,
      priority,
      notificationType,
      users: [user._id]
    });

    try {
      const notiResult = await getMessaging().send({
        token: user.deviceToken,
        notification: {
          title,
          body
        },
        data: {
          ...data,
          "click_action": "FLUTTER_NOTIFICATION_CLICK"
        }
      });
      return !!notiResult;
    } catch (err) {
      console.log(err);
      notiObj.status = NotificationStatus.FAILED;
      notiObj.errorMessage = err?.message;
      return false;
      // throw new Error(`Error sending notification to user with id ${userId}`);
    } finally {
      await notiObj.save();
    }
  }

  async sendMulticastNotification(listUserId: string[], payload: NotificationPayload) {
    const { data, title, body, priority, platform, notificationType } = payload;

    const userList = await this.userService.findMany(listUserId);

    if (!userList || userList.length === 0) {
      return false;
    }

    const tokens = userList.filter(user => !!user.deviceToken).map(user => user.deviceToken);

    const notiObj = await this.notificationModel.create({
      title,
      message: body,
      dataPayload: data,
      deviceToken: tokens,
      platform,
      priority,
      notificationType,
      users: userList.map(user => user._id)
    });

    try {
      const notiResult = await getMessaging().sendEachForMulticast({
        tokens,
        notification: {
          title,
          body
        },
        data: {
          ...data,
          "click_action": "FLUTTER_NOTIFICATION_CLICK"
        }
      });
      if (notiResult.failureCount > 0) {
        notiResult.responses.forEach((response, index) => {
          if (response.success === false) {
            notiObj.failureToken.push({ token: tokens[index], user: userList[index] });
          }
        });
      }
      return notiResult.successCount > 0;
    } catch (err) {
      console.log(err);
      notiObj.status = NotificationStatus.FAILED;
      notiObj.errorMessage = err?.message;
      return false;
      // throw new Error(`Error sending multicast notification to users`);
    } finally {
      await notiObj.save();
    }
  }

  async checkIfNotificationSent(userId: string, payload: Pick<NotificationPayload, "title" | "body">) {
    const { title, body } = payload;

    const user = await this.userService.findOne(userId);

    if (!user) {
      return false;
    }
    if (!user.deviceToken) {
      return false;
    }

    const notiObj = await this.notificationModel.findOne({
      title,
      message: body,
      deviceToken: user.deviceToken
    });

    return !!notiObj;
  }

  async checkIfAdminNotificationSent(userId: string, payload: Pick<NotificationPayload, "title" | "body" | "data">) {
    const { title, body, data } = payload;

    const notiObj = await this.notificationModel.findOne({
      title,
      message: body,
      "dataPayload.id": data?.id,
      platform: NotificationPlatform.Web
    });

    return !!notiObj;
  }

  async getNotificationsByUserId(userId: string): Promise<Notification[]> {
    const user = await this.userService.findOne(userId);
    if (!user) {
      return [];
    }
    return this.notificationModel.aggregate([
      {
        $match: {
          $or: [
            { users: { $in: [user._id] } },
            { deviceToken: { $in: [user.deviceToken] } }
          ],
          status: { $ne: NotificationStatus.FAILED }
        }
      },
      {
        $sort: { createdAt: -1 } // Sort by createdAt in descending order
      },
      {
        $limit: 20 // Limit to 20 results
      },
      {
        $project: {
          deviceToken: 0,
          failureToken: 0,
          errorMessage: 0,
          __v: 0,
          scheduledFor: 0,
          isScheduled: 0,
          users: 0
        }
      },
      {
        $group: {
          _id: { $dateToString: { format: "%Y-%m-%d", date: "$createdAt" } }, // Group by date only
          notifications: { $push: "$$ROOT" } // Push entire document into notifications array
        }
      },
      {
        $replaceWith: {
          date: "$_id",
          notifications: "$notifications"
        }
      },
      {
        $sort: { date: -1 } // Sort grouped results by createdAt in descending order (optional)
      }
    ]);
  }

  async getNotificationsForAdmin(workspaceId: string, collectionDto: CollectionDto) {
    const query = {
      workspace: workspaceId,
      status: { $ne: NotificationStatus.FAILED },
      platform: NotificationPlatform.Web,
      ...collectionDto.filter
    };
    const pagination = await this.paginate(query);
    return {
      data: await this.notificationModel.find(query)
        .skip(collectionDto.page * collectionDto.limit)
        .limit(collectionDto.limit)
        .sort(collectionDto.sort ? JSON.parse(collectionDto.sort || "{}") : { createdAt: -1 })
        .exec(),
      pagination
    };
  }

  async sendAdminNotification(workspaceId: string, payload: NotificationPayload) {
    const { data, title, body, priority, notificationType } = payload;

    if (!workspaceId) {
      return false;
    }

    // TODO: Implement push notification to user
    // const tokens = userList.filter(user => !!user.deviceToken).map(user => user.deviceToken);

    try {
      const notiObj = await this.notificationModel.create({
        title,
        message: body,
        dataPayload: data,
        deviceToken: [],
        platform: NotificationPlatform.Web,
        priority,
        notificationType,
        workspace: new mongoose.Types.ObjectId(workspaceId)
      });
      this.infoService.sendNotificationToWorkspace(workspaceId, title);
      await notiObj.save();
    } catch (err) {
      console.log(err);
      return false;
    }
  }

  async readNotification(notificationIds: string[]) {
    return this.notificationModel.updateMany({ _id: { $in: notificationIds.map(id => new mongoose.Types.ObjectId(id)) } }, { status: NotificationStatus.READ });
  }

  private async paginate(query: CollectionDto & Record<string, any>) {
    const count: number = await this.count(query);
    return this.createPagination(count, query);
  }

  private async count(query: CounterDto): Promise<number> {
    return this.notificationModel.countDocuments(query).exec();
  }

  private createPagination(count: number, collectionDto: CollectionDto) {
    return {
      total: count,
      page: collectionDto.page,
      limit: collectionDto.limit,
      next:
        (collectionDto.page + 1) * collectionDto.limit >= count
          ? undefined
          : collectionDto.page + 1,
      prev: collectionDto.page == 0 ? undefined : collectionDto.page - 1
    };
  }
}
