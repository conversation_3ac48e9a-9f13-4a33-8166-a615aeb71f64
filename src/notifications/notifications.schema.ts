import { Prop, Schem<PERSON>, SchemaFactory } from "@nestjs/mongoose";
import { Document, Schema as MongooseSchema, Types } from "mongoose";
import { Users } from "../users/users.schema";
import { Workspace } from "../workspace/workspace.schema";

export enum NotificationStatus { SENT = "sent", DELIVERED = "delivered", READ = "read", FAILED = "failed", }

export enum NotificationType { ALERT = "alert", REMINDER = "reminder", PROMOTION = "promotion", }

export enum NotificationPlatform { IOS = "iOS", ANDROID = "Android", ALL = "All", Web = 'Web'}

export enum NotificationPriority { LOW = "low", NORMAL = "normal", HIGH = "high", }

@Schema({ timestamps: true })
export class Notification {
  _id: MongooseSchema.Types.ObjectId;

  @Prop({ required: true, type: String })
  title: string;

  @Prop({ required: false })
  message: string;

  @Prop({ type: Object, default: {} })
  dataPayload: Record<string, any>;

  @Prop({ enum: NotificationStatus, default: NotificationStatus.SENT })
  status: NotificationStatus;

  @Prop({ enum: NotificationType, required: false, default: NotificationType.ALERT})
  notificationType: string;

  @Prop({ default: false })
  isScheduled: boolean;

  @Prop({ type: Date, default: null })
  scheduledFor: Date;

  @Prop([{type: Types.ObjectId, ref: "Users"}])
  users: Users[];

  @Prop({ required: false, type: Types.ObjectId, ref: Workspace.name })
  workspace?: Workspace;

  @Prop({ default: null, type: [String] })
  deviceToken: string[];

  @Prop({ default: null, type: [{ token: String, user: { type: Types.ObjectId, ref: "Users" } }] })
  failureToken?: { token: string, user: Users }[];

  @Prop({ enum: NotificationPriority, default: NotificationPriority.NORMAL })
  priority: string;

  @Prop({ enum: NotificationPlatform, required: false, default: NotificationPlatform.ALL })
  platform: string;

  @Prop({ default: null })
  errorMessage: string;
}

export type NotificationDocument = Notification & Document;

export const NotificationSchema = SchemaFactory.createForClass(Notification);