import { Request } from "express";
import { Body, Controller, Get, HttpCode, HttpStatus, Param, Post, Query, Req, UseGuards } from "@nestjs/common";
import ROLE_RESOURCES from "../@core/constants/roles.resources";
import { NotificationsService } from "./notifications.service";
import { ACGuard, UseRoles } from "nest-access-control";
import jwt from "jsonwebtoken";
import { UsersService } from "../users/users.service";
import { AuthGuard } from "../auth/auth.guard";
import { CollectionDto } from "@forlagshuset/nestjs-mongoose-paginate";

interface TokenRequest extends Request {
  user: {
    deviceToken: string;
    // other user properties
  };
}

@Controller(ROLE_RESOURCES.NOTIFICATION)
export class NotificationsController {
  constructor(
    private readonly notificationsService: NotificationsService,
    private readonly usersService: UsersService
  ) {
  }

  @HttpCode(HttpStatus.OK)
  @Get("admin")
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.NOTIFICATION,
    action: "read",
    possession: "any"
  })
  async getAdminNotifications(
    @Req() req: Request,
    @Query() collectionDto: CollectionDto
  ): Promise<any> {
    const workspace = (req as any).user.workspace;

    return {
      statusCode: HttpStatus.OK,
      message: "Notifications fetched successfully",
      data: await this.notificationsService.getNotificationsForAdmin(workspace._id, collectionDto)
    };
  }

  @HttpCode(HttpStatus.OK)
  @Post("admin/read")
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.NOTIFICATION,
    action: "read",
    possession: "any"
  })
  async readAdminNotifications(
    @Body('notificationIds') notificationIds: string[]
  ): Promise<any> {
    return {
      statusCode: HttpStatus.OK,
      message: "Notifications read successfully",
      data: await this.notificationsService.readNotification(notificationIds)
    };
  }


  @HttpCode(HttpStatus.OK)
  @Get(":userId")
  @UseRoles({
    resource: ROLE_RESOURCES.NOTIFICATION,
    action: "read",
    possession: "any"
  })
  async getNotification(
    @Param("userId") userId: string
  ): Promise<any> {
    return {
      statusCode: HttpStatus.OK,
      message: "Notification fetched successfully",
      data: await this.notificationsService.getNotificationsByUserId(userId)
    };
  }
}
