import { Test, TestingModule } from '@nestjs/testing';
import { BookingAddonService } from './booking-addon.service';

describe('BookingAddonService', () => {
  let service: BookingAddonService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [BookingAddonService],
    }).compile();

    service = module.get<BookingAddonService>(BookingAddonService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
