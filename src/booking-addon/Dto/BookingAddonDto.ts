import { ApiProperty } from "@nestjs/swagger";
import {IsEnum, IsNotEmpty, IsNumber, IsOptional, IsString, ValidateNested} from "class-validator";
import {Type} from "class-transformer";


class RangeType {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  readonly name: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  readonly price: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  readonly duration: number;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  readonly order: number;
}

export default class BookingAddonDto{
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  readonly name: string;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  readonly quantityMaximum: number;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  readonly quantityMinimum: number;

  @ApiProperty()
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => RangeType)
  readonly range : RangeType[];

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  readonly order: number;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  readonly duration: number;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  readonly price: number;

  @ApiProperty()
  @IsOptional()
  @IsString()
  readonly icon: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsEnum(['quality', 'range'])
  readonly typed: 'quality' | 'range';

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  readonly workspace: string;
}