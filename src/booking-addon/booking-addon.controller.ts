import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Patch,
  Post,
  Query, Req,
  UseGuards,
} from '@nestjs/common';
import { BookingAddonService } from './booking-addon.service';
import UpdateBookingAddonDto from './Dto/UpdateBookingAddonDto';
import BookingAddonDto from './Dto/BookingAddonDto';
import { AuthGuard } from '../auth/auth.guard';
import { ACGuard, UseRoles } from 'nest-access-control';
import ROLE_RESOURCES from '../@core/constants/roles.resources';
import { ApiTags } from '@nestjs/swagger';

@Controller(ROLE_RESOURCES.BOOKING_ADDON)
@ApiTags('Booking-addon')
export class BookingAddonController {
  constructor(private bookingAddonService: BookingAddonService) {}

  @HttpCode(HttpStatus.OK)
  @Get('/')
  async getBookingAddon(
    @Query('workspace') workspace_param: string,
    @Body('workspace') workspace_body: string,
  ) {
    const workspace = workspace_param || workspace_body;

    return {
      statusCode: HttpStatus.OK,
      data: await this.bookingAddonService.getBookingAddons(workspace),
      message: 'Booking Addon get successfully',
    };
  }

  @HttpCode(HttpStatus.OK)
  @Get('/listing')
  async getBookingAddonListing(
    @Query('list') list: string,
    @Query('workspace') workspace_param: string,
  ) {
    const workspace = workspace_param;
    // @ts-ignore
    const addonsArr = list.split(',');
    return await this.bookingAddonService.getManyBookingAddons(
      addonsArr,
      workspace,
    );
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.BOOKING_ADDON,
    action: 'read',
    possession: 'any',
  })
  @Get(':id')
  async getBookingAddonById(
    @Param('id') id: string,
    @Body('workspace') workspace: string,
  ) {
    return this.bookingAddonService.getBookingAddon(id, workspace);
  }

  @HttpCode(HttpStatus.OK)
  @Get('type/:typed')
  async getBookingAddonByType(
    @Param('typed') typed: string,
    @Body('workspace') workspace: string,
  ) {
    return this.bookingAddonService.getBookingAddonByType(typed, workspace);
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.BOOKING_ADDON,
    action: 'create',
    possession: 'any',
  })
  @Post('create')
  async createBookingAddon(
    @Body()
    bookingAddon: BookingAddonDto,
  ) {
    return this.bookingAddonService.createBookingAddon(bookingAddon);
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.BOOKING_ADDON,
    action: 'create',
    possession: 'own',
  })
  @Post('create/multiple')
  async createMultipleBookingAddon(
      @Req() req: Request,
      @Body()
          bookingAddon: BookingAddonDto[],
  ) {
    //@ts-ignore
    const workspace = req.user.workspace
    return this.bookingAddonService.createMultipleBookingAddon(bookingAddon,workspace);
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.BOOKING_ADDON,
    action: 'delete',
    possession: 'any',
  })
  @Patch(':id')
  async updateBookingAddon(
    @Param('id') id: string,
    bookingAddon: UpdateBookingAddonDto,
  ) {
    return this.bookingAddonService.updateBookingAddon(id, bookingAddon);
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.BOOKING_ADDON,
    action: 'delete',
    possession: 'any',
  })
  @Delete(':id')
  async deleteBookingAddon(
    @Param('id') id: string,
    @Body('workspace') workspace: string,
  ) {
    return this.bookingAddonService.deleteBookingAddon(id, workspace);
  }
}
