import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema, Types } from "mongoose";
import { Workspace } from "../workspace/workspace.schema";
import { Type } from "class-transformer";

export interface RangeType {
  name: string;
  price: number;
  duration: number;
  order?: number;
}

@Schema()
export class BookingAddon {
  _id: MongooseSchema.Types.ObjectId;

  @Prop({ default: true })
  name: string;

  @Prop({ optional: true })
  quantityMaximum: number;

  @Prop({ optional: true })
  quantityMinimum: number;

  @Prop({ optional: true })
  range : Array<RangeType>;

  @Prop({ optional: true})
  order: number;

  @Prop({ optional: true})
  icon: string;

  @Prop({ optional: true})
  duration: number;

  @Prop({ optional: true})
  price: number;

  @Prop({ default: true})
  typed: 'quality' | 'range';

  @Prop({ required: true, type: Types.ObjectId, ref: Workspace.name })
  @Type(() => Workspace)
  workspace: Workspace;
}

export type BookingAddonDocument = BookingAddon & Document;
export const BookingAddonSchema = SchemaFactory.createForClass(BookingAddon);