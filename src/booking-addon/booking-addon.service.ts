import {
    HttpException,
    HttpStatus,
    Inject,
    Injectable,
    forwardRef,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { BookingAddon, BookingAddonDocument } from './booking-addon.schema';
import mongoose, { Model } from 'mongoose';
import UpdateBookingAddonDto from './Dto/UpdateBookingAddonDto';
import { MediasService } from '../medias/medias.service';
import BookingAddonDto from './Dto/BookingAddonDto';
import { BookingTypes, BookingTypesDocument } from '../booking-types/booking-types.schema';

@Injectable()
export class BookingAddonService {
    constructor(
        @Inject(forwardRef(() => MediasService))
        private mediasService: MediasService,
        @InjectModel(BookingTypes.name)
        private bookingTypeModel: Model<BookingTypesDocument>,
        @InjectModel(BookingAddon.name)
        private bookingAddonModel: Model<BookingAddonDocument>,
    ) {}

    async getBookingAddons(workspace: string): Promise<any> {
        return await this.bookingAddonModel
            .find({ workspace: new mongoose.Types.ObjectId(workspace) })
            .exec();
    }

    async getBookingAddon(id: string, workspace: string): Promise<any> {
        const checkExist = await this.bookingAddonModel
            .findOne({
                _id: new mongoose.Types.ObjectId(id),
                workspace: new mongoose.Types.ObjectId(workspace),
            })
            .exec();

        if (!checkExist) {
            throw new HttpException(
                'Data Booking not existed',
                HttpStatus.CONFLICT,
            );
        }

        return {
            statusCode: HttpStatus.OK,
            message: 'Booking Addon get successfully',
            data: checkExist,
        };
    }

    async getBookingAddonByType(
        typed: string,
        workspace: string,
    ): Promise<any> {
        const checkExist = await this.bookingAddonModel
            .find({
                typed: typed,
                workspace: new mongoose.Types.ObjectId(workspace),
            })
            .exec();
        if (!checkExist?.length) {
            throw new HttpException(
                'Data Booking not existed',
                HttpStatus.CONFLICT,
            );
        }
        return {
            statusCode: HttpStatus.OK,
            message: 'Booking Addon get by type successfully',
            data: checkExist,
        };
    }

    async createBookingAddon(bookingAddon: BookingAddonDto): Promise<any> {
        const bookingAddonCreated = await this.bookingAddonModel.create({
            ...bookingAddon,
            workspace: new mongoose.Types.ObjectId(bookingAddon.workspace),
        });
        return {
            statusCode: 200,
            message: 'Booking Addon added successfully',
            data: {
                bookingAddonCreated,
            },
        };
    }

    async createMultipleBookingAddon(bookingAddon: BookingAddonDto[],workspace: mongoose.Types.ObjectId): Promise<any> {

        // Create details for booking type need to key in prices
        const bookingAddonCreated = await this.bookingAddonModel.insertMany(
            bookingAddon.map((item) => ({
                ...item,
                workspace,
            })),
        );

        // Create details for booking type for dynamic total price
        const bookingAddonCreatedDynamic = await this.bookingAddonModel.insertMany(
            bookingAddon.map((item) => ({
                ...item,
                price: 0,
                duration: 0,
                workspace,
            })),
        );

        const bookingAddonIds =  bookingAddonCreated.map((item) => item._id)

        const bookingAddonIdsDynamic =  bookingAddonCreatedDynamic.map((item) => item._id)

        const bookingTypeHome = await this.bookingTypeModel.findOne({
            name: 'Home services',
            workspace
        })

        const bookingTypeDynamic = await this.bookingTypeModel.findOne({
            name: 'Commercial services',
            workspace
        })

        await this.bookingTypeModel.updateMany(
            {
                parent:bookingTypeHome._id.toString(),
                workspace
            },
            {
                $push: {
                    addons: {
                        $each: bookingAddonIds,
                    },
                },
            },
        );

        await this.bookingTypeModel.updateMany(
            {
                parent:bookingTypeDynamic._id.toString(),
                workspace
            },
            {
                $push: {
                    addons: {
                        $each: bookingAddonIdsDynamic,
                    },
                },
            },
        );


        return {
            statusCode: 200,
            message: 'Booking Addon added successfully',
            data: {
                bookingAddonCreated,
            },
        };
    }

    async updateBookingAddon(
        id: string,
        bookingAddon: UpdateBookingAddonDto,
    ): Promise<any> {
        const checkExist = await this.bookingAddonModel.findById(id).exec();
        if (!checkExist) {
            throw new HttpException(
                'Data Booking not existed',
                HttpStatus.CONFLICT,
            );
        }
        const data = await this.bookingAddonModel
            .updateOne(
                { _id: id },
                {
                    ...bookingAddon,
                    workspace: new mongoose.Types.ObjectId(
                        bookingAddon.workspace,
                    ),
                },
            )
            .exec();
        return {
            statusCode: HttpStatus.OK,
            message: 'Booking Addon updated successfully',
            data,
        };
    }

    async deleteBookingAddon(id: string, workspace: string): Promise<any> {
        const deleteItem = await this.bookingAddonModel
            .deleteOne({
                _id: id,
                workspace: new mongoose.Types.ObjectId(workspace),
            })
            .exec();
        return {
            statusCode: HttpStatus.OK,
            message: 'Booking Addon deleted successfully',
            data: deleteItem,
        };
    }

    async getManyBookingAddons(ids: string[], workspace: string): Promise<any> {
        if (!ids?.length) {
            throw new HttpException(
                'Data Booking not existed',
                HttpStatus.CONFLICT,
            );
        }

        const dataQuery = await this.bookingAddonModel
            .find({
                _id: {
                    $in: ids,
                },
                workspace: new mongoose.Types.ObjectId(workspace),
            })
            .exec();

        const result = await Promise.all(
            dataQuery.map(async (item) => {
                if (!item.icon) return item;
                const svgContent = await this.mediasService.getMedia(item.icon);
                const data = item.toObject();
                data.icon = await svgContent.Body.transformToString();

                return { ...data };
            }),
        );

        return {
            statusCode: HttpStatus.OK,
            message: 'Booking Addon get many successfully',
            data: result,
        };
    }
}
