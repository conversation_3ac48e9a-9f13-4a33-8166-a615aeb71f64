import { Module, forwardRef } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { BookingAddon, BookingAddonSchema } from './booking-addon.schema';
import { BookingAddonService } from './booking-addon.service';
import { BookingAddonController } from './booking-addon.controller';
import { StorageModule } from '../storage/storage.module';
import { MediasModule } from '../medias/medias.module';
import { Medias, MediasSchema } from '../medias/medias.schema';
import { MediasService } from '../medias/medias.service';
import { UsersModule } from '../users/users.module';
import { ClientsModule } from '../clients/clients.module';
import { BookingTypesModule } from '../booking-types/booking-types.module';
import { BookingTypes, BookingTypesSchema } from '../booking-types/booking-types.schema';

@Module({
    imports: [
        forwardRef(() => ClientsModule),
        forwardRef(() => UsersModule),
        forwardRef(() => StorageModule),
        forwardRef(() => MediasModule),
        forwardRef(() => BookingTypesModule),
        MongooseModule.forFeature([
            { name: Medias.name, schema: MediasSchema },
        ]),
        MongooseModule.forFeature([
            { name: BookingTypes.name, schema: BookingTypesSchema}
        ]),
        MongooseModule.forFeature([
            { name: BookingAddon.name, schema: BookingAddonSchema },
        ]),
    ],
    providers: [BookingAddonService, MediasService],
    exports: [BookingAddonService],
    controllers: [BookingAddonController],
})
export class BookingAddonModule {}
