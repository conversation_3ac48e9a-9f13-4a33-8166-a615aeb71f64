import { IsString, IsOptional, IsObject, IsArray } from 'class-validator';

export class CreateEmailTemplatesDto {
  @IsString()
  name: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsObject()
  design: Record<string, any>; // EmailBuilder.js JSON

  @IsOptional()
  @IsArray()
  tags?: string[];

  @IsOptional()
  @IsString()
  category?: string;

  @IsOptional()
  @IsString()
  userId?: string;
}