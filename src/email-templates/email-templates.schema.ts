import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';
import { EmailTemplatesVersion } from './email-version.schema';
import { Workspace } from "../workspace/workspace.schema";

export type EmailTemplatesDocument = EmailTemplates & Document;

@Schema({
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
})
export class EmailTemplates {
  @Prop({ required: true })
  name: string;

  @Prop({ default: false })
  isArchived: boolean;

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'EmailTemplatesVersion' })
  currentVersionId: MongooseSchema.Types.ObjectId;

  @Prop({ required: true, default: [] })
  tags: string[];

  @Prop({ required: false })
  description: string;

  @Prop({ required: false, type: String, enum: ['booking-reminder', 'booking-confirm', 'quotation', 'invoice','other'], default: 'other' })
  category: string;

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: Workspace.name, required: true, index: true })
  workspaceId: MongooseSchema.Types.ObjectId;

  // Virtual for populating versions
  versions: EmailTemplatesVersion[];
}

export const EmailTemplatesSchema = SchemaFactory.createForClass(EmailTemplates);

// Create virtual for versions
EmailTemplatesSchema.virtual('versions', {
  ref: 'EmailTemplatesVersion',
  localField: '_id',
  foreignField: 'templateId'
});