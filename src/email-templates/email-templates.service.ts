import { BadRequestException, Injectable, NotFoundException } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model, Types } from "mongoose";

import { EmailTemplates, EmailTemplatesDocument } from "./email-templates.schema";
import {
  EmailTemplatesVersion,
  EmailTemplatesVersionDocument,
  EmailTemplatesVersionStatus
} from "./email-version.schema";
import { CreateVersionDto } from "./dto/create-version.dto";
import { CreateEmailTemplatesDto } from "./dto/create-templates.dto";
import { UpdateVersionDto } from "./dto/update-version.dto";

@Injectable()
export class EmailTemplatesService {
  constructor(
    @InjectModel(EmailTemplates.name)
    private emailTemplateModel: Model<EmailTemplatesDocument>,
    @InjectModel(EmailTemplatesVersion.name)
    private versionModel: Model<EmailTemplatesVersionDocument>
  ) {
  }

  async createTemplate(workspaceId: string, dto: CreateEmailTemplatesDto): Promise<EmailTemplates> {
    try {
      // Create the template with workspace ID
      const template = new this.emailTemplateModel({
        name: dto.name,
        description: dto.description,
        tags: dto.tags,
        category: dto.category,
        workspaceId: new Types.ObjectId(workspaceId)
      });
      const savedTemplate = await template.save();

      // Create initial version (draft) with workspace ID
      const version = new this.versionModel({
        templateId: savedTemplate._id,
        workspaceId: new Types.ObjectId(workspaceId),
        versionNumber: 1,
        status: EmailTemplatesVersionStatus.DRAFT,
        design: dto.design,
        createdBy: dto.userId
      });
      const savedVersion = await version.save();

      // Update the template with the current version ID
      savedTemplate.currentVersionId = savedVersion._id;
      await savedTemplate.save();

      return savedTemplate;
    } catch (error) {
      if (error.code === 11000) { // Duplicate key error
        throw new BadRequestException(
          `A template with the name "${dto.name}" already exists in this workspace`
        );
      }
      throw error;
    }
  }

  async getTemplateWithCurrentVersion(workspaceId: string, id: string): Promise<any> {
    const latestVersion = await this.versionModel.findOne({
      templateId: new Types.ObjectId(id),
      workspaceId: new Types.ObjectId(workspaceId)
    }).sort({ versionNumber: -1 }).exec();

    if (!latestVersion) {
      throw new NotFoundException(`No versions found for template with ID ${id}`);
    }

    const template = await this.emailTemplateModel
      .findOne({
        _id: id,
        workspaceId: new Types.ObjectId(workspaceId),
        currentVersionId: latestVersion._id
      })
      .populate("currentVersionId")
      .exec();

    if (!template) {
      throw new NotFoundException(`Template with ID ${id} not found in workspace`);
    }

    return template;
  }

  async getAllTemplatesForWorkspace(workspaceId: string): Promise<EmailTemplates[]> {
    return this.emailTemplateModel.find({
      workspaceId: new Types.ObjectId(workspaceId),
      isArchived: false
    }).exec();
  }

  async createVersion(workspaceId: string, templateId: string, dto: CreateVersionDto): Promise<EmailTemplatesVersion> {
    // Check if template exists in the specified workspace
    const template = await this.emailTemplateModel.findOne({
      _id: templateId,
      workspaceId: new Types.ObjectId(workspaceId)
    });

    if (!template) {
      throw new NotFoundException(`Template with ID ${templateId} not found in workspace`);
    }

    // Get the highest version number for this template
    const latestVersion = await this.versionModel
      .findOne({
        templateId: new Types.ObjectId(templateId),
        workspaceId: new Types.ObjectId(workspaceId)
      })
      .sort({ versionNumber: -1 })
      .exec();

    const newVersionNumber = latestVersion ? latestVersion.versionNumber + 1 : 1;

    // Create new version as draft
    const newVersion = new this.versionModel({
      templateId: new Types.ObjectId(templateId),
      workspaceId: new Types.ObjectId(workspaceId),
      versionNumber: newVersionNumber,
      status: EmailTemplatesVersionStatus.DRAFT,
      design: dto.design,
      metadata: dto.metadata,
      createdBy: dto.userId
    });

    const savedVersion = await newVersion.save();

    // Update the template's current version
    template.currentVersionId = savedVersion._id;
    await template.save();

    return savedVersion;
  }

  async publishVersion(workspaceId: string, versionId: string): Promise<EmailTemplatesVersion> {
    const version = await this.versionModel.findOne({
      _id: versionId,
      workspaceId: new Types.ObjectId(workspaceId)
    });

    if (!version) {
      throw new NotFoundException(`Version with ID ${versionId} not found in workspace`);
    }

    version.status = EmailTemplatesVersionStatus.PUBLISHED;
    version.publishedAt = new Date();

    return version.save();
  }

  async getVersions(workspaceId: string, templateId: string): Promise<EmailTemplatesVersion[]> {
    // First verify the template belongs to this workspace
    const template = await this.emailTemplateModel.findOne({
      _id: templateId,
      workspaceId: new Types.ObjectId(workspaceId)
    });

    if (!template) {
      throw new NotFoundException(`Template with ID ${templateId} not found in workspace`);
    }

    return this.versionModel
      .find({
        templateId: new Types.ObjectId(templateId),
        workspaceId: new Types.ObjectId(workspaceId)
      })
      .sort({ versionNumber: -1 })
      .exec();
  }

  async getVersion(workspaceId: string, versionId: string): Promise<EmailTemplatesVersion> {
    const version = await this.versionModel.findOne({
      _id: versionId,
      workspaceId: new Types.ObjectId(workspaceId)
    });

    if (!version) {
      throw new NotFoundException(`Version with ID ${versionId} not found in workspace`);
    }

    return version;
  }

  async getPublishedVersions(workspaceId: string, templateId: string): Promise<EmailTemplatesVersion[]> {
    const template = await this.emailTemplateModel.findOne({
      _id: templateId,
      workspaceId: new Types.ObjectId(workspaceId)
    });

    if (!template) {
      throw new NotFoundException(`Template with ID ${templateId} not found in workspace`);
    }

    return await this.versionModel.find({
      templateId: new Types.ObjectId(templateId),
      workspaceId: new Types.ObjectId(workspaceId),
      status: EmailTemplatesVersionStatus.PUBLISHED
    })
      .sort({ versionNumber: -1 })
      .exec();

  }

  async getDraftVersions(workspaceId: string, templateId: string): Promise<EmailTemplatesVersion[]> {
    const template = await this.emailTemplateModel.findOne({
      _id: templateId,
      workspaceId: new Types.ObjectId(workspaceId)
    });

    if (!template) {
      throw new NotFoundException(`Template with ID ${templateId} not found in workspace`);
    }

    return this.versionModel
      .find({
        templateId: new Types.ObjectId(templateId),
        workspaceId: new Types.ObjectId(workspaceId),
        status: EmailTemplatesVersionStatus.DRAFT
      })
      .sort({ versionNumber: -1 })
      .exec();
  }

  async updateDraftVersion(
    workspaceId: string,
    versionId: string,
    dto: UpdateVersionDto
  ): Promise<EmailTemplatesVersion> {
    // Find the version and ensure it belongs to the workspace
    const version = await this.versionModel.findOne({
      _id: versionId,
      workspaceId: new Types.ObjectId(workspaceId)
    });

    if (!version) {
      throw new NotFoundException(`Version with ID ${versionId} not found in workspace`);
    }

    if (version.status !== EmailTemplatesVersionStatus.DRAFT) {
      throw new BadRequestException("Only draft versions can be edited");
    }

    version.design = dto.design;

    if (dto.metadata) {
      version.metadata = dto.metadata;
    }

    return version.save();
  }

  async archiveTemplate(workspaceId: string, id: string): Promise<EmailTemplates> {
    const template = await this.emailTemplateModel.findOne({
      _id: id,
      workspaceId: new Types.ObjectId(workspaceId)
    });

    if (!template) {
      throw new NotFoundException(`Template with ID ${id} not found in workspace`);
    }

    template.isArchived = true;
    return template.save();
  }

  // Clone template from one workspace to another
  async cloneTemplateToWorkspace(
    sourceWorkspaceId: string,
    targetWorkspaceId: string,
    templateId: string,
    userId: string
  ): Promise<EmailTemplates> {
    // Get the template from source workspace
    const sourceTemplate = await this.getTemplateWithCurrentVersion(sourceWorkspaceId, templateId);

    if (!sourceTemplate) {
      throw new NotFoundException(`Template with ID ${templateId} not found in source workspace`);
    }

    // Get the current version of the template
    const currentVersion = await this.versionModel.findById(sourceTemplate.currentVersionId);

    if (!currentVersion) {
      throw new NotFoundException(`Current version not found for template ${templateId}`);
    }

    // Create new template in target workspace
    const newTemplate = new this.emailTemplateModel({
      name: `${sourceTemplate.name} (Copied)`,
      description: sourceTemplate.description,
      tags: sourceTemplate.tags,
      category: sourceTemplate.category,
      workspaceId: new Types.ObjectId(targetWorkspaceId)
    });

    const savedTemplate = await newTemplate.save();

    // Create initial version with the design from source
    const newVersion = new this.versionModel({
      templateId: savedTemplate._id,
      workspaceId: new Types.ObjectId(targetWorkspaceId),
      versionNumber: 1,
      status: EmailTemplatesVersionStatus.DRAFT,
      design: currentVersion.design,
      metadata: currentVersion.metadata,
      createdBy: userId
    });

    const savedVersion = await newVersion.save();

    // Update the template's current version
    savedTemplate.currentVersionId = savedVersion._id;
    await savedTemplate.save();

    return savedTemplate;
  }
}