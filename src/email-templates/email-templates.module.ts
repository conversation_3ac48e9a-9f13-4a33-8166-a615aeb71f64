import { forwardRef, Module } from "@nestjs/common";
import { MongooseModule } from "@nestjs/mongoose";
import { UsersModule } from "../users/users.module";
import { ClientsModule } from "../clients/clients.module";
import { EmailTemplatesService } from "./email-templates.service";
import { EmailTemplatesController } from "./email-templates.controller";
import { EmailTemplates, EmailTemplatesSchema } from "./email-templates.schema";
import { EmailTemplatesVersion, EmailTemplatesVersionSchema } from "./email-version.schema";

@Module({
  imports: [
    forwardRef(() => ClientsModule),
    forwardRef(() => UsersModule),
    MongooseModule.forFeature([
      { name:EmailTemplates.name, schema: EmailTemplatesSchema },
      { name:EmailTemplatesVersion.name, schema: EmailTemplatesVersionSchema },
    ]),
  ],
  controllers: [EmailTemplatesController],
  providers: [EmailTemplatesService],
  exports: [EmailTemplatesService]
})
export class EmailTemplatesModule {}
