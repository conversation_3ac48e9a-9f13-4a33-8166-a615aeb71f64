import { Body, Controller, Delete, Get, HttpStatus, Param, Patch, Post, Query, Req, UseGuards } from "@nestjs/common";
import { ApiTags } from "@nestjs/swagger";
import ROLE_RESOURCES from "../@core/constants/roles.resources";
import { EmailTemplatesService } from "./email-templates.service";
import { CreateVersionDto } from "./dto/create-version.dto";
import { CreateEmailTemplatesDto } from "./dto/create-templates.dto";
import { AuthGuard } from "../auth/auth.guard";
import { ACGuard, UseRoles } from "nest-access-control";
import { UpdateVersionDto } from "./dto/update-version.dto";

@Controller(ROLE_RESOURCES.EMAIL_TEMPLATES)
@ApiTags("Email-Templates")
export class EmailTemplatesController {
  constructor(private readonly emailTemplatesService: EmailTemplatesService) {
  }

  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.EMAIL_TEMPLATES,
    action: "create",
    possession: "any"
  })
  @Post()
  async create(
    @Req() req: Request,
    @Body() dto: CreateEmailTemplatesDto
  ) {
    //@ts-ignore
    const workspace = req.user.workspace;
    return {
      statusCode: HttpStatus.OK,
      data: await this.emailTemplatesService.createTemplate(workspace, dto),
      message: "Email template created successfully"
    };
  }

  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.EMAIL_TEMPLATES,
    action: "read",
    possession: "any"
  })
  @Get()
  async findAll(@Req() req: Request) {
    //@ts-ignore
    const workspace = req.user.workspace;
    return {
      statusCode: HttpStatus.OK,
      data: await this.emailTemplatesService.getAllTemplatesForWorkspace(workspace),
      message: "Email templates retrieved successfully"
    };
  }

  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.EMAIL_TEMPLATES,
    action: "read",
    possession: "any"
  })
  @Get(":id")
  async findCurrentOne(
    @Req() req: Request,
    @Param("id") id: string
  ) {
    //@ts-ignore
    const workspace = req.user.workspace;
    return {
      statusCode: HttpStatus.OK,
      data: await this.emailTemplatesService.getTemplateWithCurrentVersion(workspace, id),
      message: "Current email template version retrieved successfully"
    };
  }

  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.EMAIL_TEMPLATES,
    action: "create",
    possession: "any"
  })
  @Post(":id")
  async createVersion(
    @Req() req: Request,
    @Param("id") id: string,
    @Body() dto: CreateVersionDto
  ) {
    //@ts-ignore
    const workspace = req.user.workspace;
    return {
      statusCode: HttpStatus.OK,
      data: await this.emailTemplatesService.createVersion(workspace, id, dto),
      message: "Email template version created successfully"
    };
  }

  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.EMAIL_TEMPLATES,
    action: "update",
    possession: "any"
  })
  @Patch("versions/:id/publish")
  async publishVersion(
    @Req() req: Request,
    @Param("id") id: string
  ) {
    //@ts-ignore
    const workspace = req.user.workspace;
    return {
      statusCode: HttpStatus.OK,
      data: await this.emailTemplatesService.publishVersion(workspace, id),
      message: "Email template version published successfully"
    };
  }

  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.EMAIL_TEMPLATES,
    action: "update",
    possession: "any"
  })
  @Patch('versions/:id')
  async updateVersion(
    @Req() req: Request,
    @Param('id') id: string,
    @Body() dto: UpdateVersionDto
  ) {
    //@ts-ignore
    const workspace = req.user.workspace;
    return {
      statusCode: HttpStatus.OK,
      data: await this.emailTemplatesService.updateDraftVersion(workspace, id, dto),
      message: "Email template version updated successfully"
    }
  }

  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.EMAIL_TEMPLATES,
    action: "read",
    possession: "any"
  })
  @Get(":id/versions")
  async getVersions(
    @Req() req: Request,
    @Param("id") id: string
  ) {
    //@ts-ignore
    const workspace = req.user.workspace;
    return {
      statusCode: HttpStatus.OK,
      data: await this.emailTemplatesService.getVersions(workspace, id),
      message: "Email template versions retrieved successfully"
    };
  }

  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.EMAIL_TEMPLATES,
    action: "read",
    possession: "any"
  })
  @Get(":id/published")
  async getPublishedTemplates(
    @Req() req: Request,
    @Param("id") id: string
  ) {
    //@ts-ignore
    const workspace = req.user.workspace;
    return {
      statusCode: HttpStatus.OK,
      data: await this.emailTemplatesService.getPublishedVersions(workspace, id),
      message: "Published email template version retrieved successfully"
    };
  }

  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.EMAIL_TEMPLATES,
    action: "read",
    possession: "any"
  })
  @Get(":id/draft")
  async getDraftTemplates(
    @Req() req: Request,
    @Param("id") id: string
  ) {
    //@ts-ignore
    const workspace = req.user.workspace;
    return {
      statusCode: HttpStatus.OK,
      data: await this.emailTemplatesService.getDraftVersions(workspace, id),
      message: "Draft email template version retrieved successfully"
    };
  }

  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.EMAIL_TEMPLATES,
    action: "read",
    possession: "any"
  })
  @Get("versions/:id")
  async getVersion(
    @Req() req: Request,
    @Param("id") id: string
  ) {
    //@ts-ignore
    const workspace = req.user.workspace;
    return {
      statusCode: HttpStatus.OK,
      data: await this.emailTemplatesService.getVersion(workspace, id),
      message: "Email template version retrieved successfully"
    };
  }

  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.EMAIL_TEMPLATES,
    action: "create",
    possession: "any"
  })
  @Post(":id/clone-to/:targetWorkspaceId")
  async cloneToWorkspace(
    @Req() req: Request,
    @Param("targetWorkspaceId") targetWorkspaceId: string,
    @Param("id") templateId: string,
    @Body("userId") userId: string
  ) {
    //@ts-ignore
    const workspace = req.user.workspace;
    return {
      statusCode: HttpStatus.OK,
      data: await this.emailTemplatesService.cloneTemplateToWorkspace(
        workspace,
        targetWorkspaceId,
        templateId,
        userId
      ),
      message: "Email template cloned to workspace successfully"
    };
  }

  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.EMAIL_TEMPLATES,
    action: "delete",
    possession: "any"
  })
  @Delete(":id")
  async remove(
    @Req() req: Request,
    @Param("id") id: string
  ) {
    //@ts-ignore
    const workspace = req.user.workspace;
    return {
      statusCode: HttpStatus.OK,
      data: await this.emailTemplatesService.archiveTemplate(workspace, id),
      message: "Email template archived successfully"
    };
  }
}