import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';
import { Users } from "../users/users.schema";
import { EmailTemplates } from "./email-templates.schema";
import { Workspace } from "../workspace/workspace.schema";

export type EmailTemplatesVersionDocument = EmailTemplatesVersion & Document;

export enum EmailTemplatesVersionStatus {
  DRAFT = 'draft',
  PUBLISHED = 'published'
}

@Schema({
  timestamps: true
})
export class EmailTemplatesVersion {
  @Prop({ type: MongooseSchema.Types.ObjectId, ref: EmailTemplates.name, required: true })
  templateId: MongooseSchema.Types.ObjectId;

  @Prop({type: Number, required: true })
  versionNumber: number;

  @Prop({
    type: String,
    enum: Object.values(EmailTemplatesVersionStatus),
    default: EmailTemplatesVersionStatus.DRAFT
  })
  status: EmailTemplatesVersionStatus;

  @Prop({ type: Object, required: true })
  design: Record<string, any>;

  @Prop({ type: Object, required: false })
  metadata: Record<string, any>;

  @Prop({ required: false })
  publishedAt: Date;

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: Users.name, required: false })
  createdBy: MongooseSchema.Types.ObjectId;

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: Users.name, required: false })
  publishedBy: MongooseSchema.Types.ObjectId;

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: Workspace.name, required: true, index: true })
  workspaceId: MongooseSchema.Types.ObjectId;
}

export const EmailTemplatesVersionSchema = SchemaFactory.createForClass(EmailTemplatesVersion);

// Create compound index for templateId and versionNumber
EmailTemplatesVersionSchema.index({ templateId: 1, versionNumber: 1 }, { unique: true });
EmailTemplatesVersionSchema.index({ workspaceId: 1 }, { background: true });