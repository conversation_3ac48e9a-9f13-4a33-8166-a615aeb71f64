import { ApiProperty } from "@nestjs/swagger";
import {
    <PERSON><PERSON>rray,
    IsBoolean, IsDate,
    IsEnum,
    IsNotEmpty,
    IsNumber,
    IsOptional,
    IsString,
    Max,
    Min,
    ValidateNested
} from "class-validator";
import { WeekDay } from "../booking-frequency.types";
import { Type } from "class-transformer";

class WeekdayDto {
    @ApiProperty()
    @IsNotEmpty()
    @IsEnum(WeekDay)
    day: WeekDay;

    @ApiProperty()
    @IsNotEmpty()
    @IsNumber()
    @Min(1)
    week: number;
}

export default class BookingFrequencyDto{
    @ApiProperty()
    @IsNotEmpty()
    @IsString()
    readonly title: string;

    @IsEnum(['once','daily', 'weekly', 'monthly', 'yearly'])
    type: string;

    @ApiProperty()
    @IsNotEmpty()
    readonly discountFrequency: string;

    @IsNumber()
    @Min(1)
    interval: number;

    @IsOptional()
    @IsBoolean()
    onlyWeekdays?: boolean;

    @IsOptional()
    @IsArray()
    @IsEnum(WeekDay, { each: true })
    daysOfWeek?: WeekDay[];

    @IsOptional()
    @IsNumber()
    @Min(1)
    @Max(31)
    dayOfMonth?: number;

    @IsOptional()
    @ValidateNested()
    @Type(() => WeekdayDto)
    weekday?: {
        day: WeekDay;
        week: number;
    };

    @IsOptional()
    @IsNumber()
    @Min(1)
    @Max(12)
    month?: number;

    @IsOptional()
    @IsDate()
    endDate?: Date;
}