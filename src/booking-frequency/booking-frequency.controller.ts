import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post, Query, Req,
  UseGuards,
} from '@nestjs/common';
import { BookingFrequencyService } from './booking-frequency.service';
import BookingFrequencyDto from './Dto/BookingFrequencyDto';
import { Patch } from '@nestjs/common/decorators/http/request-mapping.decorator';
import UpdateBookingFrequencyDto from './Dto/UpdateBookingFrequencyDto';
import { AuthGuard } from '../auth/auth.guard';
import { ACGuard, UseRoles } from 'nest-access-control';
import ROLE_RESOURCES from '../@core/constants/roles.resources';
import { ApiTags } from '@nestjs/swagger';

@Controller(ROLE_RESOURCES.BOOKING_FREQUENCY)
@ApiTags('Booking-frequency')
export class BookingFrequencyController {
  constructor(
    private readonly bookingFrequencyService: BookingFrequencyService,
  ) {}

  @HttpCode(HttpStatus.OK)
  @Get('/')
  async findAll(
      @Query('workspace') workspace: string
  ) {
    console.log('workspace', workspace)
    return {
      statusCode: HttpStatus.OK,
      data: await this.bookingFrequencyService.findAll(workspace),
      message: 'Booking frequency found successfully',
    };
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.BOOKING_FREQUENCY,
    action: 'read',
    possession: 'any',
  })
  @Get('/:id')
  async findOne(
      @Req() req:Request,
      @Param('id') id: string
  ) {

    //@ts-ignore
    const workspace = req.user.workspace;
    const dataQuery = await this.bookingFrequencyService.findOne(id, workspace);

    if (!dataQuery) {
      return {
        statusCode: HttpStatus.NOT_FOUND,
        message: 'Booking frequency not found',
      };
    }

    return {
      statusCode: HttpStatus.OK,
      data: dataQuery,
      message: 'Booking frequency found successfully',
    };
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.BOOKING_FREQUENCY,
    action: 'create',
    possession: 'any',
  })
  @Post('/create')
  async create(
      @Req() req:Request,
      @Body() bookingFrequencyDto: BookingFrequencyDto
  ) {
    //@ts-ignore
    const workspace = req.user.workspace;
    return {
      statusCode: HttpStatus.OK,
      data: await this.bookingFrequencyService.create({
        ...bookingFrequencyDto,
      },workspace),
      message: 'Booking frequency created successfully',
    };
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.BOOKING_FREQUENCY,
    action: 'update',
    possession: 'any',
  })
  @Patch('/:id')
  async update(
    @Param('id') id: string,
    @Req() req:Request,
    @Body() bookingFrequencyDto: UpdateBookingFrequencyDto,
  ) {
    //@ts-ignore
    const workspace = req.user.workspace;
    const dataQuery = await this.bookingFrequencyService.update(
      id,
      bookingFrequencyDto,
      workspace
    );

    return {
      statusCode: HttpStatus.OK,
      data: dataQuery,
      message: 'Booking frequency updated successfully',
    };
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.BOOKING_FREQUENCY,
    action: 'delete',
    possession: 'any',
  })
  @Delete('/:id')
  async delete(
      @Req() req:Request,
      @Param('id') id: string,
  ) {
    //@ts-ignore
    const workspace = req.user.workspace;
    return {
      statusCode: HttpStatus.OK,
      data: await this.bookingFrequencyService.delete(id, workspace),
      message: 'Booking frequency deleted successfully',
    };
  }
}
