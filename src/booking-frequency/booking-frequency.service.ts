import { Injectable } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { BookingFrequency, BookingFrequencyDocument } from "./booking-frequency.schema";
import mongoose, { Model } from "mongoose";
import UpdateBookingFrequencyDto from "./Dto/UpdateBookingFrequencyDto";
import BookingFrequencyDto from "./Dto/BookingFrequencyDto";
import { FrequencyDiscount, FrequencyDiscountDocument } from "../frequency-discount/frequency-discount.schema";

@Injectable()
export class BookingFrequencyService {
  constructor(
    @InjectModel(BookingFrequency.name)
    private bookingFrequencyModel: Model<BookingFrequencyDocument>,
    @InjectModel(FrequencyDiscount.name)
    private frequencyDiscountModel: Model<FrequencyDiscountDocument>,
  ) {}

  async findAll(workspace:string): Promise<BookingFrequency[]> {
    return this.bookingFrequencyModel.find({
      workspace: new mongoose.Types.ObjectId(workspace)
    }).populate('discountFrequency').exec();
  }

  async findOne(id: string,workspace:string): Promise<BookingFrequency> {
    return this.bookingFrequencyModel.findOne({
      _id: new mongoose.Types.ObjectId(id),
      workspace: new mongoose.Types.ObjectId(workspace)
    }).populate('discountFrequency');
  }

  async create(bookingFrequency: BookingFrequencyDto,workspace: mongoose.Types.ObjectId): Promise<BookingFrequency> {
    const discountFrequency = await this.frequencyDiscountModel.findOne({
      _id: new mongoose.Types.ObjectId(bookingFrequency.discountFrequency),
      // workspace: new mongoose.Types.ObjectId(workspace.toString())
    }).exec();
    if (!discountFrequency) {
      throw new Error('Discount frequency not found');
    }
    if (discountFrequency.frequency !== bookingFrequency.type) {
      throw new Error('Discount frequency frequency does not match booking frequency frequency');
    }
    return (await this.bookingFrequencyModel.create({
      ...bookingFrequency,
      workspace: new mongoose.Types.ObjectId(workspace),
      dateCreated: new Date()
    })).populate('discountFrequency');
  }

  async createMany(bookingFrequencies: BookingFrequencyDto[],workspace:string): Promise<BookingFrequency[]> {
    return Promise.all(bookingFrequencies.map(async (bookingFrequency) => {
      return await this.bookingFrequencyModel.create({
        ...bookingFrequency,
        workspace: new mongoose.Types.ObjectId(workspace),
        dateCreated: new Date()
      });
    }))
  }

  async update(id: string, bookingFrequency: UpdateBookingFrequencyDto,workspace:string): Promise<BookingFrequency> {
    return this.bookingFrequencyModel.findByIdAndUpdate(id, {
      ...bookingFrequency,
      workspace: new mongoose.Types.ObjectId(workspace),
    },{ new: true }).populate('discountFrequency');
  }

  async delete(id: string,workspace:string): Promise<BookingFrequency> {
    return this.bookingFrequencyModel.findOneAndRemove({
      _id: new mongoose.Types.ObjectId(id),
      workspace: new mongoose.Types.ObjectId(workspace)
    });
  }
}
