import { Module, forwardRef } from '@nestjs/common';
import { BookingFrequencyService } from './booking-frequency.service';
import { BookingFrequencyController } from './booking-frequency.controller';
import { MongooseModule } from '@nestjs/mongoose';
import {
    BookingFrequency,
    BookingFrequencySchema,
} from './booking-frequency.schema';
import { UsersModule } from '../users/users.module';
import { ClientsModule } from '../clients/clients.module';
import { FrequencyDiscount, FrequencyDiscountSchema } from "../frequency-discount/frequency-discount.schema";
import { FrequencyDiscountModule } from "../frequency-discount/frequency-discount.module";

@Module({
    imports: [
        forwardRef(() => ClientsModule),
        forwardRef(() => UsersModule),
        forwardRef(() => FrequencyDiscountModule),
        MongooseModule.forFeature([
            { name: BookingFrequency.name, schema: BookingFrequencySchema },
            { name: FrequencyDiscount.name, schema: FrequencyDiscountSchema },
        ]),
    ],
    providers: [BookingFrequencyService],
    exports: [BookingFrequencyService],
    controllers: [BookingFrequencyController],
})
export class BookingFrequencyModule {}
