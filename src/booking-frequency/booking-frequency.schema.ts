import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import { Document, Schema as MongooseSchema, Types } from "mongoose";
import { Workspace } from "../workspace/workspace.schema";
import { Type } from "class-transformer";
import { WeekDay } from "./booking-frequency.types";
import { FrequencyDiscount } from "../frequency-discount/frequency-discount.schema";

@Schema()
export class BookingFrequency{
  _id: MongooseSchema.Types.ObjectId;

  @Prop({ required: true })
  title: string;

  @Prop({ required: false, default: 0 })
  discount: number;

  @Prop({ required: true, type: Types.ObjectId, ref: FrequencyDiscount.name })
  discountFrequency: FrequencyDiscount

  @Prop({ required: true })
  dateCreated: Date;

  @Prop({ required: true, type: Types.ObjectId, ref: Workspace.name })
  @Type(() => Workspace)
  workspace: Workspace;

  @Prop({ required: true })
  type: 'once' | 'daily' | 'weekly' | 'monthly' | 'yearly';

  @Prop({ required: true })
  interval: number;

  @Prop()
  onlyWeekdays?: boolean;

  @Prop({ enum: WeekDay, type: [Number] })
  daysOfWeek?: WeekDay[];

  @Prop()
  dayOfMonth?: number;

  @Prop({
    type: {
      day: Number,
      week: Number,
    }
  })
  weekday?: {
    day: WeekDay;
    week: number;
  };

  @Prop()
  month?: number;
}

export type BookingFrequencyDocument = BookingFrequency & Document

export const BookingFrequencySchema = SchemaFactory.createForClass(BookingFrequency)