// Common enum for days of the week
export enum WeekDay {
  SUNDAY = 0,
  MONDAY = 1,
  TUESDAY = 2,
  WEDNESDAY = 3,
  THURSDAY = 4,
  FRIDAY = 5,
  SATURDAY = 6,
}

// Base interface for all frequency types
export interface BaseFrequency {
  interval: number; // How often it repeats (1 = every time, 2 = every other time, etc.)
  startDate: Date;
  endDate?: Date;
}

export interface OnceFrequency extends BaseFrequency {
  type: "once";
}

// Daily frequency (e.g., every day, every 2 days)
export interface DailyFrequency extends BaseFrequency {
  type: "daily";
  onlyWeekdays?: boolean; // Optional: true = Monday to Friday only
}

// Weekly frequency (e.g., every Monday and Wednesday)
export interface WeeklyFrequency extends BaseFrequency {
  type: "weekly";
  daysOfWeek: WeekDay[]; // Array of days when the meeting occurs
}

// Monthly frequency
export interface MonthlyFrequency extends BaseFrequency {
  type: "monthly";
  // Use either dayOfMonth OR weekday, not both
  dayOfMonth?: number; // 1-31 (e.g., 15th of every month)
  weekday?: {
    day: WeekDay; // Which day (e.g., MONDAY)
    week: number; // Which week (-1 = last, 1-4 = specific week)
  };
}

// Yearly frequency
export interface YearlyFrequency extends BaseFrequency {
  type: "yearly";
  month: number; // 1-12 (January-December)
  // Use either dayOfMonth OR weekday, not both
  dayOfMonth?: number; // 1-31 (e.g., January 15th)
  weekday?: {
    day: WeekDay; // Which day (e.g., MONDAY)
    week: number; // Which week (-1 = last, 1-4 = specific week)
  };
}

// Union type for all frequency types
export type JobFrequency =
  | OnceFrequency
  | DailyFrequency
  | WeeklyFrequency
  | MonthlyFrequency
  | YearlyFrequency;