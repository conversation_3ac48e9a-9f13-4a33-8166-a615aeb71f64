import { registerDecorator, ValidationOptions, ValidationArguments } from 'class-validator';

export function IsNotObject(validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      name: 'isNotObject',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        validate(value: any, args: ValidationArguments) {
          return typeof value !== 'object' || value === null;
        },
        defaultMessage(args: ValidationArguments) {
          return `${args.property} must not be an object`;
        },
      },
    });
  };
}