import {
    CollectionDto,
    Pagination,
} from '@forlagshuset/nestjs-mongoose-paginate';

export function paginateWithCount(query: CollectionDto, count: number) {
    const pagination: Pagination = {
        total: count,
        page: query.page,
        limit: query.limit,
        next:
            (query.page + 1) * query.limit >= count
                ? undefined
                : query.page + 1,
        prev: query.page == 0 ? undefined : query.page - 1,
    };

    return pagination;
}

export function capitalizeFirstLetter(s: string) {
    return s && s[0].toUpperCase() + s.slice(1);
}

export function validateCoordinates(lng: number, lat: number): boolean {
    return !(lng < -180 || lng > 180 || lat < -90 || lat > 90);
}

export function validateGeoJSONPoint(coordinates: any): boolean {
    return (
        Array.isArray(coordinates) &&
        coordinates.length === 2 &&
        typeof coordinates[0] === 'number' &&
        typeof coordinates[1] === 'number'
    );
}
