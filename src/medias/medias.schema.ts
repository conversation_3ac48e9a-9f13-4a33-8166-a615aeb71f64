import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema, Types } from "mongoose";
import { Users } from "../users/users.schema";

@Schema()
export class Medias {
  _id: MongooseSchema.Types.ObjectId;

  @Prop({ required: true })
  mediaId: string;

  @Prop()
  etag: string;

  @Prop({ type: Types.ObjectId, ref: Users.name})
  uploadedBy: Types.ObjectId;
}

export type MediasDocument = Medias & Document;
export const MediasSchema = SchemaFactory.createForClass(Medias);
