import {
  Controller,
  Get,
  Post,
  HttpCode,
  HttpStatus,
  Param,
  Res,
  UseInterceptors,
  UploadedFile,
  Delete,
  BadRequestException,
  Body,
  Query, Req,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { MediasService } from './medias.service';
import { v4 as uuidv4 } from 'uuid';
import { Response } from 'express';
import { Readable } from 'stream';
import { UploadMediaDto } from './Dto/UploadMediaDto';
import { InitiateUploadDto } from './Dto/InitiateUploadDto';
import ROLE_RESOURCES from '../@core/constants/roles.resources';
import { ApiTags } from '@nestjs/swagger';
import { ObjectCannedACL } from '@aws-sdk/client-s3';
import { UsersService } from '../users/users.service';
import jwt from 'jsonwebtoken';


@Controller(ROLE_RESOURCES.MEDIAS)
@ApiTags('Medias')
export class MediasController {
  constructor(
      private mediasService: MediasService,
      private usersService: UsersService
  ) {}

  @HttpCode(HttpStatus.OK)
  @Get(':mediaId')
  async getMedia(
      @Req() req: Request,
      @Param('mediaId') mediaId: string,
      @Param('workspaceId') workspaceId: string,
      @Query('workspaceId') workspaceIdQuery: string,
      @Res() res: Response
  ) {
    try {
      let workspace = workspaceId
      //@ts-ignore
      const token = req.headers?.authorization;

      if (token && !workspace) {
        const decoded = jwt.decode(token);
        const user = await this.usersService.findOne(decoded['id']);
        workspace = user?.workspace?.toString();
      }

      if (workspaceIdQuery) {
        workspace = workspaceIdQuery;
      }

      console.log('workspace',workspace);

      const media = await this.mediasService.getMedia(mediaId,workspace);

      // Set the content type of the response
      const contentType = media.ContentType;
      res.setHeader('Content-Type', contentType);

      const readable = media.Body as Readable;
      // const read = new StreamableFile(readable);
      res.status(200);
      return readable.pipe(res);
      // res.send(`${SPACES_URL}/${key}`);
    } catch (err) {
      console.log(err);
      throw new BadRequestException();
    }
  }

  @HttpCode(HttpStatus.OK)
  @Get('getPreignedUrl/:mediaId')
  async getPreignedUrl(
    @Param('mediaId') mediaId: string,
    @Res() res: Response,
  ) {
    try {
      const s3Params = {
        Bucket: process.env.SPACES_BUCKET_NAME,
        Key: `${process.env.SPACES_FOLDER_NAME}/${mediaId}`,
      };
      const signedUrl = await this.mediasService.getPresignedUrl(s3Params);
      res.json({
        statusCode: HttpStatus.OK,
        message: 'Success',
        data: {
          signedUrl,
        },
      });
    } catch (err) {
      console.log(err);
      throw new BadRequestException();
    }
  }

  @HttpCode(HttpStatus.OK)
  @Delete(':mediaId')
  async deleteMedia(
      @Req() req: Request,
      @Body('workspaceId') workspaceId: string,
      @Param('mediaId') mediaId: string
  ) {
    let workspace = workspaceId
    //@ts-ignore
    const token = req.headers?.authorization;

    if (token && ! workspace) {
      const decoded = jwt.decode(token);
      const user = await this.usersService.findOne(decoded['id']);
      workspace = user?.workspace?.toString();
    }

    return this.mediasService.deleteMedia(mediaId,workspace);
  }

  @HttpCode(HttpStatus.OK)
  @Post()
  @UseInterceptors(FileInterceptor('file'))
  async createMedia(
    @Req() req: Request,
    @Body('workspaceId') workspaceId: string,
    @Body('uploadedBy') uploadedBy: string,
    @UploadedFile() file: Express.Multer.File,
    @Res() res: Response,
  ) {

    let workspace = workspaceId
    //@ts-ignore
    const token = req.headers?.authorization;

    if (token && ! workspace) {
      const decoded = jwt.decode(token);
      const user = await this.usersService.findOne(decoded['id']);
      workspace = user?.workspace?.toString();
    }

    try {
      const key = uuidv4().split('-')[0];
      const upload = await this.mediasService.upload(key, file,file.mimetype,workspace,uploadedBy);

      res.status(201).send({
        statusCode: HttpStatus.OK,
        message: 'Create media successfully',
        data: {
          key,
        },
      });
    } catch (err) {
      console.log(err);
      res.send(500);
    }
  }

  @HttpCode(HttpStatus.OK)
  @Post('initiateUpload')
  async initiateUpload(
    @Res() res: Response,
    @Body() initiateUploadDto: InitiateUploadDto,
  ) {
    try {
      const params = {
        Bucket: process.env.SPACES_BUCKET_NAME,
        Key: `${process.env.SPACES_FOLDER_NAME}/${initiateUploadDto.fileName}`,
        ACL:  ObjectCannedACL.public_read,
      };

      const upload = await this.mediasService.createMultipartUpload(params);
      res.json({
        statusCode: HttpStatus.OK,
        message: 'Initiate upload successfully',
        data: {
          uploadId: upload.UploadId,
        },
      });
    } catch (err) {
      console.error(err);
      res.status(500).json({
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: 'Error initializing upload',
      });
    }
  }

  @HttpCode(HttpStatus.OK)
  @Post('uploadPart')
  @UseInterceptors(FileInterceptor('file'))
  async uploadPart(
    @UploadedFile() file: Express.Multer.File,
    @Query('uploadId') uploadId: string,
    @Body() uploadMediaDto: UploadMediaDto,
    @Res() res: Response,
  ) {
    try {
      const s3Params = {
        Bucket: process.env.SPACES_BUCKET_NAME,
        Key: `${process.env.SPACES_FOLDER_NAME}/${uploadMediaDto.fileName}`,
        Body: file.buffer,
        PartNumber: uploadMediaDto.index + 1,
        UploadId: uploadId,
      };

      await this.mediasService.uploadPart(s3Params);
      return res.json({
        statusCode: HttpStatus.OK,
        message: 'Chunk uploaded successfully',
      });
    } catch (err) {
      console.log(err);
      return res.status(500).json({
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: 'Error uploading chunk',
      });
    }
  }

  @HttpCode(HttpStatus.OK)
  @Post('completeUpload')
  async completeUpload(
    @Query('uploadId') uploadId: string,
    @Query('fileName') fileName: string,
    @Res() res: Response,
  ) {
    try {
      const s3Params = {
        Bucket: process.env.SPACES_BUCKET_NAME,
        Key: `${process.env.SPACES_FOLDER_NAME}/${fileName}`,
        UploadId: uploadId,
      };

      let listPartsData = await this.mediasService.listParts(s3Params);

      const parts = [];
      listPartsData.Parts.forEach((part) => {
        parts.push({
          ETag: part.ETag,
          PartNumber: part.PartNumber,
        });
      });

      const completeS3Params = {
        ...s3Params,
        MultipartUpload: {
          Parts: parts,
        },
      };

      const result = await this.mediasService.completeMultipartUpload(
        completeS3Params,
      );

      return res.json({
        statusCode: HttpStatus.OK,
        message: 'Upload complete',
        data: {
          url: result.Location,
        },
      });
    } catch (err) {
      console.log(err);
      return res.status(500).json({
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: 'Error completing upload',
      });
    }
  }
}
