import {
  CompleteMultipartUploadCommandInput,
  CompleteMultipartUploadCommandOutput,
  CreateMultipartUploadCommandInput,
  CreateMultipartUploadCommandOutput,
  GetObjectCommandOutput,
  ListPartsCommandInput,
  ListPartsCommandOutput,
  PutObjectCommandInput,
  UploadPartCommandInput,
  UploadPartCommandOutput,
} from '@aws-sdk/client-s3';
import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { Model } from 'mongoose';
import { InjectModel } from '@nestjs/mongoose';
import { StorageService } from 'src/storage/services/storage.service';
import { Medias, MediasDocument } from './medias.schema';

@Injectable()
export class MediasService {
  constructor(
    private storageService: StorageService,
    @InjectModel(Medias.name) private mediaModel: Model<MediasDocument>,
  ) {}

  async upload(key: string, file: Express.Multer.File| Buffer, mimetype?:string,workspace?:string, uploadedBy?: string): Promise<any> {
    const uploaded = await this.storageService.upload(key, file,mimetype,workspace);

    return this.mediaModel.create({
      mediaId: key,
      etag: uploaded.ETag,
      uploadedBy: uploadedBy,
    });
  }

  async getMedia(mediaId: string,workspace?:string): Promise<GetObjectCommandOutput> {
    return this.storageService.getMedia(mediaId,workspace);
  }

  async deleteMedia(mediaId: string,workspace?:string): Promise<any> {
    await this.storageService.deleteMedia(mediaId,workspace);
    const deletedMedia = await this.mediaModel
      .findOneAndRemove({ mediaId: mediaId })
      .exec();

    if (!deletedMedia) {
      throw new HttpException('Media not found', HttpStatus.BAD_REQUEST);
    }

    return {
      statusCode: 200,
      message: 'Delete media successfully',
    };
  }

  async createMultipartUpload(
    params: CreateMultipartUploadCommandInput,
  ): Promise<CreateMultipartUploadCommandOutput> {
    return this.storageService.createMultipartUpload(params);
  }

  async uploadPart(
    params: UploadPartCommandInput,
  ): Promise<UploadPartCommandOutput> {
    return this.storageService.uploadPart(params);
  }

  async listParts(
    params: ListPartsCommandInput,
  ): Promise<ListPartsCommandOutput> {
    return this.storageService.listParts(params);
  }

  async completeMultipartUpload(
    params: CompleteMultipartUploadCommandInput,
  ): Promise<CompleteMultipartUploadCommandOutput> {
    return this.storageService.completeMultipartUpload(params);
  }

  async getPresignedUrl(params: PutObjectCommandInput): Promise<string> {
    return this.storageService.getPresignedUrl(params);
  }

  getPublicUrl(mediaId: string): string {
    return this.storageService.getPublicUrl(mediaId);
  }
}
