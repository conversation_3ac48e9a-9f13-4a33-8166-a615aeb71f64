import { Module, forwardRef } from '@nestjs/common';
import { MediasController } from './medias.controller';
import { MediasService } from './medias.service';
import { StorageModule } from 'src/storage/storage.module';
import { MongooseModule } from '@nestjs/mongoose';
import { Medias, MediasSchema } from './medias.schema';
import { StorageService } from 'src/storage/services/storage.service';
import { WorkspaceModule } from '../workspace/workspace.module';
import { UsersModule } from '../users/users.module';

@Module({
    imports: [
        forwardRef(() => StorageModule),
        forwardRef(() => WorkspaceModule),
        forwardRef(() => UsersModule),
        MongooseModule.forFeature([
            { name: Medias.name, schema: MediasSchema },
        ]),
    ],
    controllers: [MediasController],
    exports: [MediasService, StorageService],
    providers: [MediasService, StorageService],
})
export class MediasModule {}
