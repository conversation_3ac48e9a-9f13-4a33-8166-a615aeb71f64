import { <PERSON><PERSON><PERSON><PERSON>, ExecutionContext,  Injectable, NestInterceptor } from "@nestjs/common";
import { map, Observable } from "rxjs";

@Injectable()
export class JobsInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    return next.handle()
      .pipe(map(  (data) => {
        let result = data.data;
        if (result?.length) {
           result = result.map( (item:any) => {
            return {
              _id: item._id,
              firstName: item.bookingRequest?.firstName,
              lastName: item.bookingRequest?.lastName,
              services: {
                name: item.service?.name,
                clientType:{
                  colorPrimary: item.service?.clientType?.colorPrimary,
                  colorSecondary: item.service?.clientType?.colorSecondary,
                },
                icon: item.service?.color,
              },
              status: item.status,
              dateCreated: item.dateCreated,
              workspace: item.workspace,
              frequency: item.frequency,
              clientType: item.service?.clientType?._id,
              address: item.bookingRequest?.address,
              sentDate: item.bookingRequest?.dateCreated,
              suitableDate: item.suitableDate,
              price: item.price,
              hours: item?.hours,
            }
          })
        }
        return {
          ...data,
          data: result
        }
      }))
  }
}
