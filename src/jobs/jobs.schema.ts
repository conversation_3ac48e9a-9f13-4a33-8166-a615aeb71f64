import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, SchemaFactory } from "@nestjs/mongoose";
import { Document, Schema as MongooseSchema, Types } from "mongoose";
import { BookingAddon } from "src/booking-addon/booking-addon.schema";
import { BookingExtra } from "src/booking-extra/booking-extra.schema";
import { BookingFrequency } from "src/booking-frequency/booking-frequency.schema";
import { BookingRequest } from "src/booking-request/booking-request.schema";
import { BookingTypes } from "src/booking-types/booking-types.schema";
import { Users } from "src/users/users.schema";
import { Type } from "class-transformer";
import { Workspace } from "../workspace/workspace.schema";
import { Medias } from "../medias/medias.schema";
import { JobFrequency } from "../booking-frequency/booking-frequency.types";

@Schema()
export class Jobs {
  _id: MongooseSchema.Types.ObjectId;

  @Prop({ required: false, type: Types.ObjectId, ref: Jobs.name })
  parent: Jobs;

  @Prop({ required: true, type: Types.ObjectId, ref: BookingRequest.name })
  bookingRequest: BookingRequest;

  @Prop({ required: true, type: Types.ObjectId, ref: BookingTypes.name })
  service: BookingTypes;

  @Prop({
    type: [{ quantity: { type: Number }, addon: { type: Types.ObjectId, ref: BookingAddon.name } }]
  })
  addons: { quantity: number; addon: BookingAddon }[];

  @Prop({
    type: [{ quantity: { type: Number }, extra: { type: Types.ObjectId, ref: BookingExtra.name } }],
    default: [],
  })
  extras: { quantity: number; extra: BookingExtra }[];

  @Prop({ required: true, type: Types.ObjectId, ref: BookingFrequency.name })
  frequency: BookingFrequency;

  @Prop()
  video: string;

  @Prop()
  attachments: string[];

  @Prop({ type: [Types.ObjectId], ref: Users.name })
  staffs: Users[];

  @Prop({ default: "unassigned", type: String, enum: ["unassigned", "assigned", "process", "completed"] })
  status: string;

  @Prop({ default: "" })
  notes: string;

  @Prop()
  suitableDate: Date;

  @Prop()
  endDate?: Date;

  // TODO: - Add working activities and activities history

  @Prop({ required: true })
  dateCreated: Date;

  @Prop({ required: false })
  dateServiced: Date;

  @Prop({ required: true, type: Types.ObjectId, ref: Workspace.name })
  @Type(() => Workspace)
  workspace: Workspace;

  @Prop({ required: true, default: 0 })
  price: number;

  @Prop({ required: true, default: 0 })
  hours: number;

  @Prop({ required: false, default: null })
  discountCode: string;

  @Prop({
    type: [
      {
        staff: { type: Types.ObjectId, ref: Users.name },
        updatedAt: Date,
        medias: [{ type: Types.ObjectId, ref: Medias.name }],
        beforeAfter: { type: String, enum: ["before", "after"] }
      }
    ]
  })
  staffMedias: { staff: Types.ObjectId; medias: Types.ObjectId[]; beforeAfter: string; updatedAt: Date }[];

  @Prop({
    type: [{
      staff: { type: Types.ObjectId, ref: Users.name },
      updatedAt: Date,
      status: { type: String, enum: ["unassigned", "assigned", "process", "completed"] }
    }]
  })
  staffStatus: { staff: Types.ObjectId; status: string; updatedAt: Date }[];

  @Prop({ required: true, type: Number, default: 0 })
  square_ft: number

  @Prop({ required: false, default: '' })
  rrule: string; // Store the job's RRule string

  @Prop({ type: [Date], default: [] })
  nextOccurrences: Date[]; // Cache next few occurrences

  @Prop({ type: [Date], default: [] })
  excludedDates: Date[]; // For cancelled instances

  @Prop({ default: Date.now })
  lastCalculated: Date;

  @Prop()
  duration: number;

  @Prop()
  timezone?: string;
}

export type JobsDocument = Jobs & Document;

export const JobsSchema = SchemaFactory.createForClass(Jobs);