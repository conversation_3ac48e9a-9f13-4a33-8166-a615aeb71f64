import { TypeTemplate } from "../../@core/constants/enums";
import path from "path";
import { convertParamsInTemplate } from "../../quotes/templates/convertParamsInTemplate";

type ReminderEmailParams = {
  logo
  companyName: string;
  clientName: string;
  hoursUntilService: string;
  serviceTime: string;
  companyEmail: string;
  currentYear: string;
}

export const ReminderEmail = async ({ logo,companyEmail, companyName, clientName, hoursUntilService, serviceTime, currentYear }: ReminderEmailParams): Promise<string> => {
  const public_url = `${process.env.SPACES_URL}/${process.env.SPACES_FOLDER_NAME}/`;
  const template = path.join(
    __dirname,
    "..",
    "..",
    "..",
    "public",
    "templates",
    "reminder.html"
  );

  return await convertParamsInTemplate(
    template,
    {
      company_logo: public_url + logo,
      companyName: companyName,
      clientName: clientName,
      hoursUntilService: hoursUntilService,
      serviceTime: serviceTime,
      companyEmail: companyEmail,
      currentYear: currentYear,
    },
    TypeTemplate.file
  );
};

