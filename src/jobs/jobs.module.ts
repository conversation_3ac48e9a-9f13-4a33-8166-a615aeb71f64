import { <PERSON>du<PERSON>, forwardRef } from '@nestjs/common';
import { <PERSON>s<PERSON>ontroller } from './jobs.controller';
import { JobsService } from './jobs.service';
import { MongooseModule } from '@nestjs/mongoose';
import { Jobs, JobsSchema } from './jobs.schema';
import { BookingFrequencyModule } from '../booking-frequency/booking-frequency.module';
import { ClientsModule } from 'src/clients/clients.module';
import { UsersModule } from '../users/users.module';
import { Users, UsersSchema } from 'src/users/users.schema';
import { RolesModule } from 'src/roles/roles.module';
import {
    ClientData,
    ClientsDataSchema,
} from 'src/clients-data/client-data.schema';
import {
    WorkingHours,
    WorkingHoursSchema,
} from 'src/working-hours/working-hours.schema';
import { ClientTypeModule } from "../client-type/client-type.module";
import { MediasModule } from "../medias/medias.module";
import { <PERSON><PERSON>, MediasSchema } from "../medias/medias.schema";
import { NotificationsModule } from "../notifications/notifications.module";
import { GeneratePdfModule } from "../generate-pdf/generate-pdf.module";
import { QuotesModule } from "../quotes/quotes.module";
import { InvoicesModule } from "../invoices/invoices.module";
import { StorageModule } from '../storage/storage.module';
import { WorkspaceModule } from "../workspace/workspace.module";
import { SmtpConfigModule } from "../smtp-config/smtp-config.module";
import { SendEmailModule } from "../send-email/send-email.module";

@Module({
    imports: [
        forwardRef(() => UsersModule),
        forwardRef(() => MediasModule),
        forwardRef(() => BookingFrequencyModule),
        forwardRef(() => ClientsModule),
        forwardRef(() => RolesModule),
        forwardRef(() => ClientTypeModule),
        forwardRef(() => NotificationsModule),
        forwardRef(() => GeneratePdfModule),
        forwardRef(() => InvoicesModule),
        forwardRef(() => StorageModule),
        forwardRef(() => MediasModule),
        forwardRef(() => WorkspaceModule),
        forwardRef(() => SmtpConfigModule),
        forwardRef(() => SendEmailModule),
        MongooseModule.forFeature([
            { name: Jobs.name, schema: JobsSchema },
            { name: Users.name, schema: UsersSchema },
            { name: Medias.name, schema: MediasSchema },
            { name: ClientData.name, schema: ClientsDataSchema },
            { name: WorkingHours.name, schema: WorkingHoursSchema }
        ])
    ],
    controllers: [JobsController],
    providers: [JobsService],
    exports: [JobsService],
})
export class JobsModule {}
