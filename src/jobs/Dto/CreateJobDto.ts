import { ApiProperty } from "@nestjs/swagger";
import {
  IsDate,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsPositive,
  IsString,
  ValidateNested
} from "class-validator";
import { Type } from "class-transformer";

export class Addons {
  @ApiProperty()
  @IsNotEmpty()
  readonly addon: any;

  @ApiProperty()
  @IsNotEmpty()
  readonly quantity: number;
}

export class Extra{
  @ApiProperty()
  @IsNotEmpty()
  readonly extra: any;

  @ApiProperty()
  @IsNotEmpty()
  readonly quantity: number;
}

export default class CreateJobDto{

  @ApiProperty()
  @IsNotEmpty()
  readonly bookingRequest: any;

  @ApiProperty()
  @IsNotEmpty()
  readonly service: any;

  @ApiProperty()
  @IsNotEmpty()
  @ValidateNested({each: true})
  @Type(() => Addons)
  readonly addons: Addons[];

  @ApiProperty()
  @IsOptional()
  @ValidateNested({each: true})
  @Type(() => Extra)
  readonly extras?: Extra[];

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  readonly frequency: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  readonly video: string

  @ApiProperty()
  @IsOptional()
  @IsString({each: true})
  readonly attachments: string[]

  @ApiProperty()
  @IsOptional()
  @IsString({each: true})
  readonly staffs: string[];

  @ApiProperty()
  @IsOptional()
  @IsString()
  readonly notes: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsDate()
  readonly suitableDate: Date;

  @ApiProperty()
  @IsOptional()
  @IsDate()
  readonly endDate?: Date;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  readonly price: number;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  readonly hours: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  readonly workspace: string;

  @ApiProperty()
  @IsOptional()
  @IsEnum(['unassigned','assigned', 'process', 'completed'])
  readonly status: 'unassigned'|'assigned'|'process'|'completed';

  @ApiProperty()
  @IsOptional()
  @IsString()
  readonly discountCode: string;

  @ApiProperty()
  @IsPositive()
  @IsNumber()
  readonly square_ft: number;

  @ApiProperty()
  @IsPositive()
  @IsNumber()
  readonly duration: number;

  @ApiProperty()
  @IsOptional()
  @IsString()
  readonly timezone?: string;
}