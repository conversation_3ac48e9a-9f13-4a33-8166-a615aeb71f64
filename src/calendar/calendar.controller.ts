import {
    Controller,
    Get,
    HttpCode,
    HttpException,
    HttpStatus,
    Param,
    Query,
    UseGuards,
} from '@nestjs/common';
import { CalendarService } from './calendar.service';
import { ApiTags } from '@nestjs/swagger';
import { ACGuard, UseRoles } from 'nest-access-control';
import ROLE_RESOURCES from 'src/@core/constants/roles.resources';
import { AuthGuard } from 'src/auth/auth.guard';

@Controller('calendar')
@ApiTags('Calendar')
export class CalendarController {
    constructor(private readonly calendarService: CalendarService) {}

    @HttpCode(HttpStatus.OK)
    @UseGuards(AuthGuard, ACGuard)
    @Get('/daily/:date')
    async getDailyCalendar(@Param('date') date: Date) {
        const foundJobs = await this.calendarService.getDailyCalendar(date);
        if (foundJobs.length <= 0) {
            throw new HttpException(
                'Not found any job on this day',
                HttpStatus.BAD_REQUEST,
            );
        }

        return {
            statusCode: HttpStatus.OK,
            message: 'Find daily calendar successfully',
            data: foundJobs,
        };
    }

    @HttpCode(HttpStatus.OK)
    @UseGuards(AuthGuard, ACGuard)
    @Get('/weekly')
    async getWeeklyCalendar(
        @Query('monday') monday: Date,
        @Query('sunday') sunday: Date,
    ) {
        const foundJobs = await this.calendarService.getWeeklyCalendar(
            monday,
            sunday,
        );
        if (foundJobs.length <= 0) {
            throw new HttpException(
                'Not found any job on this week',
                HttpStatus.BAD_REQUEST,
            );
        }

        return {
            statusCode: HttpStatus.OK,
            message: 'Find weekly calendar successfully',
            data: foundJobs,
        };
    }
}
