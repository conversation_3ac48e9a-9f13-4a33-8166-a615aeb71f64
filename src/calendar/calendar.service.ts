import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import {
    WorkingHours,
    WorkingHoursDocument,
} from 'src/working-hours/working-hours.schema';

@Injectable()
export class CalendarService {
    constructor(
        @InjectModel(WorkingHours.name)
        private workingHoursModel: Model<WorkingHoursDocument>,
    ) {}

    async getDailyCalendar(date: Date): Promise<WorkingHours[]> {
        try {
            let totalJobs: number;
            let totalInProcess: number;
            let totalCompleted: number;
            let foundWorkingHours = await this.workingHoursModel
                .aggregate([
                    {
                        $match: {
                            $expr: {
                                $and: [
                                    {
                                        $eq: [
                                            '$suitableDate',
                                            { $toDate: date },
                                        ],
                                    },
                                ],
                            },
                        },
                    },
                    {
                        $lookup: {
                            from: 'jobs',
                            let: { job: '$job' },
                            pipeline: [
                                {
                                    $match: {
                                        $expr: {
                                            $and: [
                                                {
                                                    $eq: [
                                                        '$_id',
                                                        {
                                                            $toObjectId:
                                                                '$$job',
                                                        },
                                                    ],
                                                },
                                            ],
                                        },
                                    },
                                },
                                {
                                    $project: {
                                        staffs: 1,
                                        status: 1,
                                        service: 1,
                                        frequency: 1,
                                        bookingRequest: 1,
                                    },
                                },
                                {
                                    $lookup: {
                                        from: 'bookingfrequencies',
                                        let: {
                                            frequency: '$frequency',
                                        },
                                        pipeline: [
                                            {
                                                $match: {
                                                    $expr: {
                                                        $and: [
                                                            {
                                                                $eq: [
                                                                    '$_id',
                                                                    {
                                                                        $toObjectId:
                                                                            '$$frequency',
                                                                    },
                                                                ],
                                                            },
                                                        ],
                                                    },
                                                },
                                            },
                                            {
                                                $project: {
                                                    title: 1,
                                                },
                                            },
                                        ],
                                        as: 'service_info',
                                    },
                                },
                                {
                                    $lookup: {
                                        from: 'bookingtypes',
                                        let: {
                                            service: '$service',
                                        },
                                        pipeline: [
                                            {
                                                $match: {
                                                    $expr: {
                                                        $and: [
                                                            {
                                                                $eq: [
                                                                    '$_id',
                                                                    {
                                                                        $toObjectId:
                                                                            '$$service',
                                                                    },
                                                                ],
                                                            },
                                                        ],
                                                    },
                                                },
                                            },
                                            {
                                                $project: {
                                                    name: 1,
                                                },
                                            },
                                        ],
                                        as: 'service_info',
                                    },
                                },
                                {
                                    $lookup: {
                                        from: 'bookingrequests',
                                        let: {
                                            bookingRequest: '$bookingRequest',
                                        },
                                        pipeline: [
                                            {
                                                $match: {
                                                    $expr: {
                                                        $and: [
                                                            {
                                                                $eq: [
                                                                    '$_id',
                                                                    {
                                                                        $toObjectId:
                                                                            '$$bookingRequest',
                                                                    },
                                                                ],
                                                            },
                                                        ],
                                                    },
                                                },
                                            },
                                            {
                                                $project: {
                                                    title: 1,
                                                    address: 1,
                                                },
                                            },
                                        ],
                                        as: 'bookingRequest_info',
                                    },
                                },
                                {
                                    $lookup: {
                                        from: 'users',
                                        let: { staffs: '$staffs' },
                                        pipeline: [
                                            {
                                                $match: {
                                                    $expr: {
                                                        $and: [
                                                            {
                                                                $in: [
                                                                    '$_id',
                                                                    '$$staffs',
                                                                ],
                                                            },
                                                        ],
                                                    },
                                                },
                                            },
                                            {
                                                $project: {
                                                    firstName: 1,
                                                    lastName: 1,
                                                    userName: 1,
                                                },
                                            },
                                        ],
                                        as: 'cleaner_info',
                                    },
                                },
                                {
                                    $addFields: {
                                        totalJobs: { $sum: 1 },
                                        totalInProcess: {
                                            $sum: {
                                                $cond: [
                                                    {
                                                        $eq: [
                                                            '$status',
                                                            'process',
                                                        ],
                                                    },
                                                    1,
                                                    0,
                                                ],
                                            },
                                        },
                                        totalInCompleted: {
                                            $sum: {
                                                $cond: [
                                                    {
                                                        $eq: [
                                                            '$status',
                                                            'completed',
                                                        ],
                                                    },
                                                    1,
                                                    0,
                                                ],
                                            },
                                        },
                                    },
                                },
                            ],
                            as: 'job_info',
                        },
                    },
                    {
                        $sort: {
                            start_time: 1,
                        },
                    },
                ])
                .exec();

            for (let job in foundWorkingHours[0]?.job_info) {
                if (totalJobs > 0 && totalInProcess > 0 && totalCompleted > 0)
                    break;
                totalJobs = foundWorkingHours[0]?.job_info[job]?.totalJobs;
                totalInProcess =
                    foundWorkingHours[0]?.job_info[job]?.totalInProcess;
                totalCompleted =
                    foundWorkingHours[0]?.job_info[job]?.totalCompleted;
                foundWorkingHours.push(
                    totalJobs,
                    totalInProcess,
                    totalCompleted,
                );
            }
            return foundWorkingHours;
        } catch (error) {
            throw error;
        }
    }

    async getWeeklyCalendar(
        monday: Date,
        sunday: Date,
    ): Promise<WorkingHours[]> {
        try {
            let totalJobs: number = 0;
            let totalInProcess: number = 0;
            let totalCompleted: number = 0;
            let foundWorkingHours = await this.workingHoursModel
                .aggregate([
                    {
                        $match: {
                            $expr: {
                                $and: [
                                    {
                                        $gte: [
                                            '$suitableDate',
                                            { $toDate: monday },
                                        ],
                                    },
                                    {
                                        $lte: [
                                            '$suitableDate',
                                            { $toDate: sunday },
                                        ],
                                    },
                                ],
                            },
                        },
                    },
                    {
                        $lookup: {
                            from: 'jobs',
                            let: { job: '$job' },
                            pipeline: [
                                {
                                    $match: {
                                        $expr: {
                                            $and: [
                                                {
                                                    $eq: [
                                                        '$_id',
                                                        {
                                                            $toObjectId:
                                                                '$$job',
                                                        },
                                                    ],
                                                },
                                            ],
                                        },
                                    },
                                },
                                {
                                    $project: {
                                        staffs: 1,
                                        status: 1,
                                        service: 1,
                                        frequency: 1,
                                        bookingRequest: 1,
                                    },
                                },
                                {
                                    $lookup: {
                                        from: 'bookingfrequencies',
                                        let: {
                                            frequency: '$frequency',
                                        },
                                        pipeline: [
                                            {
                                                $match: {
                                                    $expr: {
                                                        $and: [
                                                            {
                                                                $eq: [
                                                                    '$_id',
                                                                    {
                                                                        $toObjectId:
                                                                            '$$frequency',
                                                                    },
                                                                ],
                                                            },
                                                        ],
                                                    },
                                                },
                                            },
                                            {
                                                $project: {
                                                    title: 1,
                                                },
                                            },
                                        ],
                                        as: 'service_info',
                                    },
                                },
                                {
                                    $lookup: {
                                        from: 'bookingtypes',
                                        let: {
                                            service: '$service',
                                        },
                                        pipeline: [
                                            {
                                                $match: {
                                                    $expr: {
                                                        $and: [
                                                            {
                                                                $eq: [
                                                                    '$_id',
                                                                    {
                                                                        $toObjectId:
                                                                            '$$service',
                                                                    },
                                                                ],
                                                            },
                                                        ],
                                                    },
                                                },
                                            },
                                            {
                                                $project: {
                                                    name: 1,
                                                },
                                            },
                                        ],
                                        as: 'service_info',
                                    },
                                },
                                {
                                    $lookup: {
                                        from: 'bookingrequests',
                                        let: {
                                            bookingRequest: '$bookingRequest',
                                        },
                                        pipeline: [
                                            {
                                                $match: {
                                                    $expr: {
                                                        $and: [
                                                            {
                                                                $eq: [
                                                                    '$_id',
                                                                    {
                                                                        $toObjectId:
                                                                            '$$bookingRequest',
                                                                    },
                                                                ],
                                                            },
                                                        ],
                                                    },
                                                },
                                            },
                                            {
                                                $project: {
                                                    title: 1,
                                                    address: 1,
                                                },
                                            },
                                        ],
                                        as: 'bookingRequest_info',
                                    },
                                },
                                {
                                    $lookup: {
                                        from: 'users',
                                        let: { staffs: '$staffs' },
                                        pipeline: [
                                            {
                                                $match: {
                                                    $expr: {
                                                        $and: [
                                                            {
                                                                $in: [
                                                                    '$_id',
                                                                    '$$staffs',
                                                                ],
                                                            },
                                                        ],
                                                    },
                                                },
                                            },
                                            {
                                                $project: {
                                                    firstName: 1,
                                                    lastName: 1,
                                                    userName: 1,
                                                },
                                            },
                                        ],
                                        as: 'cleaner_info',
                                    },
                                },
                                {
                                    $addFields: {
                                        totalJobs: { $sum: 1 },
                                        totalInProcess: {
                                            $sum: {
                                                $cond: [
                                                    {
                                                        $eq: [
                                                            '$status',
                                                            'process',
                                                        ],
                                                    },
                                                    1,
                                                    0,
                                                ],
                                            },
                                        },
                                        totalInCompleted: {
                                            $sum: {
                                                $cond: [
                                                    {
                                                        $eq: [
                                                            '$status',
                                                            'completed',
                                                        ],
                                                    },
                                                    1,
                                                    0,
                                                ],
                                            },
                                        },
                                    },
                                },
                            ],
                            as: 'job_info',
                        },
                    },
                    {
                        $sort: {
                            start_time: 1,
                            suitableDate: 1,
                        },
                    },
                ])
                .exec();

            for (let job in foundWorkingHours) {
                totalJobs += foundWorkingHours[job]?.job_info?.totalJobs;
                totalInProcess +=
                    foundWorkingHours[job]?.job_info?.totalInProcess;
                totalCompleted +=
                    foundWorkingHours[job]?.job_info?.totalCompleted;
                foundWorkingHours.push(
                    totalJobs,
                    totalInProcess,
                    totalCompleted,
                );
            }
            return foundWorkingHours;
        } catch (error) {
            throw error;
        }
    }
}
