import { Module, forwardRef } from '@nestjs/common';
import { CalendarService } from './calendar.service';
import { CalendarController } from './calendar.controller';
import {
    WorkingHours,
    WorkingHoursSchema,
} from 'src/working-hours/working-hours.schema';
import { MongooseModule } from '@nestjs/mongoose';
import { UsersModule } from 'src/users/users.module';
import { ClientsModule } from 'src/clients/clients.module';

@Module({
    imports: [
        MongooseModule.forFeature([
            { name: WorkingHours.name, schema: WorkingHoursSchema },
        ]),
        forwardRef(() => UsersModule),
        forwardRef(() => ClientsModule),
    ],
    controllers: [CalendarController],
    providers: [CalendarService],
})
export class CalendarModule {}
