import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { Issues, IssuesDocument } from './issues.schema';
import mongoose, { Model } from 'mongoose';
import { InjectModel } from '@nestjs/mongoose';
import {
  CollectionDto,
  DocumentCollector,
} from '@forlagshuset/nestjs-mongoose-paginate';
import CreateIssueDto from './Dto/CreateIssueDto';
import { ClientsService } from '../clients/clients.service';
import { BookingTypesService } from '../booking-types/booking-types.service';
import UpdateIssueDto from './Dto/UpdateIssueDto';
import { SendEmailService } from '../send-email/send-email.service';
import { NotificationEmailAdminIssue } from '../send-email/send-email.template';

@Injectable()
export class IssuesService {
  constructor(
    private bookingTypeService: BookingTypesService,
    private clientService: ClientsService,
    private sendEmailService: SendEmailService,
    @InjectModel(Issues.name) private issuesModel: Model<IssuesDocument>,
  ) {}

  async findAll(collectionDto: CollectionDto): Promise<any> {
    const collector = new DocumentCollector<IssuesDocument>(this.issuesModel);
    let result = await collector.find(collectionDto);
    let data = result.data?.map(async (item) => {
      let serviceId = item.service;
      if (serviceId) {
        let serviceQuery = await this.bookingTypeService.getBookingType(
          serviceId,
        );
        item.service = serviceQuery.name;
      }
      return item;
    });
    await Promise.all(data);
    return result;
  }

  async findOne(id: string, workspace: string): Promise<Issues> {
    let item = await this.issuesModel.findOne({
      _id: new mongoose.Types.ObjectId(id),
      workspace: new mongoose.Types.ObjectId(workspace),
    });
    let serviceID = item.service;
    if (serviceID) {
      let serviceQuery = await this.bookingTypeService.getBookingType(
        serviceID,
      );
      item.service = serviceQuery.name;
    }
    if (!item) {
      throw new Error('Data Issues not existed');
    }
    return item;
  }

  async create(issues: CreateIssueDto): Promise<Issues> {
    let client = await this.clientService.getClientByEmail(issues.email);
    if (!client) {
      const bookingType = await this.bookingTypeService.getBookingType(
        issues.service,
      );
      const clientType = bookingType.clientType;
      client = await this.clientService.createClientByIssue(
        issues
      );
      await this.clientService.createClient({
        firstName: issues.firstName,
        lastName: issues.lastName,
        phone: [{ type: 'main', item: issues.phone }],
        email: [{ type: 'main', item: issues.email }],
        // address: {
        //   street_one: issues.address,
        //   street_two: '',
        //   city: '',
        //   state: '',
        //   coordinates: issues.coordinates,
        // },
        password: issues.phone
      },issues.workspace);
    }

    return await this.issuesModel.create({
      title: issues.firstName + ' ' + issues.lastName + '-' + new Date(),
      firstName: issues.firstName,
      lastName: issues.lastName,
      phone: issues.phone,
      email: issues.email,
      address: issues.address,
      clientId: client._id,
      video: issues?.video,
      attachments: issues?.attachments,
      service: issues?.service,
      jobRelated: issues?.jobRelated,
      status: issues?.status || 'pending',
      workspace: new mongoose.Types.ObjectId(issues.workspace),
      dateCreated: new Date(),
    });
  }

  async update(id: string, issues: UpdateIssueDto): Promise<Issues> {
    const checkExist = await this.issuesModel
      .findOne({
        _id: new mongoose.Types.ObjectId(id),
        workspace: new mongoose.Types.ObjectId(issues.workspace),
      })
      .exec();
    if (!checkExist) {
      throw new HttpException('Data Issue not existed', HttpStatus.CONFLICT);
    }

    const bodyContent = NotificationEmailAdminIssue({
      _id: id,
    });

    const handleSendEmail = async () => {
      if (!checkExist?.attachments?.length) {
        await this.sendEmailService.sendEmail({
          userID: process.env.ADMIN_USER_ID,
          fromEmailName: 'booking',
          subject:
            'Issue created - ' +
            checkExist.firstName +
            ' ' +
            checkExist.lastName +
            '-' +
            new Date(),
          body: bodyContent,
          workspace: issues.workspace,
        });
      }
    };

    const handling = await Promise.all([
      handleSendEmail(),
      this.issuesModel.findByIdAndUpdate(id, {
        ...issues,
        workspace: new mongoose.Types.ObjectId(issues.workspace),
      }),
    ]);

    return handling[1];
  }

  async delete(id: string, workspace: string): Promise<Issues> {
    return this.issuesModel.findOneAndRemove({
      _id: new mongoose.Types.ObjectId(id),
      workspace: new mongoose.Types.ObjectId(workspace),
    });
  }
}
