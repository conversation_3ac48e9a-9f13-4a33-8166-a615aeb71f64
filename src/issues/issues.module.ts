import { Module, forwardRef } from '@nestjs/common';
import { IssuesController } from './issues.controller';
import { IssuesService } from './issues.service';
import { MongooseModule } from '@nestjs/mongoose';
import { Issues, IssuesSchema } from './issues.schema';
import { SendEmailModule } from '../send-email/send-email.module';
import { BookingTypesModule } from '../booking-types/booking-types.module';
import { ClientsModule } from '../clients/clients.module';
import { StorageModule } from '../storage/storage.module';
import { MediasModule } from '../medias/medias.module';
import { ClientTypeModule } from '../client-type/client-type.module';
import { UsersModule } from '../users/users.module';

@Module({
    imports: [
        forwardRef(() => UsersModule),
        forwardRef(() => StorageModule),
        forwardRef(() => MediasModule),
        forwardRef(() => ClientTypeModule),
        forwardRef(() => SendEmailModule),
        forwardRef(() => ClientsModule),
        forwardRef(() => BookingTypesModule),
        MongooseModule.forFeature([
            { name: Issues.name, schema: IssuesSchema },
        ]),
    ],
    providers: [IssuesService],
    exports: [IssuesService],
    controllers: [IssuesController],
})
export class IssuesModule {}
