import {
    CollectionProperties,
    Expose,
} from '@forlagshuset/nestjs-mongoose-paginate';

export class IssuesCollectionProperties extends CollectionProperties {
    @Expose({ name: 'createdAt', default: true, sortable: true})
    readonly dateCreated: 'desc' | 'asc';
    //
    @Expose({ name: 'status', filterable: true, sortable: true })
    readonly status: string;
    // readonly created_at: 'desc' | 'asc';
    //
    readonly unsortable: string;
}
