import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema, Types } from "mongoose";
import { Workspace } from "../workspace/workspace.schema";
import { Type } from "class-transformer";

export enum STATUS {
  pending = 'pending',
  approved = 'approved',
  rejected = 'rejected'
}

@Schema()
export class Issues {
  _id: MongooseSchema.Types.ObjectId;

  @Prop({ required: true })
  title: string;

  @Prop({ required: true })
  clientId: string;

  @Prop({ required: true })
  firstName: string;

  @Prop({ required: true })
  lastName: string;

  @Prop({ required: true })
  phone: string;

  @Prop({ required: true })
  email: string;

  @Prop({ required: true })
  address: string;

  @Prop()
  video: string

  @Prop()
  attachments: string[];

  @Prop({ required: true })
  service: string

  @Prop({ default: '' })
  jobRelated: string;

  @Prop({ default: STATUS.pending, enum: STATUS })
  status: string;

  @Prop({ required: true })
  dateCreated: Date;

  @Prop({ required: true })
  dateServiced: Date;

  @Prop({ required: true, type: Types.ObjectId, ref: Workspace.name })
  @Type(() => Workspace)
  workspace: Workspace;
}

export type IssuesDocument = Issues & Document

export const IssuesSchema = SchemaFactory.createForClass(Issues)