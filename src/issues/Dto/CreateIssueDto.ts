import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty, IsOptional } from "class-validator";

export default class CreateIssueDto{

  @ApiProperty()
  @IsNotEmpty()
  firstName: string;

  @ApiProperty()
  @IsNotEmpty()
  lastName: string;

  @ApiProperty()
  @IsNotEmpty()
  phone: string;

  @ApiProperty()
  @IsNotEmpty()
  email: string;

  @ApiProperty()
  @IsNotEmpty()
  address: string;

  @ApiProperty()
  @IsOptional()
  video: string

  @ApiProperty()
  @IsOptional()
  attachments: string[]

  @ApiProperty()
  @IsOptional()
  jobRelated: string;

  @ApiProperty()
  @IsNotEmpty()
  service: string;

  @ApiProperty()
  @IsOptional()
  status: 'pending' | 'approved' | 'rejected';

  @ApiProperty()
  @IsOptional()
  workspace: string;

  @ApiProperty()
  @IsNotEmpty()
  coordinates: {
    lat: number;
    lng: number;
  };
}