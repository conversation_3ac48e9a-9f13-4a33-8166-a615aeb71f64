import { ApiProperty } from "@nestjs/swagger";
import { IsOptional } from "class-validator";

export default class UpdateIssueDto{

  @ApiProperty()
  @IsOptional()
  firstName: string;

  @ApiProperty()
  @IsOptional()
  lastName: string;

  @ApiProperty()
  @IsOptional()
  phone: string;

  @ApiProperty()
  @IsOptional()
  email: string;

  @ApiProperty()
  @IsOptional()
  address: string;

  @ApiProperty()
  @IsOptional()
  video: string

  @ApiProperty()
  @IsOptional()
  attachments: string[]

  @ApiProperty()
  @IsOptional()
  jobRelated: string;

  @ApiProperty()
  @IsOptional()
  service: string;

  @ApiProperty()
  @IsOptional()
  status: 'pending' | 'approved' | 'rejected';

  @ApiProperty()
  @IsOptional()
  workspace: string;
}