import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpException,
  HttpStatus,
  Param,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { IssuesService } from './issues.service';
import {
  CollectionDto,
  CollectionResponse,
  ValidationPipe,
} from '@forlagshuset/nestjs-mongoose-paginate';
import { Issues } from './issues.schema';
import CreateIssueDto from './Dto/CreateIssueDto';
import { Patch } from '@nestjs/common/decorators/http/request-mapping.decorator';
import UpdateIssueDto from './Dto/UpdateIssueDto';
import { IssuesCollectionProperties } from './issues.properties';
import mongoose from 'mongoose';
import { AuthGuard } from '../auth/auth.guard';
import { ACGuard, UseRoles } from 'nest-access-control';
import ROLE_RESOURCES from '../@core/constants/roles.resources';
import { ApiTags } from '@nestjs/swagger';

@Controller(ROLE_RESOURCES.ISSUES)
@ApiTags('Issues')
export class IssuesController {
  constructor(private issueService: IssuesService) {}

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.ISSUES,
    action: 'read',
    possession: 'any',
  })
  @Get('/')
  async getIssues(
    @Query(new ValidationPipe(IssuesCollectionProperties))
    collectionDto: CollectionDto,
    @Body('workspace') workspace: string,
  ): Promise<CollectionResponse<Issues>> {
    collectionDto.filter['workspace'] = {
      $eq: new mongoose.Types.ObjectId(workspace),
    };
    return this.issueService.findAll(collectionDto);
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.ISSUES,
    action: 'read',
    possession: 'any',
  })
  @Get(':id')
  async getIssue(
    @Param('id') id: string,
    @Body('workspace') workspace: string,
  ) {
    let issue = await this.issueService.findOne(id, workspace);
    if (!issue) {
      throw new HttpException(
        'Data Issues not existed',
        HttpStatus.BAD_REQUEST,
      );
    }
    return {
      statusCode: HttpStatus.OK,
      message: 'Get issue success',
      data: issue,
    };
  }

  @HttpCode(HttpStatus.OK)
  @Post('/')
  async createIssue(@Body() issue: CreateIssueDto) {
    let itemCreated = await this.issueService.create(issue);
    if (!itemCreated) {
      throw new HttpException('Can not create Issue', HttpStatus.BAD_REQUEST);
    }
    return {
      statusCode: HttpStatus.OK,
      message: 'Create issue successfully',
      data: itemCreated,
    };
  }

  @HttpCode(HttpStatus.OK)
  @Patch('/:id')
  async updateIssue(@Param('id') id: string, @Body() issue: UpdateIssueDto) {
    let itemUpdated = await this.issueService.update(id, issue);
    if (!itemUpdated) {
      throw new HttpException('Can not update Issue', HttpStatus.BAD_REQUEST);
    }

    return {
      statusCode: HttpStatus.OK,
      message: 'Update issue successfully',
      data: itemUpdated,
    };
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.ISSUES,
    action: 'delete',
    possession: 'any',
  })
  @Delete('/:id')
  async deleteIssue(
    @Param('id') id: string,
    @Body('workspace') workspace: string,
  ) {
    let itemDeleted = await this.issueService.delete(id, workspace);
    if (!itemDeleted) {
      throw new HttpException('Can not delete Issue', HttpStatus.BAD_REQUEST);
    }

    return {
      statusCode: HttpStatus.OK,
      message: 'Delete issue successfully',
      data: itemDeleted,
    };
  }
}
