import { ApiProperty } from "@nestjs/swagger";
import {IsEnum, IsNotEmpty, IsNumber, IsOptional, IsString} from "class-validator";

export default class BookingTypesDto{
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  readonly name: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  readonly icon: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  readonly color: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  readonly clientType: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  readonly parent: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  readonly price: number;

  @ApiProperty()
  @IsOptional()
  @IsString()
  readonly unit: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  readonly duration: number;

  @ApiProperty()
  @IsOptional()
  @IsString({each: true})
  readonly addons: string[];

  @ApiProperty()
  @IsOptional()
  @IsString({each: true})
  readonly extras: string[];

  @ApiProperty()
  @IsOptional()
  @IsString()
  readonly conditions: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  readonly workspace: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsEnum(['booking', 'camera','issue'])
  readonly type: 'booking' | 'camera' | 'issue';
}