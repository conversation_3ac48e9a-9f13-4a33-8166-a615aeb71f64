import { Module, forwardRef } from '@nestjs/common';
import { BookingTypesService } from './booking-types.service';
import { MongooseModule } from '@nestjs/mongoose';
import { BookingTypesController } from './booking-types.controller';
import { BookingTypes, BookingTypesSchema } from './booking-types.schema';
import { MediasService } from '../medias/medias.service';
import { MediasModule } from '../medias/medias.module';
import { StorageModule } from '../storage/storage.module';
import { Medias, MediasSchema } from '../medias/medias.schema';
import { UsersModule } from '../users/users.module';
import { ClientsModule } from '../clients/clients.module';

@Module({
    imports: [
        forwardRef(() => ClientsModule),
        forwardRef(() => UsersModule),
        forwardRef(() => StorageModule),
        forwardRef(() => MediasModule),
        MongooseModule.forFeature([
            { name: Medias.name, schema: MediasSchema },
        ]),
        MongooseModule.forFeature([
            { name: BookingTypes.name, schema: BookingTypesSchema },
        ]),
    ],
    providers: [BookingTypesService, MediasService],
    exports: [BookingTypesService],
    controllers: [BookingTypesController],
})
export class BookingTypesModule {}
