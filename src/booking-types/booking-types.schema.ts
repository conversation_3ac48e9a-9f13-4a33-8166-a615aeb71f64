import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, SchemaFactory } from '@nestjs/mongoose';
import mongoose, { Document, Schema as MongooseSchema, Types } from "mongoose";
import {Type} from "class-transformer";
import {ClientTypes} from "../client-type/client-type.schema";
import { Workspace } from "../workspace/workspace.schema";

interface ConditionChild{
  name: string;
  value: string;
}

export interface ConditionObject {
  types: Array<string>;
  params: Array<string>;
  condition: Array<ConditionChild>;
}

@Schema()
export class BookingTypes {
  _id: MongooseSchema.Types.ObjectId;

  @Prop({ required: true })
  name: string;

  @Prop({ required: true })
  icon: string;

  @Prop({ required: false, default: '' })
  color: string;

  @Prop({ type: Types.ObjectId, ref: ClientTypes.name,required: true })
  @Type(()=> ClientTypes )
  clientType: string;

  @Prop({required: false, default: null })
  parent: string;

  @Prop({ required: false,default: 0 })
  price: number;

  @Prop({ required: false, default: 'USD' })
  unit: string;

  @Prop({ required: false, default: 0 })
  duration: number;

  @Prop({ required: false, default: [] })
  addons: Array<string>;

  @Prop({required: false, default: ''})
  conditions: string;

  @Prop({required: false, default: [] })
  extras: Array<string>;

  @Prop({ increament: true })
  order: number;

  @Prop({ required: true })
  type: 'booking' | 'camera';

  @Prop({ required: true, type: Types.ObjectId, ref: Workspace.name })
  @Type(() => Workspace)
  workspace: Workspace;
}

export type BookingTypesDocument = BookingTypes & Document;
export const BookingTypesSchema = SchemaFactory.createForClass(BookingTypes);