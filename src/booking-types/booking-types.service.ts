import { forwardRef, HttpException, HttpStatus, Inject, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import mongoose, { Model } from 'mongoose';
import { BookingTypes, BookingTypesDocument } from './booking-types.schema';
import BookingTypesDto from './Dto/BookingTypeDto';
import UpdateBookingTypeDto from './Dto/UpdateBookingTypeDto';
import { CollectionDto } from '@forlagshuset/nestjs-mongoose-paginate';
import { MediasService } from '../medias/medias.service';

@Injectable()
export class BookingTypesService {
    constructor(
        @Inject(forwardRef(() => MediasService))
        private mediasService: MediasService,
        @InjectModel(BookingTypes.name)
        private bookingTypeModel: Model<BookingTypesDocument>,
    ) {}

    async getBookingTypes(
        collectionDto: CollectionDto & { workspace: string | mongoose.Types.ObjectId },
    ): Promise<any> {
        let query: any = [
            {
                $skip:
                    (collectionDto?.page || 0) * (collectionDto?.limit || 50),
            },
            {
                $limit: collectionDto?.limit || 50,
            },
        ];

        const collection = JSON.parse(collectionDto.filter?.toString() || '{}');

        for (let key in collection) {
            if (collection[key]['$eq'] === 'null') {
                collection[key]['$eq'] = null;
            }
            if (collection[key]['$ne'] === 'null') {
                collection[key]['$ne'] = null;
            }
        }

        if (collectionDto?.workspace) {
            const workspaceId = new mongoose.Types.ObjectId(collectionDto.workspace);

            collection.workspace = { $eq: workspaceId };
        }

        query = [
            {
                $match: collection,
            },
            ...query,
        ];

        return {
            statusCode: HttpStatus.OK,
            data: await this.bookingTypeModel.aggregate(query),
        };
    }

    async getBookingType(id: string): Promise<BookingTypes> {
        return await this.bookingTypeModel
            .findOne({
                _id: new mongoose.Types.ObjectId(id),
            })
            .populate(['clientType'])
            .exec();
    }

    async createBookingType(BookingTypesDto: BookingTypesDto): Promise<any> {
        const bookingType = await this.bookingTypeModel.create({
            ...BookingTypesDto,
            workspace: new mongoose.Types.ObjectId(BookingTypesDto.workspace),
        });
        return {
            statusCode: HttpStatus.OK,
            message: 'Booking Type added successfully',
            data: {
                bookingType,
            },
        };
    }

    async createMultipleBookingTypes(
        BookingTypesDto: BookingTypesDto[],
        workspace: string,
    ): Promise<any>{
        return await this.bookingTypeModel.insertMany(
            BookingTypesDto.map((item) => {
                return {
                    ...item,
                    clientType: new mongoose.Types.ObjectId(item.clientType),
                    workspace: new mongoose.Types.ObjectId(workspace),
                };
            }),
        )
    }


    async updateBookingType(
        id: string,
        BookingTypesDto: UpdateBookingTypeDto,
    ): Promise<any> {
        const checkExist = await this.bookingTypeModel
            .findOne({
                _id: new mongoose.Types.ObjectId(id),
                workspace: new mongoose.Types.ObjectId(
                    BookingTypesDto.workspace,
                ),
            })
            .exec();
        if (!checkExist) {
            throw new HttpException(
                'Data Booking not existed',
                HttpStatus.CONFLICT,
            );
        }
        await this.bookingTypeModel
            .updateOne(
                {
                    _id: new mongoose.Types.ObjectId(id),
                    workspace: new mongoose.Types.ObjectId(
                        BookingTypesDto.workspace,
                    ),
                },
                BookingTypesDto,
                {
                    new: true,
                }
            )
            .exec();
        return {
            statusCode: HttpStatus.OK,
            message: 'Booking Type updated successfully',
        };
    }

    async deleteBookingType(id: string, workspace: string): Promise<any> {
        const deleteItem = await this.bookingTypeModel
            .deleteOne({
                _id: new mongoose.Types.ObjectId(id),
                workspace: new mongoose.Types.ObjectId(workspace),
            })
            .exec();
        return {
            statusCode: HttpStatus.OK,
            message: 'Booking Type deleted successfully',
            data: {
                ...deleteItem,
            },
        };
    }

    async getCommonBookingTypes(workspace: string): Promise<any> {
        const bookingParent = await this.bookingTypeModel
            .find({
                parent: null,
                workspace: new mongoose.Types.ObjectId(workspace),
            })
            .exec();
        const result = await Promise.all(
            bookingParent.map(async (item) => {
                const child = await this.bookingTypeModel
                    .find({
                        parent: {
                            $eq: item.id,
                        },
                    })
                    .exec();
                let parentData = item.toObject();
                const svgContent = await this.mediasService.getMedia(
                    parentData.icon,
                );
                parentData.icon = await svgContent.Body.transformToString();

                const newChild = await Promise.all(
                    child.map(async (childItem) => {
                        const svgContent = await this.mediasService.getMedia(
                            childItem.icon,
                        );
                        const childData = childItem.toObject();
                        childData.icon =
                            await svgContent.Body.transformToString();
                        return { ...childData };
                    }),
                );

                return {
                    ...parentData,
                    child: newChild,
                };
            }),
        );

        return {
            statusCode: HttpStatus.OK,
            data: result,
            message: 'Get common booking types successfully',
        };
    }

    async getBookingTypesByClientType(
        clientType: string,
        workspace: string,
    ): Promise<BookingTypes[]> {
        return await this.bookingTypeModel
            .find({
                parent: {
                    $ne: null,
                },
                clientType: new mongoose.Types.ObjectId(clientType),
                workspace: new mongoose.Types.ObjectId(workspace),
            })
            .exec();
    }
}
