import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Patch,
  Post,
  Query, Req,
  UseGuards,
} from '@nestjs/common';
import { BookingTypesService } from './booking-types.service';
import BookingTypesDto from './Dto/BookingTypeDto';
import UpdateBookingTypeDto from './Dto/UpdateBookingTypeDto';
import { CollectionDto } from '@forlagshuset/nestjs-mongoose-paginate';
import { AuthGuard } from '../auth/auth.guard';
import { ACGuard, UseRoles } from 'nest-access-control';
import ROLE_RESOURCES from '../@core/constants/roles.resources';
import { ApiTags } from '@nestjs/swagger';

@Controller(ROLE_RESOURCES.BOOKING_TYPES)
@ApiTags('Booking-types')
export class BookingTypesController {
  constructor(private bookingTypeService: BookingTypesService) {}

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.BOOKING_TYPES,
    action: 'read',
    possession: 'any',
  })
  @Get('/')
  async getBookingTypes(
    @Req() req: Request,
    @Query()
    collectionDto: CollectionDto & { workspace: string },
  ) {
    //@ts-ignore
    const workspace = req?.user?.workspace || collectionDto?.workspace;

    return this.bookingTypeService.getBookingTypes({
        ...collectionDto,
        workspace,
    });
  }


  @HttpCode(HttpStatus.OK)
  @Get('/common')
  async getBookingTypesCommon(
    @Query('workspace') workspaceParam: string,
    @Body('workspace') workspaceBody: string,
  ) {
    const workspace = workspaceParam || workspaceBody;

    return this.bookingTypeService.getCommonBookingTypes(workspace);
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.BOOKING_TYPES,
    action: 'read',
    possession: 'any',
  })
  @Get(':id')
  async getBookingType(@Param('id') id: string) {
    return this.bookingTypeService.getBookingType(id);
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.BOOKING_TYPES,
    action: 'create',
    possession: 'any',
  })
  @Post('create')
  async createBookingType(@Body() BookingTypesDto: BookingTypesDto) {
    return this.bookingTypeService.createBookingType(BookingTypesDto);
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.BOOKING_TYPES,
    action: 'create',
    possession: 'own',
  })
  @Post('create-multiple')
    async createMultipleBookingTypes(
        @Req () req: Request,
        @Body() BookingTypesDto: BookingTypesDto[],
  ) {
        //@ts-ignore
     const workspace = req.user.workspace
     return {
         statusCode: HttpStatus.OK,
         message: 'Booking Types added successfully',
         data: await this.bookingTypeService.createMultipleBookingTypes(BookingTypesDto, workspace),
     };
  }


  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.BOOKING_TYPES,
    action: 'update',
    possession: 'any',
  })
  @Patch(':id')
  async updateBookingType(
    @Param('id') id: string,
    @Body() BookingTypesDto: UpdateBookingTypeDto,
  ) {
    return this.bookingTypeService.updateBookingType(id, BookingTypesDto);
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.BOOKING_TYPES,
    action: 'delete',
    possession: 'any',
  })
  @Delete(':id')
  async deleteBookingType(
    @Param('id') id: string,
    @Body('workspace') workspace: string,
  ) {
    return this.bookingTypeService.deleteBookingType(id, workspace);
  }
}
