import { <PERSON>p, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema, Types } from 'mongoose';
import { Address } from '../address/address.schema';

export const PaymentTypes = {
  STRIPE: 'stripe',
  OTHER: 'other',
} as const;

export type PaymentType = (typeof PaymentTypes)[keyof typeof PaymentTypes];

@Schema()
export class Workspace extends Document {
  _id: MongooseSchema.Types.ObjectId;

  @Prop({ required: true })
  name: string;

  @Prop({ optional: true })
  description: string;

  @Prop({ required: true })
  workingHours: string[];

  @Prop({ required: true })
  logo: string; // link media database

  @Prop({ required: false, default: null })
  url: string;

  @Prop({ required: false, default: null })
  email: string;

  @Prop({ required: false, default: null })
  phone: string;

  @Prop({ required: true })
  dateCreated: Date;

  @Prop({ required: false, default: false })
  isTrial: boolean;

  @Prop({ required: false, default: null, type: Types.ObjectId, ref: Address.name })
  address: Address;

  @Prop({ required: false })
  payments: [
    {
      type: string;
      publicKey: string;
      secretKey: string;
      webhookSecret: string;
    },
  ];

  @Prop({ required: false, default: true })
  isFirstLogin: boolean;

  @Prop({ required: false, default: null })
  stripeAccountId: string;
}

export type WorkspaceDocument = Workspace & Document;
export const WorkspaceSchema = SchemaFactory.createForClass(Workspace);
