import { Module, forwardRef } from '@nestjs/common';
import { WorkspaceController } from './workspace.controller';
import { WorkspaceService } from './workspace.service';
import { Workspace, WorkspaceSchema } from './workspace.schema';
import { MongooseModule } from '@nestjs/mongoose';
import { UsersModule } from '../users/users.module';
import { ClientsModule } from '../clients/clients.module';
import { BookingRequestModule } from 'src/booking-request/booking-request.module';
import { InvoicesModule } from 'src/invoices/invoices.module';
import { TransactionsModule } from 'src/transactions/transactions.module';
import { BookingFrequencyModule } from '../booking-frequency/booking-frequency.module';
import { FrequencyDiscountModule } from "../frequency-discount/frequency-discount.module";

@Module({
    imports: [
        forwardRef(() => ClientsModule),
        forwardRef(() => UsersModule),
        MongooseModule.forFeature([
            { name: Workspace.name, schema: WorkspaceSchema },
        ]),
        forwardRef(() => BookingRequestModule),
        forwardRef(() => InvoicesModule),
        forwardRef(() => TransactionsModule),
        forwardRef(()=> BookingFrequencyModule),
        forwardRef(()=> FrequencyDiscountModule)
    ],
    controllers: [WorkspaceController],
    providers: [WorkspaceService],
    exports: [WorkspaceService],
})
export class WorkspaceModule {}
