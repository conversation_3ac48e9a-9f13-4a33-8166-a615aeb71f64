import {
  Body,
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Patch,
  Post,
  Query, Req,
  UseGuards,
  ValidationPipe,
} from '@nestjs/common';
import { WorkspaceService } from './workspace.service';
import { CollectionDto } from '@forlagshuset/nestjs-mongoose-paginate';
import { CreateWorkSpaceDto } from './Dto/CreateWorkSpaceDto';
import { UpdateWorkSpaceDto } from './Dto/UpdateWorkSpaceDto';
import { AuthGuard } from '../auth/auth.guard';
import { ACGuard, UseRoles } from 'nest-access-control';
import ROLE_RESOURCES from '../@core/constants/roles.resources';
import { WorkSpacePaymentDto } from './Dto/WorkSpacePaymentDto';
import { PaymentType } from './workspace.schema';
import { SecondAuthGuard } from 'src/auth/second-auth.guard';
import { ApiTags } from '@nestjs/swagger';
import { Request } from 'express';

@Controller(ROLE_RESOURCES.WORKSPACE)
@ApiTags('Workspace')
export class WorkspaceController {
  constructor(private workspaceService: WorkspaceService) {}

  @HttpCode(HttpStatus.OK)
  @Get('/')
  async findAll(
    @Query()
    collectionDto: CollectionDto,
  ) {
    let workspaces = await this.workspaceService.findAll(collectionDto);
    if (!workspaces) {
      return {
        statusCode: HttpStatus.BAD_REQUEST,
        message: 'Can not get workspaces',
        data: null,
      };
    }

    return {
      statusCode: HttpStatus.OK,
      message: 'Get workspaces successfully',
      data: workspaces,
    };
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.WORKSPACE,
    action: 'read',
    possession: 'own',
  })
  @Get('detail')
  async findOne(
      @Req() req: Request,
  ) {
    //@ts-ignore
    const workspace = await this.workspaceService.findOne(req.user.workspace);
    if (!workspace) {
      return {
        statusCode: HttpStatus.BAD_REQUEST,
        message: 'Workspace not existed',
        data: null,
      };
    }

    return {
      statusCode: HttpStatus.OK,
      message: 'Get workspace successfully',
      data: workspace,
    };
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.WORKSPACE,
    action: 'read',
    possession: 'any',
  })
  @Get('working-hours')
  async getWorkingHours(
    @Req() req: Request
  ) {
    //@ts-ignore
    const workspace = req.user.workspace;
    const query = await this.workspaceService.findOne(workspace);
    if (!query) {
      return {
        statusCode: HttpStatus.BAD_REQUEST,
        message: 'Workspace not existed',
        data: null,
      };
    }

    return {
      statusCode: HttpStatus.OK,
      message: 'Get workspace successfully',
      data: {
        workingHours: query.workingHours.pop(),
      },
    };
  }

  @HttpCode(HttpStatus.OK)
  @Post('/')
  async create(@Body() workspace: CreateWorkSpaceDto) {
    let workspaceCreated = await this.workspaceService.create(workspace);
    if (!workspaceCreated) {
      return {
        statusCode: HttpStatus.BAD_REQUEST,
        message: 'Can not create workspace',
        data: null,
      };
    }

    return {
      statusCode: HttpStatus.OK,
      message: 'Create workspace successfully',
      data: workspaceCreated,
    };
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.WORKSPACE,
    action: 'read',
    possession: 'own',
  })
  @Patch('detail')
  async updateDetail(
    @Req() req: Request,
    @Body() workspace: UpdateWorkSpaceDto,
  ) {
    //@ts-ignore
    const workspaceUpdated = await this.workspaceService.update(req.user.workspace, workspace);

    if (!workspaceUpdated) {
      return {
        statusCode: HttpStatus.BAD_REQUEST,
        message: 'Can not update workspace',
      };
    }

    return {
      statusCode: HttpStatus.OK,
      message: 'Update workspace successfully',
      data: workspaceUpdated,
    };
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.WORKSPACE,
    action: 'update',
    possession: 'own',
  })
  @Patch(':id')
  async update(@Param('id') id: string, @Body() workspace: UpdateWorkSpaceDto) {
    const workspaceUpdated = await this.workspaceService.update(id, workspace);

    if (!workspaceUpdated) {
      return {
        statusCode: HttpStatus.BAD_REQUEST,
        message: 'Can not update workspace',
      };
    }

    return {
      statusCode: HttpStatus.OK,
      message: 'Update workspace successfully',
      data: workspaceUpdated,
    };
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.WORKSPACE,
    action: 'update',
    possession: 'own',
  })
  @Patch(':id')
  async delete(@Param('id') id: string) {
    const workspaceDeleted = await this.workspaceService.delete(id);

    if (!workspaceDeleted) {
      return {
        statusCode: HttpStatus.BAD_REQUEST,
        message: 'Can not delete workspace',
      };
    }

    return {
      statusCode: HttpStatus.OK,
      message: 'Delete workspace successfully',
      data: workspaceDeleted,
    };
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(SecondAuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.WORKSPACE,
    action: 'update',
    possession: 'any',
  })
  @Patch('payment/:id')
  async updatePaymentKeys(
    @Param('id') id: string,
    @Body() updateDto: WorkSpacePaymentDto,
  ) {
    const workspaceUpdated = await this.workspaceService.updatePayment(
      id,
      updateDto,
    );

    if (!workspaceUpdated) {
      return {
        statusCode: HttpStatus.BAD_REQUEST,
        message: 'Can not update workspace payment',
      };
    }

    return {
      statusCode: HttpStatus.OK,
      message: 'Update workspace payment successfully',
      data: workspaceUpdated,
    };
  }

  @HttpCode(HttpStatus.OK)
  @Get('/payment/:id')
  async getPaymentKeys(
    @Param('id', new ValidationPipe()) id: string,
    @Query('type') type: PaymentType,
  ) {
    const foundWorkspacePayment = await this.workspaceService.findPaymentKeys(
      id,
      type,
    );

    if (!foundWorkspacePayment)
      return {
        statusCode: HttpStatus.BAD_REQUEST,
        message: 'Workspace not found!',
      };

    return {
      statusCode: HttpStatus.OK,
      message: 'Found workspace payment successfully',
      data: foundWorkspacePayment,
    };
  }

}
