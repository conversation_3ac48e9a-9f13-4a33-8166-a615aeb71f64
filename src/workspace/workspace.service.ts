import {
    Inject,
    Injectable,
    forwardRef,
} from '@nestjs/common';
import { PaymentType, Workspace } from './workspace.schema';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import {
    CollectionDto,
    DocumentCollector,
} from '@forlagshuset/nestjs-mongoose-paginate';
import { CreateWorkSpaceDto } from './Dto/CreateWorkSpaceDto';
import { UpdateWorkSpaceDto } from './Dto/UpdateWorkSpaceDto';
import { WorkSpacePaymentDto } from './Dto/WorkSpacePaymentDto';
import { BookingFrequencyService } from '../booking-frequency/booking-frequency.service';
import { FrequencyDiscountService } from "../frequency-discount/frequency-discount.service";

@Injectable()
export class WorkspaceService {
    constructor(
        @InjectModel(Workspace.name) private workspaceModel: Model<Workspace>,
        @Inject(forwardRef(() => BookingFrequencyService))
        private bookingFrequencyService: BookingFrequencyService,
        @Inject(forwardRef(() => FrequencyDiscountService))
        private frequencyDiscountService: FrequencyDiscountService,
    ) {}

    async findAll(collectionDto: CollectionDto): Promise<any> {
        const collector = new DocumentCollector<Workspace>(this.workspaceModel);
        return collector.find(collectionDto);
    }

    async findOne(id: string): Promise<Workspace> {
        return this.workspaceModel.findById(id).exec();
    }

    async findByStripeAccountId(stripeAccountId:string): Promise<Workspace> {
        return this.workspaceModel.findOne({
            stripeAccountId: stripeAccountId
        }).exec();
    }

    async create(workspace: CreateWorkSpaceDto): Promise<Workspace> {
        const dummyFrequencies = [
            {
                "title":"Daily",
                "type": "daily",
                "interval": 1,
            },
            {
                "title":"Bi-weekly",
                "type": "weekly",
                "interval": 2,
            },
            {
                "title":"Monthly",
                "type": "monthly",
                "interval": 1,
            }
        ]
        const result = await this.workspaceModel.create({
            ...workspace,
            dateCreated: new Date(),
        });
        const dummyDiscountFreq = [
            {
                "frequency": "daily" as const,
                "discount": 10,
                "workspace": result._id.toString(),
            },
            {
                "frequency": "weekly" as const,
                "discount": 10,
                "workspace": result._id.toString(),
            },
            {
                "frequency": "monthly" as const,
                "discount": 10,
                "workspace": result._id.toString(),
            }
        ];
        const discountFreqResult = await this.frequencyDiscountService.createMany(dummyDiscountFreq);
        const newDummyFrequencies = dummyFrequencies.map((frequency,index) => {
            return {
                ...frequency,
                discountFrequency: discountFreqResult[index]._id.toString()
            }
        });
        await this.bookingFrequencyService.createMany(newDummyFrequencies, result._id.toString());
        return result;
    }

    async update(
        id: string,
        workspace: UpdateWorkSpaceDto,
    ): Promise<Workspace> {
        return this.workspaceModel
            .findByIdAndUpdate(
                id,
                {
                    ...workspace,
                },
                {
                    new: true,
                },
            )
            .lean()
            .exec();
    }

    async delete(id: string): Promise<Workspace> {
        return this.workspaceModel.findByIdAndDelete(id).lean().exec();
    }

    async updatePayment(
        id: string,
        updateDto: WorkSpacePaymentDto,
    ): Promise<Workspace> {
        const workspace = await this.workspaceModel.findById(id).exec();
        if (workspace) {
            const index = workspace.payments.findIndex(payment => payment.type === updateDto.type);

            if (index !== -1) { // update existing item
                workspace.payments[index] = updateDto;
            } else { // add new item
                workspace.payments.push(updateDto);
            }

            return workspace.save();
        }
    }

    async findPaymentKeys(
        id: string,
        paymentType: PaymentType,
    ): Promise<WorkSpacePaymentDto> {
        let result: any;
        const found = (await this.workspaceModel.findById(id)).payments;
        for (let i = 0; i < found.length; i++) {
            if (found[i].type === paymentType) result = found[i];
            break;
        }
        return result;
    }

}
