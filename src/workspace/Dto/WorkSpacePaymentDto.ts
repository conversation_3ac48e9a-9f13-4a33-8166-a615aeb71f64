import { ApiProperty } from '@nestjs/swagger';
import { IsString } from 'class-validator';
import { PaymentType, PaymentTypes } from '../workspace.schema';

export class WorkSpacePaymentDto {
  @ApiProperty({ type: String, example: 'stripe', enum: PaymentTypes })
  @IsString()
  readonly type: PaymentType;

  @ApiProperty({ type: String })
  @IsString()
  readonly publicKey: string;

  @ApiProperty({ type: String })
  @IsString()
  readonly secretKey: string;

  @ApiProperty({ type: String })
  @IsString()
  readonly webhookSecret : string;
}
