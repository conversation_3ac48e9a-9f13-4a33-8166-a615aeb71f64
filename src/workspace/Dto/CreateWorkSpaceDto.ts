import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty, IsOptional, IsString } from "class-validator";


export class CreateWorkSpaceDto{

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  readonly name: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  readonly description?: string;

  @ApiProperty()
  @IsOptional()
  @IsString({each: true})
  readonly workingHours?: string[];

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  readonly logo: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  readonly stripeAccountId: string;
}