import { PartialType } from "@nestjs/mapped-types";
import { CreateWorkSpaceDto } from "./CreateWorkSpaceDto";
import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsNotEmpty, IsOptional, IsString, IsUrl } from 'class-validator';


class Address {
    @ApiProperty()
    @IsString()
    readonly street_one: string;

    @ApiProperty()
    @IsOptional()
    readonly street_two?: string;

    @ApiProperty()
    @IsString()
    readonly city: string;

    @ApiProperty()
    @IsString()
    readonly state: string;

    @ApiProperty()
    @IsOptional()
    readonly zip?: string;

    @ApiProperty()
    @IsNotEmpty()
    readonly coordinates: {
        lat: number;
        lng: number;
    };
}

export class UpdateWorkSpaceDto extends PartialType(CreateWorkSpaceDto){
    @ApiProperty()
    @IsString()
    @IsOptional()
    readonly stripeAccountId: string;

    @ApiProperty()
    @IsUrl()
    @IsOptional()
    readonly url: string;

    @ApiProperty()
    @IsEmail()
    @IsOptional()
    readonly email: string;

    @ApiProperty()
    @IsString()
    @IsOptional()
    readonly phone: string;

    @ApiProperty()
    @IsOptional()
    readonly address: Address;

    @ApiProperty()
    @IsOptional()
    readonly isTrial: boolean;

    @ApiProperty()
    @IsOptional()
    readonly isFirstLogin: boolean;
}