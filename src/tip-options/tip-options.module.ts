import { Module, forwardRef } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { TipOptionsController } from './tip-options.controller';
import { TipOptionsService } from './tip-options.service';
import { TipOption, TipOptionSchema } from './tip-options.schema';
import { TipOptionsSeeder } from './tip-options.seeder';
import { UsersModule } from '../users/users.module';
import { RolesModule } from '../roles/roles.module';
import { ClientsModule } from '../clients/clients.module';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: TipOption.name, schema: TipOptionSchema }]),
    forwardRef(() => UsersModule),
    forwardRef(() => RolesModule),
    forwardRef(() => ClientsModule),
  ],
  controllers: [TipOptionsController],
  providers: [TipOptionsService, TipOptionsSeeder],
  exports: [TipOptionsService]
})
export class TipOptionsModule {} 