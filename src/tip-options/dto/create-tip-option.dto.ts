import { IsNotEmpty, IsNumber, IsString, IsEnum, IsBoolean, IsOptional } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { TipType } from '../tip-options.schema';

export class CreateTipOptionDto {
  @ApiProperty({ description: 'The label to display for the tip option (e.g. "15%", "$5")' })
  @IsNotEmpty()
  @IsString()
  label: string;

  @ApiProperty({ description: 'The type of tip (percentage or fixed amount)' })
  @IsNotEmpty()
  @IsEnum(TipType)
  type: TipType;

  @ApiProperty({ description: 'The value of the tip (percentage or amount)' })
  @IsNotEmpty()
  @IsNumber()
  value: number;

  @ApiProperty({ description: 'Whether this tip option is active', default: true })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @ApiProperty({ description: 'The sort order for displaying tip options', default: 0 })
  @IsOptional()
  @IsNumber()
  sortOrder?: number;
} 