import { Injectable, OnModuleInit } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { TipOption, TipType } from './tip-options.schema';
import { CreateTipOptionDto } from './dto/create-tip-option.dto';
import { TipOptionsSeeder } from './tip-options.seeder';

@Injectable()
export class TipOptionsService implements OnModuleInit {
  constructor(
    @InjectModel(TipOption.name) private tipOptionModel: Model<TipOption>,
    private readonly tipOptionsSeeder: TipOptionsSeeder
  ) {}

  async onModuleInit() {
    await this.seedDefaultOptions();
  }

  private async seedDefaultOptions() {
    await this.tipOptionsSeeder.seed();
  }

  async create(createTipOptionDto: CreateTipOptionDto, workspace?: string): Promise<TipOption> {
    // Explicitly set isActive based on the DTO value or default to true
    const isActive = createTipOptionDto.isActive !== undefined ? createTipOptionDto.isActive : true;

    const createdTipOption = new this.tipOptionModel({
      ...createTipOptionDto,
      isActive, 
      workspace: workspace || null,
      isDefault: !workspace 
    });
    
    const savedTipOption = await createdTipOption.save();
    return savedTipOption;
  }

  async findAll(workspace?: string): Promise<TipOption[]> {
    // Get both workspace-specific and default options
    const query: any = { isActive: true };
    if (workspace) {
      query.$or = [
        { workspace },
        { isDefault: true }
      ];
    } else {
      query.isDefault = true;
    }
    
    return this.tipOptionModel
      .find(query)
      .sort({ isDefault: -1, sortOrder: 1 })
      .exec();
  }

  async findOne(id: string, workspace?: string): Promise<TipOption> {
    const tipOption = await this.tipOptionModel.findById(id).exec();
    
    // Return if it's a default option or belongs to the workspace
    if (tipOption?.isDefault || !workspace || tipOption?.workspace?.toString() === workspace) {
      return tipOption;
    }
    return null;
  }

  async update(id: string, updateTipOptionDto: Partial<CreateTipOptionDto>, workspace?: string): Promise<TipOption> {
    const tipOption = await this.findOne(id);
    // Only allow updating if it belongs to the workspace and is not a default option
    if (!tipOption || tipOption.isDefault || (workspace && tipOption.workspace?.toString() !== workspace)) {
      return null;
    }
    return this.tipOptionModel.findByIdAndUpdate(id, updateTipOptionDto, { new: true }).exec();
  }

  async remove(id: string, workspace?: string): Promise<TipOption> {
    const tipOption = await this.findOne(id);
    // Only allow deletion if it belongs to the workspace and is not a default option
    if (!tipOption || tipOption.isDefault || (workspace && tipOption.workspace?.toString() !== workspace)) {
      return null;
    }
    return this.tipOptionModel.findByIdAndDelete(id).exec();
  }

  async calculateTipAmount(baseAmount: number, tipOptionId: string | null, customAmount?: number): Promise<number> {
    if (customAmount !== undefined && customAmount >= 0) {
      return customAmount;
    }

    if (!tipOptionId) {
      return 0;
    }

    const tipOption = await this.findOne(tipOptionId);
    if (!tipOption) {
      return 0;
    }

    if (tipOption.type === TipType.PERCENTAGE) {
      return (baseAmount * tipOption.value) / 100;
    } else if (tipOption.type === TipType.FIXED) {
      return tipOption.value;
    }

    return 0;
  }
} 