import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { TipOption, TipType } from './tip-options.schema';

@Injectable()
export class TipOptionsSeeder {
  constructor(
    @InjectModel(TipOption.name) private tipOptionModel: Model<TipOption>
  ) {}

  async seed() {
    const defaultTipOptions = [
      {
        label: '15%',
        type: TipType.PERCENTAGE,
        value: 15,
        sortOrder: 0,
        isActive: true,
        isDefault: true
      },
      {
        label: '20%',
        type: TipType.PERCENTAGE,
        value: 20,
        sortOrder: 1,
        isActive: true,
        isDefault: true
      },
      {
        label: '25%',
        type: TipType.PERCENTAGE,
        value: 25,
        sortOrder: 2,
        isActive: true,
        isDefault: true
      }
    ];

    for (const tipOption of defaultTipOptions) {
      const exists = await this.tipOptionModel.findOne({ 
        label: tipOption.label,
        type: tipOption.type,
        value: tipOption.value,
        isDefault: true
      });

      if (!exists) {
        await this.tipOptionModel.create(tipOption);
      }
    }
  }
} 