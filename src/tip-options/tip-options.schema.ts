import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';
import { Workspace } from '../workspace/workspace.schema';

export enum TipType {
  PERCENTAGE = 'percentage',
  FIXED = 'fixed',
  CUSTOM = 'custom'
}

@Schema()
export class TipOption extends Document {
  @Prop({ required: true })
  label: string;

  @Prop({ required: true, enum: TipType })
  type: TipType;

  @Prop({ required: true })
  value: number;

  @Prop({ required: true, default: true })
  isActive: boolean;

  @Prop({ required: true, default: 0 })
  sortOrder: number;

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'Workspace', required: false })
  workspace: Workspace;

  @Prop({ required: true, default: false })
  isDefault: boolean;
}

export const TipOptionSchema = SchemaFactory.createForClass(TipOption); 