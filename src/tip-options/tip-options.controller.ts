import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Req,
  HttpCode,
  HttpStatus
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { TipOptionsService } from './tip-options.service';
import { CreateTipOptionDto } from './dto/create-tip-option.dto';
import { TipOption } from './tip-options.schema';
import { AuthGuard, UserRequest } from '../auth/auth.guard';
import { ACGuard, UseRoles } from 'nest-access-control';
import ROLE_RESOURCES from '../@core/constants/roles.resources';

@ApiTags('Tip Options')
@Controller('tip-options')
@UseGuards(AuthGuard, ACGuard)
export class TipOptionsController {
  constructor(private readonly tipOptionsService: TipOptionsService) {}

  @Post()
  // @UseRoles({
  //   resource: ROLE_RESOURCES.TIP_OPTIONS,
  //   action: 'create',
  //   possession: 'any'
  // })
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'Create a new tip option' })
  @ApiResponse({ status: 201, description: 'The tip option has been successfully created.' })
  create(@Body() createTipOptionDto: CreateTipOptionDto, @Req() req: UserRequest) {
    const workspace = req.user.workspace?.toString();
    return this.tipOptionsService.create(createTipOptionDto, workspace);
  }

  @Get()
  // @UseRoles({
  //   resource: ROLE_RESOURCES.TIP_OPTIONS,
  //   action: 'read',
  //   possession: 'any'
  // })
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Get all active tip options' })
  @ApiResponse({ status: 200, description: 'Return all active tip options.' })
  findAll(@Req() req: UserRequest) {
    const workspace = req.user.workspace?.toString();
    return this.tipOptionsService.findAll(workspace);
  }

  @Get(':id')
  // @UseRoles({
  //   resource: ROLE_RESOURCES.TIP_OPTIONS,
  //   action: 'read',
  //   possession: 'any'
  // })
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Get a tip option by id' })
  @ApiResponse({ status: 200, description: 'Return the tip option.' })
  findOne(@Param('id') id: string, @Req() req: UserRequest) {
    const workspace = req.user.workspace?.toString();
    return this.tipOptionsService.findOne(id, workspace);
  }

  @Patch(':id')
  // @UseRoles({
  //   resource: ROLE_RESOURCES.TIP_OPTIONS,
  //   action: 'update',
  //   possession: 'any'
  // })
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Update a tip option' })
  @ApiResponse({ status: 200, description: 'The tip option has been successfully updated.' })
  update(
    @Param('id') id: string,
    @Body() updateTipOptionDto: Partial<CreateTipOptionDto>,
    @Req() req: UserRequest
  ) {
    const workspace = req.user.workspace?.toString();
    return this.tipOptionsService.update(id, updateTipOptionDto, workspace);
  }

  @Delete(':id')
  // @UseRoles({
  //   resource: ROLE_RESOURCES.TIP_OPTIONS,
  //   action: 'delete',
  //   possession: 'any'
  // })
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Delete a tip option' })
  @ApiResponse({ status: 200, description: 'The tip option has been successfully deleted.' })
  remove(@Param('id') id: string, @Req() req: UserRequest) {
    const workspace = req.user.workspace?.toString();
    return this.tipOptionsService.remove(id, workspace);
  }
} 