import {
    HttpException,
    HttpStatus,
    Inject,
    Injectable,
    forwardRef,
} from '@nestjs/common';
import { BookingExtra, BookingExtraDocument } from './booking-extra.schema';
import { InjectModel } from '@nestjs/mongoose';
import mongoose, { Model } from 'mongoose';
import BookingExtraDto from './Dto/BookingExtraDto';
import UpdateBookingExtraDto from './Dto/UpdateBookingExtraDto';
import { MediasService } from '../medias/medias.service';
import { BookingTypes, BookingTypesDocument } from '../booking-types/booking-types.schema';

@Injectable()
export class BookingExtraService {
    constructor(
        @Inject(forwardRef(() => MediasService))
        private mediasService: MediasService,
        @InjectModel(BookingTypes.name)
        private bookingTypeModel: Model<BookingTypesDocument>,
        @InjectModel(BookingExtra.name)
        private bookingExtraModel: Model<BookingExtraDocument>,
    ) {}

    async getBookingExtras(workspace: string): Promise<any> {
        return await this.bookingExtraModel
            .find({
                workspace: new mongoose.Types.ObjectId(workspace),
            })
            .exec();
    }

    async getBookingExtra(id: string, workspace: string): Promise<any> {
        const checkExist = await this.bookingExtraModel
            .findOne({
                _id: new mongoose.Types.ObjectId(id),
                workspace: new mongoose.Types.ObjectId(workspace),
            })
            .exec();
        if (!checkExist) {
            throw new HttpException(
                'Data Booking Extra not existed',
                HttpStatus.CONFLICT,
            );
        }
        return {
            statusCode: HttpStatus.OK,
            message: 'Booking Extra updated successfully',
            data: checkExist,
        };
    }

    async createBookingExtra(BookingExtra: BookingExtraDto): Promise<any> {
        const bookingExtra = await this.bookingExtraModel.create({
            ...BookingExtra,
            workspace: new mongoose.Types.ObjectId(BookingExtra.workspace),
        });
        return {
            statusCode: HttpStatus.OK,
            message: 'Booking Extra added successfully',
            data: {
                bookingExtra,
            },
        };
    }

    async createMultipleBookingExtras(
        bookingExtrasDto: BookingExtraDto[],
        workspace: mongoose.Types.ObjectId,
    ){

        // create booking extra for fixed price service
        console.log('bookingExtrasDto',bookingExtrasDto[bookingExtrasDto.length-1])
        const bookingExtras = await this.bookingExtraModel.insertMany(
            bookingExtrasDto.map((bookingExtra) => ({
                ...bookingExtra,
                workspace: new mongoose.Types.ObjectId(workspace),
            })),
        )
        // create booking extra for dynamic price service
        const bookingExtrasDynamicPrice = await this.bookingExtraModel.insertMany(
            bookingExtrasDto.map((bookingExtra) => ({
                ...bookingExtra,
                price: 0,
                duration: 0,
                workspace: new mongoose.Types.ObjectId(workspace),
            })),
        )

        const bookingTypeHome = await this.bookingTypeModel.findOne({
            name: 'Home services',
            workspace: new mongoose.Types.ObjectId(workspace)
        })

        const bookingTypeDynamic = await this.bookingTypeModel.findOne({
            name: 'Commercial services',
            workspace: new mongoose.Types.ObjectId(workspace)
        })

        await this.bookingTypeModel.updateMany({
                parent:bookingTypeHome._id.toString(),
                workspace: new mongoose.Types.ObjectId(workspace)
            },
            {
                $push: {
                    extras: {
                        $each: bookingExtras.map((item) => item._id)
                    }
                }
            }
        )

        await this.bookingTypeModel.updateMany({
                parent:bookingTypeDynamic._id.toString(),
                workspace: new mongoose.Types.ObjectId(workspace)
            },
            {
                $push: {
                    extras: {
                        $each: bookingExtrasDynamicPrice.map((item) => item._id)
                    }
                }
            }
        )

        return {
            statusCode: HttpStatus.OK,
            message: 'Booking Extras added successfully',
            data: {
                bookingExtras,
            },
        };
    }

    async updateBookingExtra(
        id: string,
        BookingExtra: UpdateBookingExtraDto,
    ): Promise<any> {
        const checkExist = await this.bookingExtraModel
            .findOne({
                _id: new mongoose.Types.ObjectId(id),
                workspace: new mongoose.Types.ObjectId(BookingExtra.workspace),
            })
            .exec();
        if (!checkExist) {
            throw new HttpException(
                'Data Booking Extra not existed',
                HttpStatus.CONFLICT,
            );
        }
        await this.bookingExtraModel
            .updateOne(
                { _id: id },
                {
                    ...BookingExtra,
                    workspace: new mongoose.Types.ObjectId(
                        BookingExtra.workspace,
                    ),
                },
            )
            .exec();
        return {
            statusCode: HttpStatus.OK,
            message: 'Booking Extra updated successfully',
        };
    }

    async deleteBookingExtra(id: string, workspace: string): Promise<any> {
        await this.bookingExtraModel
            .deleteOne({
                _id: id,
                workspace: new mongoose.Types.ObjectId(workspace),
            })
            .exec();
        return {
            statusCode: HttpStatus.OK,
            message: 'Booking Extra deleted successfully',
        };
    }

    async getManyBookingExtras(ids: string[], workspace: string): Promise<any> {
        if (!ids.length) {
            throw new HttpException(
                'Data input must be array string',
                HttpStatus.CONFLICT,
            );
        }

        const dataQuery = await this.bookingExtraModel
            .find({
                _id: {
                    $in: ids,
                },
                workspace: new mongoose.Types.ObjectId(workspace),
            })
            .exec();

        const result = await Promise.all(
            dataQuery.map(async (item) => {
                const svgContent = await this.mediasService.getMedia(item.icon);
                const data = item.toObject();
                data.icon = await svgContent.Body.transformToString();

                return { ...data };
            }),
        );

        return {
            statusCode: HttpStatus.OK,
            message: 'Booking Extra get many successfully',
            data: result,
        };
    }
}
