import { <PERSON><PERSON>, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema, Types } from "mongoose";
import { Workspace } from "../workspace/workspace.schema";
import { Type } from "class-transformer";

@Schema()
export class BookingExtra {
  _id: MongooseSchema.Types.ObjectId;

  @Prop({ required: true })
  name: string;

  @Prop({ optional: true })
  subtitle: string;

  @Prop({ required: true })
  price: number;

  @Prop({ required: true })
  duration: number;

  @Prop({ required: true })
  icon: string;

  @Prop({ required: true, type: Types.ObjectId, ref: Workspace.name })
  @Type(() => Workspace)
  workspace: Workspace;
}

export type BookingExtraDocument = BookingExtra & Document;
export const BookingExtraSchema = SchemaFactory.createForClass(BookingExtra);