import { Module, forwardRef } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { BookingExtra, BookingExtraSchema } from './booking-extra.schema';
import { BookingExtraService } from './booking-extra.service';
import { BookingExtraController } from './booking-extra.controller';
import { StorageModule } from '../storage/storage.module';
import { MediasModule } from '../medias/medias.module';
import { Medias, MediasSchema } from '../medias/medias.schema';
import { MediasService } from '../medias/medias.service';
import { UsersModule } from '../users/users.module';
import { ClientsModule } from '../clients/clients.module';
import { BookingTypes, BookingTypesSchema } from '../booking-types/booking-types.schema';

@Module({
    imports: [
        forwardRef(() => ClientsModule),
        forwardRef(() => UsersModule),
        forwardRef(() => StorageModule),
        forwardRef(() => MediasModule),
        MongooseModule.forFeature([
            { name: Medias.name, schema: MediasSchema },
        ]),
        MongooseModule.forFeature([
            { name: BookingTypes.name, schema: BookingTypesSchema}
        ]),
        MongooseModule.forFeature([
            { name: BookingExtra.name, schema: BookingExtraSchema },
        ]),
    ],
    providers: [BookingExtraService, MediasService],
    exports: [BookingExtraService],
    controllers: [BookingExtraController],
})
export class BookingExtraModule {}
