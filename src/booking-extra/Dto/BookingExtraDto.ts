import { ApiProperty } from "@nestjs/swagger";
import {IsNotEmpty, IsN<PERSON>ber, IsOptional, IsString} from "class-validator";

export default class BookingExtraDto{
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  readonly name: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  readonly subtitle: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  readonly price: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  readonly duration: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  readonly icon: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  readonly workspace: string;
}