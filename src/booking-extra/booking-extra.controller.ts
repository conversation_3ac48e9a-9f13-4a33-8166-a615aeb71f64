import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Patch,
  Post,
  Query, Req,
  UseGuards,
} from '@nestjs/common';
import { BookingExtraService } from './booking-extra.service';
import BookingExtraDto from './Dto/BookingExtraDto';
import UpdateBookingExtraDto from './Dto/UpdateBookingExtraDto';
import { ACGuard, UseRoles } from 'nest-access-control';
import { AuthGuard } from '../auth/auth.guard';
import ROLE_RESOURCES from '../@core/constants/roles.resources';
import { UsersService } from '../users/users.service';
import { ApiTags } from '@nestjs/swagger';
import jwt from 'jsonwebtoken';

@Controller(ROLE_RESOURCES.BOOKING_EXTRA)
@ApiTags('Booking-extra')
export class BookingExtraController {
  constructor(
    private bookingExtraService: BookingExtraService,
    private usersService: UsersService
  ) {}

  @HttpCode(HttpStatus.OK)
  @Get('/')
  async getBookingExtras(
    @Req() req:Request,
    @Query('workspace') workspace_param: string,
  ) {

     let workspace = workspace_param
      //@ts-ignore
      const token = req.headers?.authorization;

      if (token && !workspace) {
        const decoded = jwt.decode(token);
        const user = await this.usersService.findOne(decoded['id']);
        workspace = user?.workspace?.toString();
      }

    return {
      statusCode: HttpStatus.OK,
      message: 'Get Booking Extra items successfully',
      data: await this.bookingExtraService.getBookingExtras(workspace),
    };
  }

  @HttpCode(HttpStatus.OK)
  @Get('/listing')
  async getBookingExtrasListing(
    @Query('list') list: string,
    @Query('workspace') workspace_param: string,
  ) {
    const workspace = workspace_param;
    const extrasArr = list.split(',');
    return await this.bookingExtraService.getManyBookingExtras(
      extrasArr,
      workspace,
    );
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.BOOKING_EXTRA,
    action: 'read',
    possession: 'any',
  })
  @Get(':id')
  async getBookingExtra(
    @Req() req:Request,
    @Param('id') id: string,
  ) {
    //@ts-ignores
    const workspace = req.user.workspace;
    return this.bookingExtraService.getBookingExtra(id, workspace);
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.BOOKING_EXTRA,
    action: 'create',
    possession: 'any',
  })
  @Post('create')
  async createBookingExtra(
      @Req() req:Request,
      @Body() bookingExtraDto: BookingExtraDto
  ) {
    //@ts-ignores
    const workspace = req.user.workspace;
    return await this.bookingExtraService.createBookingExtra({...bookingExtraDto, workspace});
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
     resource: ROLE_RESOURCES.BOOKING_EXTRA,
     action: 'create',
     possession: 'own',
  })
  @Post('create/multiple')
    async createMultipleBookingExtras(
        @Req() req:Request,
        @Body() bookingExtrasDto: BookingExtraDto[]
  ) {
        //@ts-ignores
        const workspace = req.user.workspace;
        return await this.bookingExtraService.createMultipleBookingExtras(bookingExtrasDto,workspace);
    }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.BOOKING_EXTRA,
    action: 'update',
    possession: 'any',
  })
  @Patch(':id')
  async updateBookingExtra(
    @Param('id') id: string,
    @Body() bookingExtraDto: UpdateBookingExtraDto,
  ) {
    return this.bookingExtraService.updateBookingExtra(id, bookingExtraDto);
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.BOOKING_EXTRA,
    action: 'delete',
    possession: 'any',
  })
  @Delete(':id')
  async deleteBookingExtra(
    @Param('id') id: string,
    @Body('workspace') workspace: string,
  ) {
    return this.bookingExtraService.deleteBookingExtra(id, workspace);
  }
}
