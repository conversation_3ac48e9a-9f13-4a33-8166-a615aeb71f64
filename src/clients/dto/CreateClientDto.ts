import { ApiProperty } from '@nestjs/swagger';
import {IsNotEmpty, IsObject, IsOptional, IsString, ValidateNested} from 'class-validator';
import {Type} from "class-transformer";

class Phone {
  @ApiProperty()
  @IsString()
  readonly type: string;

  @ApiProperty()
  @IsString()
  readonly item: string;
}
class Email {
  @ApiProperty()
  @IsString()
  readonly type: string;

  @ApiProperty()
  @IsString()
  readonly item: string;
}
export default class CreateClientDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  readonly firstName: string;

  @ApiProperty()
  @IsNotEmpty()
  readonly lastName: string;


  @ApiProperty()
  @ValidateNested({ each: true })
  @Type(() => Phone)
  readonly phone: Phone[];

  @ApiProperty()
  @ValidateNested({ each: true })
  @Type(() => Email)
  readonly email: Email[];


  @ApiProperty()
  @IsString()
  @IsOptional()
  readonly password?: string;

}
