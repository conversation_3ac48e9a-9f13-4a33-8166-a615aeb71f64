import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty, IsN<PERSON>ber, IsString } from "class-validator";

export class GetWorkspaceDtoDto {

  @ApiProperty({required: true})
  @IsString()
  @IsNotEmpty()
  readonly client: string;

  @ApiProperty({required: true})
  @IsNumber({})
  @IsNotEmpty()
  readonly pageSize: number;

  @ApiProperty({required: true})
  @IsNumber()
  @IsNotEmpty()
  readonly pageNum: number;
}