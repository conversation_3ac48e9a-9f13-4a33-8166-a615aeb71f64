import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty, IsN<PERSON>ber, IsOptional, IsString } from "class-validator";

export class GetWorkspaceDto {

  @ApiProperty({required: true})
  @IsString()
  @IsNotEmpty()
  readonly workspace: string;

  @ApiProperty({required: true})
  @IsNumber({})
  @IsNotEmpty()
  pageSize: number;

  @ApiProperty({required: true})
  @IsNumber()
  @IsNotEmpty()
  pageNum: number;

  @ApiProperty({required: true})
  @IsString()
  @IsOptional()
  query?: string;
}