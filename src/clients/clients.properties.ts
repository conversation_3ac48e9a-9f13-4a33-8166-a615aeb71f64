import {
  CollectionProperties,
  Expose,
} from '@forlagshuset/nestjs-mongoose-paginate';

export class MyCollectionProperties extends CollectionProperties {
  @Expose({ name: 'createdAt', sortable: true})
  readonly created_at: 'desc' | 'asc';
  //
  @Expose({ name: 'clientType', filterable: true })
  readonly clientType: string;
  // readonly created_at: 'desc' | 'asc';
  //
  readonly unsortable: string;
}
