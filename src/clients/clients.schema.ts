import { <PERSON><PERSON>, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';
import { Exclude } from 'class-transformer';
import { Workspace } from '../workspace/workspace.schema';

@Schema({
    toJSON: {
        virtuals: true,
    },
})
export class Clients {
    _id: MongooseSchema.Types.ObjectId;

    @Prop({ required: true })
    Id: string;

    @Prop()
    email: string;

    @Prop()
    @Exclude()
    password: string;

    @Prop({ type: MongooseSchema.Types.ObjectId, ref: Workspace.name, required: true })
    workspace: Workspace; // Single workspace reference

    @Prop()
    createdAt: Date;

    @Prop()
    updatedAt: Date;

    @Prop({
        default: null,
    })
    firstSignedIn: Date;
}

export type ClientsDocument = Clients & Document;
const ClientsSchema = SchemaFactory.createForClass(Clients);

export { ClientsSchema };
