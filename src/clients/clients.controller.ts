import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpException,
  HttpStatus,
  Param,
  Post,
  Query, Req,
  Request,
  UseGuards,
} from '@nestjs/common';
import { ClientsService } from './clients.service';
import CreateClientDto from './dto/CreateClientDto';
import { Patch } from '@nestjs/common/decorators/http/request-mapping.decorator';
import { AuthGuard } from '../auth/auth.guard';
import { ACGuard, UseRoles } from 'nest-access-control';
import ROLE_RESOURCES from '../@core/constants/roles.resources';
import { ApiTags } from '@nestjs/swagger';
import UpdateClientDataDto from './dto/updateClientDto';
import { DeleteMultipleClientsDto } from './dto/delete-multiple-clients.dto';

@Controller(ROLE_RESOURCES.CLIENTS)
@ApiTags('Clients')
export class ClientsController {
  constructor(private clientService: ClientsService) {}

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: 'clients',
    action: 'delete',
    possession: 'any',
  })
  @Delete('bulk-delete')
  async bulkDelete(
    @Body() dto: DeleteMultipleClientsDto,
    @Req() req: Request,
  ) {
    try {
      //@ts-ignore
      const result = await this.clientService.bulkDelete(dto.clientIds, req.user.workspace);
      return result;
    } catch (error) {
      throw new HttpException(
        error.message || 'Internal server error',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: 'clients',
    action: 'read',
    possession: 'any',
  })
  @Get('/')
  async getClients(
    @Req() req: Request,
    @Query('pageSize') pageSize: number,
    @Query('pageNum') pageNum: number
  ): Promise<any> {
    const data = await this.clientService.queryClients({
      //@ts-ignore
      workspace:req.user.workspace,
      pageSize,
      pageNum
    });

    return {
      message: 'Get clients success',
      data: data,
      status: HttpStatus.OK,
    };
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: 'clients',
    action: 'read',
    possession: 'any',
  })
  @Get('/nearest')
  async getNearestClients(
    @Query('lat') lat: number,
    @Query('lng') lng: number,
    @Body('workspace') workspace: string,
  ): Promise<any> {
    const data = await this.clientService.getNearestClients(
      workspace,
      lat,
      lng,
    );

    return {
      message: 'Get clients success',
      data: data,
      status: HttpStatus.OK,
    };
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: 'clients',
    action: 'read',
    possession: 'any',
  })
  @Get('/search')
  async searchClients(
    @Req() req: Request,
    @Query('pageSize') pageSize: number,
    @Query('pageNum') pageNum: number,
    @Query('query') query: string,
  ): Promise<any> {
    //@ts-ignore
    const workspace = req.user.workspace;
    const data = await this.clientService.searchClients({
      workspace,
      pageSize,
      pageNum,
      query,
    });

    return {
      message: 'Get clients success',
      data: data,
      status: HttpStatus.OK,
    };
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: 'clients',
    action: 'read',
    possession: 'any',
  })
  @Get('/workspace/me')
  async getMyClientsWorkspace(@Request() req: any) {
    const client = req.user._id;

    return this.clientService.getClientWorkspace(client.toString());
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: 'clients',
    action: 'create',
    possession: 'any',
  })
  @Post('create')
  async createClient(
      @Req () req: Request,
      @Body() CreateClientDto: CreateClientDto
  ) {
    //@ts-ignore
    const workspace = req.user.workspace;
    return this.clientService.createClient(CreateClientDto,workspace);
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: 'clients',
    action: 'read',
    possession: 'any',
  })
  @Get(':id')
  async getClient(
    @Req() req: Request,
    @Param('id') id: string
  ) {
    //@ts-ignore
    const workspace = req.user.workspace;
    return {
      message: 'Get client success',
      data: await this.clientService.getClient({
        id,
        workspace,
      }),
      status: HttpStatus.OK,
    };
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: 'clients',
    action: 'update',
    possession: 'any',
  })
  @Patch(':id')
  async updateClient(
    @Req() req: Request,
    @Param('id') id: string,
    @Body() updateClientDto: UpdateClientDataDto,
  ) {
    //@ts-ignore
    const workspace = req.user.workspace
    return this.clientService.updateClient(id, updateClientDto,workspace);
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: 'clients',
    action: 'delete',
    possession: 'any',
  })
  @Delete(':id')
  remove(
      @Param('id') id: string,
      @Req() req: Request
  ) {
    //@ts-ignore
    const workspace = req.user.workspace
    return this.clientService.deleteClient(id, workspace);
  }
}
