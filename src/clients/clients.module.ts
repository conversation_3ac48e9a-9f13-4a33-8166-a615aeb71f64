import { Module, forwardRef } from '@nestjs/common';
import { ClientsService } from './clients.service';
import { MongooseModule } from '@nestjs/mongoose';
import { Clients, ClientsSchema } from './clients.schema';
import { ClientsController } from './clients.controller';
import { UsersModule } from '../users/users.module';
import { ClientsDataModule } from '../clients-data/clients-data.module';

@Module({
    imports: [
        MongooseModule.forFeature([
            { name: Clients.name, schema: ClientsSchema },
        ]),
        forwardRef(() => UsersModule),
        forwardRef(() => ClientsDataModule),
    ],
    providers: [ClientsService],
    exports: [ClientsService],
    controllers: [ClientsController],
})
export class ClientsModule {}
