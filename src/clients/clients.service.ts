import {
    BadRequestException,
    HttpException,
    HttpStatus,
    Inject,
    Injectable,
    forwardRef,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import mongoose, { Model } from 'mongoose';
import { Clients, ClientsDocument } from './clients.schema';
import CreateClientDto from './dto/CreateClientDto';
import { v4 as uuidv4 } from 'uuid';
import CreateIssueDto from '../issues/Dto/CreateIssueDto';
import { genSalt, hash } from 'bcrypt';
import { ClientsDataService } from '../clients-data/clients-data.service';
import GetClientsDto from './dto/GetClientsDto';
import { GetWorkspaceDto } from './dto/getClientWorkspace.dto';
import UpdateClientDataDto from './dto/updateClientDto';
@Injectable()
export class ClientsService {
    constructor(
        @InjectModel(Clients.name) private clientModel: Model<ClientsDocument>,
        @Inject(forwardRef(() => ClientsDataService))
        private clientDataService: ClientsDataService
    ) {}

    async queryClients(getWorkspaceDto: GetWorkspaceDto): Promise<any> {
        let { workspace, pageSize, pageNum } = getWorkspaceDto;

        if (!workspace) {
            throw new HttpException(
                'Workspace is required',
                HttpStatus.BAD_REQUEST,
            );
        }

        if (!pageSize) {
            getWorkspaceDto.pageSize = 10;
        }

        if (!pageNum) {
            getWorkspaceDto.pageNum = 1;
        }

        return this.clientDataService.getClientsByWorkspace(getWorkspaceDto);
    }

    async getNearestClients(
        workspace: string,
        lat: number,
        lng: number,
    ): Promise<any> {
        return this.clientDataService.getNearestClientsByWorkspace(
            workspace,
            lat,
            lng,
        );
    }

    async searchClients(getWorkspaceDto: GetWorkspaceDto): Promise<any> {
        let { workspace, pageSize, pageNum } = getWorkspaceDto;
        if (!workspace) {
            throw new HttpException(
                'Workspace is required',
                HttpStatus.BAD_REQUEST,
            );
        }

        if (!pageSize) {
            getWorkspaceDto.pageSize = 10;
        }

        if (!pageNum) {
            getWorkspaceDto.pageNum = 1;
        }

        return this.clientDataService.searchClientsByWorkspace(getWorkspaceDto);
    }

    async createClient(CreateClientDto: CreateClientDto, workspace: string): Promise<any> {
        const email = CreateClientDto.email.find(
            (item) => item.type === 'main',
        );

        // Check if client exists with same email and workspace
        const existingClient = await this.clientModel.findOne({
            email: email.item,
            workspace: workspace
        });

        if (existingClient) {
            // Client already exists in this workspace
            const existingClientData = await this.clientDataService.findDataByClientAndWorkspace(
                existingClient._id.toString(),
                workspace
            );

            if (!existingClientData) {
                await this.clientDataService.create({
                    firstName: CreateClientDto.firstName,
                    lastName: CreateClientDto.lastName,
                    phone: CreateClientDto.phone,
                    email: CreateClientDto.email,
                    client: existingClient._id,
                    workspace: workspace
                });
            }

            return {
                message: 'Client already exists in workspace',
                data: {
                    id: existingClient.Id,
                },
                status: HttpStatus.OK,
            };
        }

        // Create new client
        const salt = await genSalt();
        const createdClient = new this.clientModel({
            email: email.item,
            password: await hash(CreateClientDto.password, salt),
            Id: uuidv4(),
            workspace: workspace, // Single workspace
            createdAt: new Date(),
            updatedAt: new Date(),
        });
        const client = await createdClient.save();

        // Create new client data
        await this.clientDataService.create({
            firstName: CreateClientDto.firstName,
            lastName: CreateClientDto.lastName,
            phone: CreateClientDto.phone,
            email: CreateClientDto.email,
            client: client._id,
            workspace: workspace,
        });

        return {
            message: 'Create client success',
            data: {
                id: client.Id,
            },
            status: HttpStatus.OK,
        };
    }

    async getRootClient(id: string): Promise<any> {
        const client = await this.clientModel.findOne({ Id: id });

        if (!client) {
            throw new HttpException('Client not found', HttpStatus.BAD_REQUEST);
        }

        return client;
    }

    async getClient(data: GetClientsDto): Promise<any> {
        // console.log(data);
        const clientRoot = await this.clientModel.findOne({ Id: data.id });

        if (!clientRoot) {
            throw new HttpException('Client not found', HttpStatus.BAD_REQUEST);
        }
        // console.log(clientRoot);

        const client =
            await this.clientDataService.findDataByClientAndWorkspace(
                clientRoot._id,
                data.workspace,
            );

        if (!client) {
            throw new HttpException(
                'Data Client not found',
                HttpStatus.BAD_REQUEST,
            );
        } else {
            return client;
        }
    }

    async updateClient(
        id: string,
        updateClientDto: UpdateClientDataDto,
        workspace: string,
    ): Promise<any> {
        const client = await this.clientModel.findOne({ Id: id });

        // const checkExist =
        //     await this.clientDataService.findDataByClientAndWorkspace(
        //         client._id,
        //         workspace,
        //     );
        //
        // if (!checkExist) {
        //     throw new HttpException(
        //         'Data Client not found',
        //         HttpStatus.BAD_REQUEST,
        //     );
        // }
        // const email = updateClientDto.email.find(
        //     (item) => item.type === 'main',
        // );

        await this.clientDataService.update({
            ...updateClientDto,
            client: client._id.toString(),
        },workspace);

        return {
            message: 'Update success',
            status: HttpStatus.OK,
        };
    }

    async deleteClient(id: string, workspace: string): Promise<any> {
        const client = await this.clientModel.findOne({ Id: id });
        await this.clientDataService.delete(client.id, workspace);

        return {
            message: 'Delete client success',
            status: HttpStatus.OK,
        };
    }

    async getClientByEmail(email: string): Promise<Clients> {
        return this.clientModel.findOne({ email });
    }

    async getClientByEmailWorkspace(email: string, workspace: string): Promise<Clients> {
        return this.clientModel.findOne({ email, workspace: new mongoose.Types.ObjectId(workspace) });
    }

    async createClientByIssue(
        issue: CreateIssueDto,
        workspace?:string,
    ): Promise<Clients> {
        return this.createClient({
            firstName: issue.firstName,
            lastName: issue.lastName,
            phone: [
                {
                    item: issue.phone,
                    type: 'main',
                },
            ],
            email: [
                {
                    item: issue.email,
                    type: 'main',
                },
            ],
        },workspace);
    }

    async getClientWorkspace(id: string): Promise<any> {
        const data = await this.clientDataService.getWorkspaceClients({
            client: id,
            pageSize: 10,
            pageNum: 1,
        });

        return {
            message: 'Get client workspace success',
            data: data.clients,
            status: HttpStatus.OK,
        };
    }

    async getOneClient(id: string): Promise<Clients> {
        try {
            return await this.clientModel.findById(id).lean().exec();
        } catch (error) {
            throw new BadRequestException(
                `Failed to get client info, try again`,
            );
        }
    }

    async bulkDelete(clientIds: string[], workspace: string): Promise<any> {
        try {
            // Find all clients first
            const clients = await Promise.all(
                clientIds.map(id => this.clientModel.findOne({ Id: id }))
            );

            const validClients = clients.filter(client => client !== null);
            
            if (!validClients.length) {
                throw new HttpException('No clients found to delete', HttpStatus.BAD_REQUEST);
            }

            // Delete client data for each client
            await Promise.all(
                validClients.map(client => 
                    this.clientDataService.delete(client._id.toString(), workspace)
                )
            );

            return {
                statusCode: HttpStatus.OK,
                message: 'Clients deleted successfully', 
                data: {
                    deletedCount: validClients.length
                }
            };

        } catch (error) {
            throw new HttpException(
                error.message || 'Internal server error',
                error.status || HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }
}
