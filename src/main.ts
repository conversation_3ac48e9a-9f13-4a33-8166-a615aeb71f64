import { HttpExceptionFilter } from './@core/filter/http-exception/http-exception.filter';

require('dotenv').config();
import { NestFactory } from '@nestjs/core';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { AppModule } from './app.module';
import { Logger, ValidationPipe } from '@nestjs/common';
import  cookieParser from 'cookie-parser';
import { MicroserviceOptions } from '@nestjs/microservices';
import { ConfigService } from '@nestjs/config';
import { initializeFirebaseAdmin } from "./@core/configurations/firebase/firebase";

async function bootstrap() {
    const logger = new Logger();
    const NODE_ENV = process.env.NODE_ENV || 'development';
    const PORT = process.env.PORT || 3000;
    const app = await NestFactory.create(AppModule,{
        rawBody: true,
    });
    const configService = app.get(ConfigService);
    app.useGlobalPipes(
        new ValidationPipe({
            whitelist: true,
            transform: true,
            forbidNonWhitelisted: true,
            transformOptions: {
                enableImplicitConversion: true,
            },
        }),
    );
    app.use(cookieParser());
    app.enableCors();

    if (NODE_ENV === 'development' || NODE_ENV === 'staging') {
        const config = new DocumentBuilder()
            .setTitle('CRM Web App')
            .setDescription('The CRM Web App API description')
            .setVersion('1.0')
            .build();
        app.useGlobalFilters(new HttpExceptionFilter());
        const document = SwaggerModule.createDocument(app, config);
        SwaggerModule.setup('api', app, document);
    }

    // firebase
    initializeFirebaseAdmin();

    await app.startAllMicroservices();
    await app.listen(PORT);
    logger.log(`Server is listening on port: ${PORT}`);
}
bootstrap();
