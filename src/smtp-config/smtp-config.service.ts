import { Injectable } from "@nestjs/common";
import { CreateSmtpConfigDto } from "./dto/create-smtp-config.dto";
import { UpdateSmtpConfigDto } from "./dto/update-smtp-config.dto";
import { InjectModel } from "@nestjs/mongoose";
import { FrequencyDiscount, FrequencyDiscountDocument } from "../frequency-discount/frequency-discount.schema";
import mongoose, { Model } from "mongoose";
import { SMTPConfig, SMTPConfigDocument } from "./smtp-config.schema";

@Injectable()
export class SmtpConfigService {
  constructor(
    @InjectModel(SMTPConfig.name)
    private smtpConfigModel: Model<SMTPConfigDocument>
  ) {
  }

  async create(createSmtpConfigDto: CreateSmtpConfigDto, workspace: string) {
    return this.smtpConfigModel.create({
      ...createSmtpConfigDto,
      workspace: new mongoose.Types.ObjectId(workspace)
    });
  }

  async findLatest(workspace: string) {
    return this.smtpConfigModel.findOne({
      workspace: new mongoose.Types.ObjectId(workspace)
    }, {}, { sort: { "createdAt": -1 } });
  }

  async findAll(workspace: string) {
    return this.smtpConfigModel.find({
      workspace: new mongoose.Types.ObjectId(workspace)
    }, {}, { sort: { "createdAt": -1 } });
  }

  async findOne(id: string, workspace: string) {
    return this.smtpConfigModel.findOne({
      _id: new mongoose.Types.ObjectId(id),
      workspace: new mongoose.Types.ObjectId(workspace)
    });

  }

  async update(id: string, updateSmtpConfigDto: UpdateSmtpConfigDto, workspace: string) {
    return this.smtpConfigModel.findByIdAndUpdate(id, {
      ...updateSmtpConfigDto,
      workspace: new mongoose.Types.ObjectId(workspace)
    }, { new: true });
  }

  async remove(id: string, workspace: string) {
    return this.smtpConfigModel.findOneAndDelete({
      _id: new mongoose.Types.ObjectId(id),
      workspace: new mongoose.Types.ObjectId(workspace)
    });
  }
}
