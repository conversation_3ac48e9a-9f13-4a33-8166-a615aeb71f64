import { forwardRef, Modu<PERSON> } from "@nestjs/common";
import { SmtpConfigService } from "./smtp-config.service";
import { SmtpConfigController } from "./smtp-config.controller";
import { ClientsModule } from "../clients/clients.module";
import { UsersModule } from "../users/users.module";
import { MongooseModule } from "@nestjs/mongoose";
import { FrequencyDiscount, FrequencyDiscountSchema } from "../frequency-discount/frequency-discount.schema";
import { SMTPConfig, SMTPConfigSchema } from "./smtp-config.schema";

@Module({
  imports: [
    forwardRef(() => ClientsModule),
    forwardRef(() => UsersModule),
    MongooseModule.forFeature([
      { name: SMTPConfig.name, schema: SMTPConfigSchema }
    ])
  ],
  controllers: [SmtpConfigController],
  providers: [SmtpConfigService],
  exports: [SmtpConfigService]
})
export class SmtpConfigModule {
}
