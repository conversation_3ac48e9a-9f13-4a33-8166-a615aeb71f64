import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, Query, Req, HttpStatus } from "@nestjs/common";
import { SmtpConfigService } from "./smtp-config.service";
import { CreateSmtpConfigDto } from "./dto/create-smtp-config.dto";
import { UpdateSmtpConfigDto } from "./dto/update-smtp-config.dto";
import { ApiTags } from "@nestjs/swagger";
import ROLE_RESOURCES from "../@core/constants/roles.resources";
import { AuthGuard } from "../auth/auth.guard";
import { ACGuard, UseRoles } from "nest-access-control";
import jwt from "jsonwebtoken";
import { UsersService } from "../users/users.service";

@Controller(ROLE_RESOURCES.STMP_CONFIG)
@ApiTags("smtp-config")
export class SmtpConfigController {
  constructor(private readonly smtpConfigService: SmtpConfigService, private readonly usersService: UsersService) {
  }

  @Post()
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.FREQUENCY_DISCOUNT,
    action: "create",
    possession: "any"
  })
  async create(@Req() req: Request, @Body() createSmtpConfigDto: CreateSmtpConfigDto) {
    //@ts-ignore
    const workspace = req.user.workspace;
    return {
      statusCode: HttpStatus.CREATED,
      data: await this.smtpConfigService.create(createSmtpConfigDto, workspace),
      message: "Frequency discount created successfully"
    };
  }

  @Get()
  async findAll(
    @Req() req: Request,
    @Query("workspace") workspace_param: string
  ) {
    let workspace = workspace_param;
    //@ts-ignore
    const token = req.headers?.authorization;
    if (token && !workspace) {
      const decoded = jwt.decode(token);
      const user = await this.usersService.findOne(decoded["id"]);
      workspace = user?.workspace?.toString();
    }
    return {
      statusCode: HttpStatus.OK,
      data: await this.smtpConfigService.findAll(workspace),
      message: "Frequency discount found successfully"
    };
  }

  @Get(":id")
  async findOne(@Param("id") id: string, @Query("workspace") workspace_param: string) {
    let workspace = workspace_param;
    //@ts-ignore
    const token = req.headers?.authorization;
    if (token && !workspace) {
      const decoded = jwt.decode(token);
      const user = await this.usersService.findOne(decoded["id"]);
      workspace = user?.workspace?.toString();
    }
    return {
      statusCode: HttpStatus.OK,
      data: await this.smtpConfigService.findOne(id, workspace),
      message: "Frequency discount detail found successfully"
    };
  }

  @Patch(":id")
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.FREQUENCY_DISCOUNT,
    action: "update",
    possession: "any"
  })
  async update(@Req() req: Request, @Param("id") id: string, @Body() updateSmtpConfigDto: UpdateSmtpConfigDto) {
    //@ts-ignore
    const workspace = req.user.workspace;
    return {
      statusCode: HttpStatus.OK,
      data: await this.smtpConfigService.update(id, updateSmtpConfigDto, workspace),
      message: "Frequency discount updated successfully"
    };
  }

  @Delete(":id")
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.FREQUENCY_DISCOUNT,
    action: "delete",
    possession: "any"
  })
  async remove(@Req() req: Request, @Param("id") id: string) {
    //@ts-ignore
    const workspace = req.user.workspace;
    return {
      statusCode: HttpStatus.NO_CONTENT,
      data: await this.smtpConfigService.remove(id, workspace),
      message: "Frequency discount deleted successfully"
    };
  }
}
