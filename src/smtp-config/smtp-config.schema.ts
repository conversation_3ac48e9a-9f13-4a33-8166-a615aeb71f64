import { <PERSON><PERSON>, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from "mongoose";
import { Workspace } from "../workspace/workspace.schema";

@Schema({ timestamps: true })
export class SMTPConfig {
  @Prop({ required: true })
  host: string;

  @Prop({ required: true })
  port: number;

  @Prop({ required: true })
  username: string;

  @Prop({ required: true })
  password: string;

  @Prop({ required: false, default: false })
  secure: boolean

  @Prop({ required: false })
  fromEmail?: string;

  @Prop({ required: true, type: Types.ObjectId, ref: Workspace.name })
  workspace: Workspace;
}

export type SMTPConfigDocument = SMTPConfig & Document;

export const SMTPConfigSchema = SchemaFactory.createForClass(SMTPConfig);