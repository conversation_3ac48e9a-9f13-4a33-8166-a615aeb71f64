import { ApiProperty } from "@nestjs/swagger";
import { IsBoolean, IsOptional, IsString } from "class-validator";

export class CreateSmtpConfigDto {
  @ApiProperty()
  @IsString()
  host: string;

  @ApiProperty()
  @IsString()
  port: string;

  @ApiProperty()
  @IsBoolean()
  secure: boolean;

  @ApiProperty()
  @IsString()
  username: string;

  @ApiProperty()
  @IsString()
  password: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  fromEmail?: string;
}
