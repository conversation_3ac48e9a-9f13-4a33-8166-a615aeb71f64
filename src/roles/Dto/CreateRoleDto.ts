import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString } from "class-validator";

export class CreateRoleDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty()
  @IsOptional()
  showSidebar: boolean;

  @ApiProperty()
  @IsOptional()
  icon: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString({ each: true })
  grants: string[];

  @IsOptional()
  @IsString()
  workspace: string;
}
