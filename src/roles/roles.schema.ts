import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema, Types } from "mongoose";
import { Grants } from "../grants/grants.schema";
import { Type } from "class-transformer";

@Schema()
export class Roles {
  _id: MongooseSchema.Types.ObjectId;

  @Prop({ required: true })
  name: string;

  @Prop({ default: false, optional: true })
  showSidebar: boolean;

  @Prop({ default: null, optional: true})
  icon: string;

  @Prop({ required: true, type: Types.ObjectId, ref: Grants.name })
  @Type(() => Grants)
  grants: Grants[];

  @Prop({ default: null, optional: true})
  workspace: string;
}

export type RolesDocument = Roles & Document;
export const RolesSchema = SchemaFactory.createForClass(Roles);
