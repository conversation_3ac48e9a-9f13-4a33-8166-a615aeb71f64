import { BadRequestException, HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import mongoose, { Model } from 'mongoose';
import { Roles, RolesDocument } from './roles.schema';
import { CreateRoleDto } from './Dto/CreateRoleDto';
import { GrantsService } from '../grants/grants.service';
import { capitalizeFirstLetter } from 'src/utils/helpers';

@Injectable()
export class RolesService {
    constructor(
        @InjectModel(Roles.name) private roleModel: Model<RolesDocument>,
        private readonly grantsService: GrantsService,
    ) {}

    async getRoles(): Promise<any> {
        const roles = await this.roleModel.find().exec();

        return {
            statusCode: 200,
            data: {
                roles,
            },
        };
    }

    async getRolesPublic(): Promise<any> {
        const roles = await this.roleModel.find({

        }, {
            grants: 0,
            showSidebar: 0,
        }).exec();

        return {
            statusCode: 200,
            data: roles,
            message: 'Get roles successfully'
        };
    }

    async getAllRoles(): Promise<any> {
        const roles = await this.roleModel.find().exec();
        return Promise.all(
            roles.map(async (role) => {
                const grants = await this.grantsService.findMany(
                    role.grants.map((grant) => grant.toString()),
                );
                return [].concat(
                    grants.map((grant) => {
                        return {
                            role: role._id.toString(),
                            resource: grant.resource,
                            action: grant.action,
                            attributes: grant.attributes,
                        };
                    }),
                );
            }),
        );
    }

    async createRole(createRoleDto: CreateRoleDto): Promise<any> {
        const existedRole = await this.roleModel.findOne({
            name: createRoleDto.name,
        });

        if (existedRole) {
            throw new BadRequestException();
        } else {
            const role = await this.roleModel.create({
                name: createRoleDto.name,
                showSidebar: createRoleDto.showSidebar,
                icon: createRoleDto.icon,
                grants: createRoleDto.grants.map(
                    (grant) => new mongoose.Types.ObjectId(grant),
                ),
                workspace: new mongoose.Types.ObjectId(createRoleDto.workspace),
            });

            return {
                statusCode: 200,
                message: 'Create role successfully',
                data: {
                    role,
                },
            };
        }
    }

    async updateRole(id: string, updateRoleDto: CreateRoleDto): Promise<any> {
        const updatedRole = await this.roleModel
            .findOneAndUpdate(
                {
                    _id: id,
                },
                {
                    ...updateRoleDto,
                },
            )
            .exec();

        if (!updatedRole) {
            throw new HttpException('Role not found', HttpStatus.BAD_REQUEST);
        } else {
            return {
                message: 'Update role successfully',
                status: HttpStatus.OK,
            };
        }
    }

    async deleteRole(id: string): Promise<any> {
        const deletedRole = await this.roleModel
            .findOneAndRemove({ _id: id })
            .exec();

        if (!deletedRole) {
            throw new HttpException('Role not found', HttpStatus.BAD_REQUEST);
        }

        return {
            statusCode: 200,
            message: 'Delete role successfully',
        };
    }

    async getParticularRole(role: string): Promise<any> {
        return await this.roleModel
            .find({ name: capitalizeFirstLetter(role) })
            .exec();
    }

    async getRole(id: string): Promise<Roles> {
        return this.roleModel.findById(id).lean().exec();
    }
}
