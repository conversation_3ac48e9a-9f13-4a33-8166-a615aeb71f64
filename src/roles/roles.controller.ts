import {
  Body,
  Controller,
  HttpCode,
  HttpStatus,
  Get,
  Post,
  Delete,
  Param,
  Patch,
  UseGuards,
} from '@nestjs/common';
import { CreateRoleDto } from './Dto/CreateRoleDto';
import { RolesService } from './roles.service';
import { AuthGuard } from '../auth/auth.guard';
import { ACGuard, UseRoles } from 'nest-access-control';
import ROLE_RESOURCES from '../@core/constants/roles.resources';
import { ApiTags } from '@nestjs/swagger';

@Controller(ROLE_RESOURCES.ROLES)
@ApiTags('Roles')
export class RolesController {
  constructor(private rolesService: RolesService) {}

  @HttpCode(HttpStatus.OK)
  @Get('public')
  getRolesPublic() {
    return this.rolesService.getRolesPublic();
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.ROLES,
    action: 'read',
    possession: 'any',
  })
  @Get()
  getRoles() {
    return this.rolesService.getRoles();
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.ROLES,
    action: 'create',
    possession: 'any',
  })
  @Post()
  createRole(@Body() createRoleDto: CreateRoleDto) {
    return this.rolesService.createRole(createRoleDto);
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.ROLES,
    action: 'update',
    possession: 'any',
  })
  @Patch(':id')
  async updateRole(
    @Param('id') id: string,
    @Body() updateRoleDto: CreateRoleDto,
  ) {
    return this.rolesService.updateRole(id, updateRoleDto);
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.ROLES,
    action: 'delete',
    possession: 'any',
  })
  @Delete(':id')
  deleteRole(@Param('id') id: string) {
    return this.rolesService.deleteRole(id);
  }
}
