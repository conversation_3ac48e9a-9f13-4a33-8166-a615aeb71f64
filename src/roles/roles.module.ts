import { Module, forwardRef } from '@nestjs/common';
import { RolesService } from './roles.service';
import { RolesController } from './roles.controller';
import { Roles, RolesSchema } from './roles.schema';
import { MongooseModule } from '@nestjs/mongoose';
import { GrantsModule } from '../grants/grants.module';
import { UsersModule } from '../users/users.module';
import { ClientsModule } from '../clients/clients.module';

@Module({
    imports: [
        forwardRef(() => ClientsModule),
        forwardRef(() => UsersModule),
        forwardRef(() => GrantsModule),
        MongooseModule.forFeature([{ name: Roles.name, schema: RolesSchema }]),
    ],
    controllers: [RolesController],
    providers: [RolesService],
    exports: [RolesService],
})
export class RolesModule {}
