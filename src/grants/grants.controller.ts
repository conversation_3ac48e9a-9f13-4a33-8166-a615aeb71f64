import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Patch,
  Post,
} from '@nestjs/common';
import { GrantsService } from './grants.service';
import { CreateGrantDto } from './Dto/CreateGrantDto';
import { UpdateGrantDto } from './Dto/UpdateGrantDto';
import ROLE_RESOURCES from '../@core/constants/roles.resources';
import { ApiTags } from '@nestjs/swagger';

@Controller(ROLE_RESOURCES.GRANTS)
@ApiTags('Grants')
export class GrantsController {
  constructor(private grantsService: GrantsService) {}

  @HttpCode(HttpStatus.OK)
  @Get('/')
  async findAll() {
    let grants = await this.grantsService.findAll();
    if (!grants) {
      return {
        statusCode: HttpStatus.BAD_REQUEST,
        message: 'Can not get grants',
        data: null,
      };
    }

    return {
      statusCode: HttpStatus.OK,
      message: 'Get grants successfully',
      data: grants,
    };
  }

  @HttpCode(HttpStatus.OK)
  @Get('/:id')
  async findOne(@Param('id') id: string) {
    const grant = await this.grantsService.findOne(id);
    if (!grant) {
      return {
        statusCode: HttpStatus.BAD_REQUEST,
        message: 'Grant not existed',
        data: null,
      };
    }

    return {
      statusCode: HttpStatus.OK,
      message: 'Get grant successfully',
      data: grant,
    };
  }

  @HttpCode(HttpStatus.OK)
  @Post('/')
  async create(@Body() grant: CreateGrantDto) {
    let newGrant = await this.grantsService.create(grant);
    if (!newGrant) {
      return {
        statusCode: HttpStatus.BAD_REQUEST,
        message: 'Can not create grant',
        data: null,
      };
    }

    return {
      statusCode: HttpStatus.OK,
      message: 'Create grant successfully',
      data: newGrant,
    };
  }

  @HttpCode(HttpStatus.OK)
  @Patch('/:id')
  async update(@Param('id') id: string, @Body() grant: UpdateGrantDto) {
    let grantUpdated = await this.grantsService.update(id, grant);
    if (!grantUpdated) {
      return {
        statusCode: HttpStatus.BAD_REQUEST,
        message: 'Can not update grant',
        data: null,
      };
    }

    return {
      statusCode: HttpStatus.OK,
      message: 'Update grant successfully',
      data: grantUpdated,
    };
  }

  @HttpCode(HttpStatus.OK)
  @Delete('/:id')
  async remove(@Param('id') id: string) {
    let grantDeleted = await this.grantsService.remove(id);
    if (!grantDeleted) {
      return {
        statusCode: HttpStatus.BAD_REQUEST,
        message: 'Can not delete grant',
        data: null,
      };
    }

    return {
      statusCode: HttpStatus.OK,
      message: 'Delete grant successfully',
      data: grantDeleted,
    };
  }
}
