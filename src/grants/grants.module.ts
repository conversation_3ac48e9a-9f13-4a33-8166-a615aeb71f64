import { Module, forwardRef } from '@nestjs/common';
import { GrantsService } from './grants.service';
import { GrantsController } from './grants.controller';
import { MongooseModule } from '@nestjs/mongoose';
import { Grants, GrantsSchema } from './grants.schema';
import { UsersModule } from '../users/users.module';
import { ClientsModule } from '../clients/clients.module';

@Module({
    imports: [
        forwardRef(() => ClientsModule),
        forwardRef(() => UsersModule),
        MongooseModule.forFeature([
            { name: Grants.name, schema: GrantsSchema },
        ]),
    ],
    providers: [GrantsService],
    controllers: [GrantsController],
    exports: [GrantsService],
})
export class GrantsModule {}
