import { HttpException, HttpStatus, Injectable } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Grants } from "./grants.schema";
import { Model } from "mongoose";
import { CreateGrantDto } from "./Dto/CreateGrantDto";
import { UpdateGrantDto } from "./Dto/UpdateGrantDto";

class GrantsDocument {
}

@Injectable()
export class GrantsService {
  constructor(
    @InjectModel(Grants.name) private grantsModel: Model<Grants>,
  ) {}

  async create(createGrantDto: CreateGrantDto): Promise<Grants> {
    let checkExist = await this.grantsModel.findOne({ name: createGrantDto.name });

    if (checkExist) {
      throw new HttpException("Grant already exists", HttpStatus.BAD_REQUEST);
    }

    return await this.grantsModel.create({...createGrantDto});
  }

  async findAll(): Promise<Grants[]> {
    return this.grantsModel.find();
  }

  async findMany(ids: string[]): Promise<Grants[]> {
    return this.grantsModel.find({
      _id: {
        $in: ids
      }
    });
  }

  async findOne(id: string): Promise<Grants> {
    return this.grantsModel.findById(id);
  }

  async update(id: string, updateGrantDto: UpdateGrantDto): Promise<Grants> {
    return this.grantsModel.findByIdAndUpdate(id, updateGrantDto, { new: true });
  }

  async remove(id: string): Promise<Grants> {
    return this.grantsModel.findByIdAndRemove(id);
  }
}
