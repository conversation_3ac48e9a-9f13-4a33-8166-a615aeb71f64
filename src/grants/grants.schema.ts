import { <PERSON><PERSON>, <PERSON>hem<PERSON>, SchemaFactory } from "@nestjs/mongoose";
import { Document, Schema as MongooseSchema } from "mongoose";

@Schema()

export class Grants extends Document{

    _id: MongooseSchema.Types.ObjectId;

    @Prop({ required: true })
    name: string;

    @Prop({ required: true })
    resource: string;

    @Prop({ required: true })
    action: string;

    @Prop({ required: true })
    attributes: string;
}

export const GrantsSchema = SchemaFactory.createForClass(Grants);