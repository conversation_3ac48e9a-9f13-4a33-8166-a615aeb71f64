import { BadRequestException, forwardRef, Inject, Injectable } from "@nestjs/common";
import { WorkspaceService } from "../workspace/workspace.service";
import <PERSON><PERSON> from "stripe";
import { ClientsService } from "../clients/clients.service";
import { PropertyService } from "../property/property.service";
import { BookingRequestService } from "../booking-request/booking-request.service";
import { BookingTypesService } from "../booking-types/booking-types.service";
import { InvoicesService } from "../invoices/invoices.service";
import { JobsService } from "../jobs/jobs.service";
import { UserPaymentService } from "../payment/user_payment/user_payment.service";
import { PaymentMethod, PaymentStatus } from "../payment/user_payment/schema/user_payment.schema";
import { TipOptionsService } from "../tip-options/tip-options.service";
import { TipType } from "../tip-options/tip-options.schema";

@Injectable()
export class WebhooksService {

    constructor(
        @Inject(forwardRef(() => WorkspaceService))
        private workspaceService: WorkspaceService,
        @Inject(forwardRef(()=> ClientsService))
        private clientsService: ClientsService,
        @Inject(forwardRef(()=> PropertyService))
        private propertyService: PropertyService,
        @Inject(forwardRef(()=> BookingRequestService))
        private bookingRequestService: BookingRequestService,
        @Inject(forwardRef(()=> BookingTypesService))
        private bookingTypesService: BookingTypesService,
        @Inject(forwardRef(()=> InvoicesService))
        private invoicesService: InvoicesService,
        @Inject(forwardRef(()=> JobsService))
        private jobsService: JobsService,
        @Inject(forwardRef(() => UserPaymentService))
        private userPaymentService: UserPaymentService,
        @Inject(forwardRef(() => TipOptionsService))
        private tipOptionsService: TipOptionsService
    ) {}

    getHighestNumber(item:string){
        // Step 1: Split the string by the dash
        const parts = item.split('-');

        // Step 2 & 3: Remove commas and parse as integers
        const numbers = parts.map(part => parseInt(part.replace(/,/g, ''), 10));

        // Step 4: Find the highest number
        return Math.max(...numbers);
    }

    private async handleTipAmount(baseAmount: number, metaData: any): Promise<{ tipAmount: number; tipOptionId: string | null }> {
        try {
            const customTipAmount = metaData.customTipAmount ? parseFloat(metaData.customTipAmount) : undefined;
            const tipOptionId = metaData.tipOptionId || null;
            let finalTipAmount = 0;
            let finalTipOptionId = null;

            if (customTipAmount !== undefined && !isNaN(customTipAmount) && customTipAmount >= 0) {
                try {
                    // Safely create custom tip option
                    const customTipOption = await this.tipOptionsService.create({
                        label: `Custom $${customTipAmount}`,
                        type: TipType.CUSTOM,
                        value: customTipAmount,
                        isActive: true,
                        sortOrder: 1000
                    });
                    finalTipAmount = customTipAmount;
                    finalTipOptionId = customTipOption._id.toString();
                } catch (error) {
                    console.error('Error creating custom tip option:', error);
                    // Fallback to using just the amount if tip option creation fails
                    finalTipAmount = customTipAmount;
                }
            } else if (tipOptionId) {
                try {
                    // Safely calculate tip from existing option
                    const calculatedTip = await this.tipOptionsService.calculateTipAmount(baseAmount, tipOptionId);
                    finalTipAmount = calculatedTip;
                    finalTipOptionId = tipOptionId;
                } catch (error) {
                    console.error('Error calculating tip amount:', error);
                }
            }

            return {
                tipAmount: finalTipAmount,
                tipOptionId: finalTipOptionId
            };
        } catch (error) {
            console.error('Error in handleTipAmount:', error);
            // Return safe default values if anything goes wrong
            return {
                tipAmount: 0,
                tipOptionId: null
            };
        }
    }

    async handleStripeEvent(signature: string, workspace: string, payload: Buffer) {
        const workspaceData = await this.workspaceService.findOne(workspace);
        if (!workspaceData) {
            throw new BadRequestException('Workspace not found');
        }
        const stripeObject = workspaceData.payments.find(payment => payment.type === 'stripe');
        const endpointSecret = stripeObject.webhookSecret;
        const stripe = new Stripe(stripeObject.secretKey)

        let event: Stripe.Event;

        // Step 1: Verify the Stripe signature
        try {
            event = stripe.webhooks.constructEvent(
                payload, // Stripe SDK expects the raw body as a string
                signature,
                endpointSecret
            );
        } catch (err) {
            console.log(`Webhook Error: ${err.message}`);
            throw new BadRequestException('Webhook Error: Invalid signature.');
        }

        const handleCheckAndCreateBooking = async (event: Stripe.Event) => {
            const paymentIntent = event.data.object as Stripe.PaymentIntent;
            const metaData = paymentIntent.metadata;
            const bookingRequestId = metaData.bookingRequestId;
            const bookingRequest = await this.bookingRequestService.findOne(bookingRequestId, workspace);

            // return {bookingRequest, addonObject, extraObject, metaData, paymentIntent, client}
            return { bookingRequest, metaData, paymentIntent };
        }

        // Step 2: Determine the type of event
        switch (event.type) {
            case 'payment_intent.succeeded':
                // Step 3: Process the event
                const {bookingRequest, metaData, paymentIntent} = await handleCheckAndCreateBooking(event);

                if (!bookingRequest) return;
                const chargesData = (paymentIntent as any).charges?.data?.[0];
                const bookingRequestId = bookingRequest._id;
                const baseAmount = paymentIntent.amount / 100;

                // Safely handle tip calculation without affecting main payment flow
                let tipData = {
                    tipAmount: 0,
                    tipOptionId: null
                };
                try {
                    tipData = await this.handleTipAmount(baseAmount, metaData);
                } catch (error) {
                    console.error('Error handling tip amount:', error);
                }

                const userPayment = await this.userPaymentService.findByBookingId(bookingRequestId);
                const paymentData = {
                    paymentStatus: PaymentStatus.Succeeded,
                    paymentMethod: PaymentMethod.Card,
                    stripePaymentId: paymentIntent.id,
                    amount: baseAmount,
                    currency: paymentIntent.currency,
                    cardBrand: chargesData?.payment_method_details?.card.brand || "",
                    cardLastFour: chargesData?.payment_method_details?.card.last4 || "",
                    stripeChargeId: chargesData?.id || "",
                    paymentDate: new Date(paymentIntent.created * 1000),
                    tipOption: tipData.tipOptionId,
                    tipAmount: tipData.tipAmount
                };

                if (userPayment) {
                    try {
                        await this.userPaymentService.update(userPayment._id, paymentData);
                    } catch (error) {
                        console.error('Error updating user payment:', error);
                        // Continue with job creation even if tip update fails
                    }
                }

                // Create job without depending on tip processing
                const createdJob = await this.jobsService.create({
                    bookingRequest: bookingRequestId,
                    service: metaData.service,
                    addons: bookingRequest.addons.map(item => {
                        return {
                            quantity: item.quantity,
                            addon: item.addon,
                        }
                    }),
                    extras: bookingRequest.extras.map(item => {
                        return {
                            quantity: item.quantity,
                            extra: item.extra,
                        }
                    }),
                    frequency: JSON.parse(metaData.priceDetail).titlefreq,
                    hours: parseFloat(metaData?.totalHours) || 0,
                    notes: metaData.specialInstruction,
                    price: baseAmount,
                    suitableDate: metaData.suitableDate as unknown as Date,
                    workspace: workspace,
                    discountCode: metaData.discountCode,
                    attachments: [],
                    staffs: [],
                    status: 'unassigned',
                    video: '',
                    square_ft: bookingRequest.square_ft,
                    duration: bookingRequest.duration,
                    endDate: bookingRequest.endDate,
                    timezone: bookingRequest.timezone,
                })

                // Create invoice with safe tip amount
                await this.invoicesService.create({
                    hours: parseFloat(metaData?.totalHours) || 0,
                    price: baseAmount,
                    status: 'paid',
                    workspace: workspace,
                    discountCode: metaData.discountCode,
                    job: createdJob._id.toString(),
                    tipAmount: tipData.tipAmount
                })

                break;
            case 'customer.subscription.created':
                const subscription = event.data.object;
                // Handle new subscription creation
                break;
            // Add more cases as needed for other event types
            default:
                console.log(`Unhandled event type ${event.type}`);
        }

        // Step 4: Respond appropriately
        // Since we're not in an HTTP handler here, you'll return a value indicating success,
        // and then in your controller, you'll send the HTTP response.
        return { received: true };
    }

}