import { forwardRef, Module } from '@nestjs/common';
import { WebhooksController } from './webhooks.controller';
import { WebhooksService } from './webhooks.service';
import { WorkspaceModule } from "../workspace/workspace.module";
import { ClientsModule } from "../clients/clients.module";
import { PropertyModule } from "../property/property.module";
import { BookingRequestModule } from "../booking-request/booking-request.module";
import { BookingTypesModule } from "../booking-types/booking-types.module";
import { InvoicesModule } from "../invoices/invoices.module";
import { JobsModule } from "../jobs/jobs.module";
import { UserPaymentModule } from "../payment/user_payment/user_payment.module";
import { TipOptionsModule } from "../tip-options/tip-options.module";

@Module({
    imports: [
        forwardRef(() => WorkspaceModule),
        forwardRef(() => ClientsModule),
        forwardRef(() => PropertyModule),
        forwardRef(() => BookingRequestModule),
        forwardRef(() => BookingTypesModule),
        forwardRef(() => InvoicesModule),
        forwardRef(() => JobsModule),
        forwardRef(() => UserPaymentModule),
        forwardRef(() => TipOptionsModule),
    ],
    controllers: [WebhooksController],
    providers: [WebhooksService],
    exports: [WebhooksService]
})
export class WebhooksModule {}
