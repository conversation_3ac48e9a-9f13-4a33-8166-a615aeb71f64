import {
  Controller,
  HttpCode,
  Headers,
  HttpStatus,
  Post,
  Body, Param,
  RawBodyRequest, Req,
} from '@nestjs/common';
import { WebhooksService } from './webhooks.service';
import { ApiTags } from '@nestjs/swagger';

@Controller('webhooks')
@ApiTags('Webhooks')
export class WebhooksController {
  constructor(private webhooksService: WebhooksService) {}

  @HttpCode(HttpStatus.OK)
  @Post('stripe/:workspace')
  handleUser(
    @Headers('stripe-signature') signature: string,
    @Param('workspace') workspace: string,
    @Req() req: RawBodyRequest<Request>,
  ) {
    const payload = req.rawBody;
    return this.webhooksService.handleStripeEvent(signature, workspace, payload);
  }
}
