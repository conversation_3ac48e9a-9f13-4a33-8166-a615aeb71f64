import { <PERSON><PERSON><PERSON><PERSON>, ExecutionContext, forwardRef, Inject, Injectable, NestInterceptor } from "@nestjs/common";
import { map, Observable } from "rxjs";
import { BookingTypesService } from "../booking-types/booking-types.service";
import { BookingRequest } from "./booking-request.schema";
import { AddressService } from "../address/address.service";
import { BookingFrequencyService } from "../booking-frequency/booking-frequency.service";

@Injectable()
export class BookingRequestInterceptor implements NestInterceptor {
  constructor(
    @Inject(forwardRef(()=> BookingFrequencyService))
    private bookingFrequencyService: BookingFrequencyService,
    private BookingTypesService: BookingTypesService,
    @Inject(forwardRef(() => AddressService))
    private addressService: AddressService,
  ) {}
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    return next.handle()
      .pipe(map(async (data) => {
        let result = data.data;
        if(result?.length){
          result = result.map(async (item:BookingRequest) => {
            let serviceID = item.service
            let addressID = item.address
            let frequencyID = item.frequency
            if(serviceID){
              item.service = await this.BookingTypesService.getBookingType(serviceID.toString())
            }
            if(addressID){
              try {
                item.address = await this.addressService.getAddress(addressID.toString())
              }catch (e){
                item.address = addressID
              }
            }
            if(frequencyID){
              item.frequency = await this.bookingFrequencyService.findOne(frequencyID.toString(),item.workspace.toString())
            }
            return item
          })
          await Promise.all(result)
        }
        return { ...data }
      }));
  }
}
