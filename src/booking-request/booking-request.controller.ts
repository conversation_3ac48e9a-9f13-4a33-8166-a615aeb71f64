import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpException,
  HttpStatus,
  Param,
  Post,
  Query,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { BookingRequestService } from './booking-request.service';
import {
  CollectionDto,
  CollectionResponse,
} from '@forlagshuset/nestjs-mongoose-paginate';
import { BookingRequest } from './booking-request.schema';
import BookingRequestDto from './Dto/BookingRequestDto';
import { Patch } from '@nestjs/common/decorators/http/request-mapping.decorator';
import UpdateBookingRequestDto from './Dto/UpdateBookingRequestDto';
import { BookingRequestInterceptor } from './booking-request.interceptor';
import mongoose from 'mongoose';
import { AuthGuard } from '../auth/auth.guard';
import { ACGuard, UseRoles } from 'nest-access-control';
import ROLE_RESOURCES from '../@core/constants/roles.resources';
import { ApiTags } from '@nestjs/swagger';
import { NewBookingRequestDto } from "./Dto/NewBookingRequest.dto";

@Controller(ROLE_RESOURCES.BOOKING_REQUEST)
@ApiTags('Booking-request')
export class BookingRequestController {
  constructor(private bookingRequestService: BookingRequestService) {}

  @HttpCode(HttpStatus.OK)
  @Get('/')
  @UseInterceptors(BookingRequestInterceptor)
  async getBookingRequests(
    @Query()
    collectionDto: CollectionDto,
    @Body('workspace') workspace: string,
  ): Promise<CollectionResponse<BookingRequest>> {
    if (!collectionDto?.filter) {
      collectionDto = { ...collectionDto, filter: {} };
    }
    collectionDto.filter['workspace'] = {
      $eq: new mongoose.Types.ObjectId(workspace),
    };
    return this.bookingRequestService.findAll(collectionDto);
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.BOOKING_REQUEST,
    action: 'read',
    possession: 'any',
  })
  @Get('/clients/:id')
  // @UseInterceptors(BookingRequestInterceptor)
  async getBookingRequestsByClientId(
    @Query()
    collectionDto: CollectionDto,
    @Param('id') id: string,
    @Body('workspace') workspace: string,
  ): Promise<CollectionResponse<BookingRequest>> {
    return this.bookingRequestService.findAllByClientId(
      id,
      workspace,
      collectionDto,
    );
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.BOOKING_REQUEST,
    action: 'read',
    possession: 'any',
  })
  @Get(':id')
  async getBookingRequest(
    @Param('id') id: string,
    @Body('workspace') workspace: string,
  ) {
    let bookingRequest = await this.bookingRequestService.findOne(
      id,
      workspace,
    );
    if (!bookingRequest) {
      return new HttpException(
        'Booking request not found',
        HttpStatus.BAD_REQUEST,
      );
    }
    return {
      statusCode: HttpStatus.OK,
      message: 'Booking request found successfully',
      data: bookingRequest,
    };
  }

  @HttpCode(HttpStatus.OK)
  @Post('create')
  async createBookingRequest(@Body() bookingRequest: BookingRequestDto) {
    const { newBookingRequest, newQuote } = await this.bookingRequestService.create(bookingRequest);
    return {
      statusCode: HttpStatus.OK,
      message: 'Booking request created successfully',
      data: {
        bookingRequest: newBookingRequest,
        quote: newQuote
      },
    };
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.BOOKING_REQUEST,
    action: 'create',
    possession: 'any',
  })
  @Post('create-new')
  async createNewBookingRequest(@Body() nouveauBookingRequest: NewBookingRequestDto) {
    const { newBookingRequest, newQuote, client, property } = await this.bookingRequestService.createNewBooking(nouveauBookingRequest);
    return {
      statusCode: HttpStatus.OK,
      message: 'Booking request created successfully',
      data: {
        bookingRequest: newBookingRequest,
        quote: newQuote,
        client,
        property
      },
    };
  }

  @HttpCode(HttpStatus.OK)
  @Patch(':id')
  async updateBookingRequest(
    @Param('id') id: string,
    @Body() bookingRequest: UpdateBookingRequestDto,
  ) {
    return this.bookingRequestService.update(id, bookingRequest);
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.BOOKING_REQUEST,
    action: 'delete',
    possession: 'any',
  })
  @Delete(':id')
  async deleteBookingRequest(
    @Param('id') id: string,
    @Body('workspace') workspace: string,
  ) {
    return this.bookingRequestService.delete(id, workspace);
  }
}
