import { ApiProperty, OmitType, PartialType } from "@nestjs/swagger";
import { IsEnum, IsNotEmpty, IsNumber, IsOptional, IsPositive, IsString, ValidateNested } from "class-validator";
import BookingRequestDto from "./BookingRequestDto";
import { CreatePropertyDto } from "../../property/Dto/createProperty.dto";
import { Type } from "class-transformer";
import { PaymentMethod } from "../../payment/user_payment/schema/user_payment.schema";

class CreatePropertyDtoNoClient extends PartialType(CreatePropertyDto) {
}

class CreateBookingRequestDto extends OmitType(BookingRequestDto, ["frequency", "square_ft"] as const) {
  @ApiProperty()
  @IsOptional()
  @IsString()
  readonly frequency: string;

  @ApiProperty()
  @IsOptional()
  @IsPositive()
  @IsNumber()
  readonly square_ft?: number;
}

export class NewBookingRequestDto {
  @ApiProperty()
  @ValidateNested()
  @Type(() => CreateBookingRequestDto)
  bookingRequest: CreateBookingRequestDto;

  @ApiProperty()
  @ValidateNested()
  @Type(() => CreatePropertyDtoNoClient)
  property: CreatePropertyDtoNoClient;

  @ApiProperty()
  @IsNotEmpty()
  workspace: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsEnum(PaymentMethod)
  paymentMethod: PaymentMethod;

  @ApiProperty()
  @IsOptional()
  @IsString()
  readonly timezone?: string;
}