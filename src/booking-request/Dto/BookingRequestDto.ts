import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
    IsBoolean,
    IsDate,
    IsEmail,
    IsEnum,
    IsNotEmpty,
    IsNumber,
    IsOptional,
    IsPhoneNumber, IsPositive,
    IsString,
    ValidateNested
} from "class-validator";
import { Type } from 'class-transformer';
import { TransactionTypes } from 'src/transactions/transactions.schema';
import { Prop } from "@nestjs/mongoose";
import { JobFrequency } from "../../booking-frequency/booking-frequency.types";

class Address {
    @ApiProperty()
    @IsString()
    readonly street_one: string;

    @ApiProperty()
    @IsOptional()
    readonly street_two?: string;

    @ApiProperty()
    @IsString()
    readonly city: string;

    @ApiProperty()
    @IsString()
    readonly state: string;

    @ApiProperty()
    @IsOptional()
    readonly zip?: string;

    @ApiProperty()
    @IsNotEmpty()
    readonly coordinates: {
        lat: number;
        lng: number;
    };
}

class Addon {
    @ApiProperty()
    @IsNotEmpty()
    @IsNumber()
    quantity: number;

    @ApiProperty()
    @IsNotEmpty()
    @IsString()
    addon: string;
}

class Extra {
    @ApiProperty()
    @IsNotEmpty()
    @IsNumber()
    quantity: number;

    @ApiProperty()
    @IsNotEmpty()
    @IsString()
    extra: string;
}
export default class BookingRequestDto {
    @ApiProperty()
    @IsNotEmpty()
    @IsString()
    readonly firstName: string;

    @ApiProperty()
    @IsNotEmpty()
    @IsString()
    readonly lastName: string;

    @ApiProperty()
    @IsNotEmpty()
    @IsString()
    readonly phone: string;

    @ApiProperty()
    @IsNotEmpty()
    @IsEmail()
    readonly email: string;

    @ApiProperty()
    @IsNotEmpty()
    @Type(() => Address)
     address: Address;

    @ApiProperty()
    @IsOptional()
    @IsString()
    readonly video: string;

    @ApiProperty()
    @IsOptional()
    @IsString({ each: true })
    readonly attachments: string[];

    @ApiProperty()
    @IsOptional()
    @IsString()
    readonly jobRelated: string;

    @ApiProperty()
    @IsNotEmpty()
    @IsString()
    readonly service: string;

    @ApiProperty()
    @IsOptional()
    @IsDate()
    readonly suitableDate: Date;

    @ApiProperty()
    @IsOptional()
    @ValidateNested({ each: true })
    @Type(() => Addon)
    readonly addons: Addon[];

    @ApiProperty()
    @IsOptional()
    @ValidateNested({ each: true })
    @Type(() => Extra)
    readonly extras?: Extra[];

    @ApiProperty()
    @IsNotEmpty()
    @IsString()
    readonly frequency: string;

    @ApiProperty()
    @IsOptional()
    @IsString()
    readonly notes: string;

    @ApiProperty()
    @IsOptional()
    @IsNumber()
    readonly price: number;

    @ApiProperty()
    @IsOptional()
    @IsNumber()
    readonly hours: number;

    @ApiProperty()
    @IsOptional()
    @IsString()
    workspace: string;

    @ApiProperty({
        description: 'is booked online?',
        default: false,
        example: true,
    })
    @IsOptional()
    @IsBoolean()
    onlineBooking: boolean;

    @ApiProperty()
    @IsOptional()
    @IsEnum(['pending', 'approved', 'rejected'])
    readonly status: 'pending' | 'approved' | 'rejected';

    @ApiPropertyOptional({ type: String })
    @IsOptional()
    @IsString()
    transactionID: string;

    @ApiPropertyOptional({ type: String, enum: TransactionTypes })
    @IsOptional()
    @IsString()
    transactionType: string;

    @ApiProperty()
    @IsOptional()
    @IsString()
    discountCode: string;

    @ApiProperty()
    @IsPositive()
    @IsNumber()
    @IsNotEmpty()
    square_ft: number;

    @ApiProperty()
    @IsPositive()
    @IsNumber()
    duration: number;

    @ApiProperty()
    @IsOptional()
    @IsDate()
    endDate?: Date;


    @ApiProperty()
    @IsOptional()
    @IsString()
    readonly timezone?: string;
}
