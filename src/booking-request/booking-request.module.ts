import { Module, forwardRef } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { BookingRequest, BookingRequestSchema } from './booking-request.schema';
import { BookingRequestService } from './booking-request.service';
import { BookingRequestController } from './booking-request.controller';
import { SendEmailModule } from '../send-email/send-email.module';
import { BookingTypesModule } from '../booking-types/booking-types.module';
import { ClientsModule } from 'src/clients/clients.module';
import { Clients, ClientsSchema } from 'src/clients/clients.schema';
import { ClientsService } from 'src/clients/clients.service';
import { StorageModule } from '../storage/storage.module';
import { MediasModule } from '../medias/medias.module';
import { ClientTypeModule } from '../client-type/client-type.module';
import { UsersModule } from '../users/users.module';
import { ClientsDataModule } from '../clients-data/clients-data.module';
import { AddressModule } from '../address/address.module';
import { QuotesModule } from '../quotes/quotes.module';
import { BookingTimeArrivalModule } from '../booking-time-arrival/booking-time-arrival.module';
import { BookingFrequencyModule } from '../booking-frequency/booking-frequency.module';
import { PropertyModule } from '../property/property.module';
import { UserPaymentModule } from '../payment/user_payment/user_payment.module';
import {
    FrequencyDiscount,
    FrequencyDiscountSchema,
} from '../frequency-discount/frequency-discount.schema';
import { FrequencyDiscountModule } from '../frequency-discount/frequency-discount.module';
import { JobsModule } from '../jobs/jobs.module';
import { AuthenticationService } from 'src/iam/authentication/authentication.service';
import { IamModule } from 'src/iam/iam.module';
import { WorkspaceModule } from 'src/workspace/workspace.module';
import { Jobs, JobsSchema } from 'src/jobs/jobs.schema';
import { SmtpConfigModule } from '../smtp-config/smtp-config.module';

@Module({
    imports: [
        forwardRef(() => ClientsModule),
        forwardRef(() => UsersModule),
        forwardRef(() => StorageModule),
        forwardRef(() => MediasModule),
        forwardRef(() => ClientTypeModule),
        forwardRef(() => ClientsDataModule),
        forwardRef(() => AddressModule),
        forwardRef(() => QuotesModule),
        forwardRef(() => BookingTimeArrivalModule),
        forwardRef(() => BookingFrequencyModule),
        forwardRef(() => PropertyModule),
        forwardRef(() => UserPaymentModule),
        forwardRef(() => FrequencyDiscountModule),
        forwardRef(() => JobsModule),
        forwardRef(() => IamModule),
        forwardRef(() => WorkspaceModule),
        forwardRef(() => SendEmailModule),
        forwardRef(() => BookingTypesModule),
        forwardRef(() => SmtpConfigModule),
        MongooseModule.forFeature([
            { name: BookingRequest.name, schema: BookingRequestSchema },
            { name: Clients.name, schema: ClientsSchema },
            { name: Jobs.name, schema: JobsSchema },
        ]),
    ],
    providers: [BookingRequestService],
    exports: [BookingRequestService],
    controllers: [BookingRequestController],
})
export class BookingRequestModule {}
