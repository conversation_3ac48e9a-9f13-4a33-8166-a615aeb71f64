import {
    BadRequestException,
    HttpException,
    HttpStatus,
    Inject,
    Injectable,
    forwardRef,
    InternalServerErrorException,
} from '@nestjs/common';
import mongoose, { Model } from 'mongoose';
import {
    BookingRequest,
    BookingRequestDocument,
} from './booking-request.schema';
import { InjectModel } from '@nestjs/mongoose';
import BookingRequestDto from './Dto/BookingRequestDto';
import {
    CollectionDto,
    DocumentCollector,
} from '@forlagshuset/nestjs-mongoose-paginate';
import { Clients, ClientsDocument } from '../clients/clients.schema';
import UpdateBookingRequestDto from './Dto/UpdateBookingRequestDto';
import { SendEmailService } from '../send-email/send-email.service';
import { BookingTypesService } from '../booking-types/booking-types.service';
import { NotificationEmailAdminBookingRequest, BookingConfirmationEmail } from '../send-email/send-email.template';
import { ClientsService } from 'src/clients/clients.service';
import { StorageService } from '../storage/services/storage.service';
import { paginateWithCount } from 'src/utils/helpers';
import { AddressService } from '../address/address.service';
import { QuotesService } from '../quotes/quotes.service';
import { BookingTimeArrivalService } from '../booking-time-arrival/booking-time-arrival.service';
import dayjs from 'dayjs';
import { UsersService } from '../users/users.service';
import { PropertyService } from '../property/property.service';
import { NewBookingRequestDto } from './Dto/NewBookingRequest.dto';
import { UserPaymentService } from '../payment/user_payment/user_payment.service';
import {
    PaymentMethod,
    PaymentStatus,
} from '../payment/user_payment/schema/user_payment.schema';
import { JobsService } from '../jobs/jobs.service';
import { Jobs, JobsDocument } from 'src/jobs/jobs.schema';
import { Quote_Email } from 'src/quotes/templates/quote_email';
import { AuthenticationService } from 'src/iam/authentication/authentication.service';
import { WorkspaceService } from 'src/workspace/workspace.service';
import { BookingFrequencyService } from '../booking-frequency/booking-frequency.service';
import { SmtpConfigService } from '../smtp-config/smtp-config.service';

@Injectable()
export class BookingRequestService {
    constructor(
        @Inject(forwardRef(() => AuthenticationService))
        private AuthenticationService: AuthenticationService,
        @Inject(forwardRef(() => WorkspaceService))
        private WorkspaceService: WorkspaceService,
        @Inject(forwardRef(() => BookingTimeArrivalService))
        private bookingTimeArrivalService: BookingTimeArrivalService,
        @Inject(forwardRef(() => QuotesService))
        private quotesService: QuotesService,
        @Inject(forwardRef(() => AddressService))
        private addressService: AddressService,
        @Inject(forwardRef(() => StorageService))
        private storageService: StorageService,
        @Inject(forwardRef(() => BookingTypesService))
        private BookingTypesService: BookingTypesService,
        @Inject(forwardRef(() => SendEmailService))
        private SendEmailService: SendEmailService,
        @Inject(forwardRef(() => ClientsService))
        private ClientsService: ClientsService,
        @Inject(forwardRef(() => UsersService))
        private usersService: UsersService,
        @Inject(forwardRef(() => PropertyService))
        private propertyService: PropertyService,
        @Inject(forwardRef(() => UserPaymentService))
        private userPaymentService: UserPaymentService,
        @Inject(forwardRef(() => JobsService))
        private jobsService: JobsService,
        @InjectModel(BookingRequest.name)
        private bookingRequestModel: Model<BookingRequestDocument>,
        @InjectModel(Jobs.name) private jobsModel: Model<JobsDocument>,
        @Inject(forwardRef(() => BookingFrequencyService))
        private bookingFrequencyService: BookingFrequencyService,
        @Inject(forwardRef(() => SmtpConfigService))
        private smtpConfigService: SmtpConfigService,
    ) {}

    async findAll(collectionDto: CollectionDto): Promise<any> {
        const collector = new DocumentCollector<BookingRequestDocument>(
            this.bookingRequestModel,
        );
        if (collectionDto?.sort) {
            // @ts-ignore
            collectionDto.sort = JSON.parse(collectionDto.sort);
            // @ts-ignore
            collectionDto.sorter = collectionDto.sort;
        }
        return collector.find(collectionDto);
    }

    async findAllByClientId(
        id: string,
        workspace: string,
        collectionDto: CollectionDto,
    ): Promise<any> {
        const client = await this.ClientsService.getClient({ id, workspace });

        const mainEmail = client.email.find(
            (i: { type: string }) => i.type == 'main',
        ).item;

        const bookingRequestsWithPagination = await this.bookingRequestModel
            .find({
                email: mainEmail,
                workspace: new mongoose.Types.ObjectId(workspace),
            })
            .limit(collectionDto?.limit || 50)
            .skip((collectionDto?.page || 0) * (collectionDto?.limit || 50))
            .populate({
                path: 'frequency',
                populate: 'discountFrequency',
            })
            .populate(['service']);

        const bookingRequests = await this.bookingRequestModel.find({
            email: mainEmail,
            workspace: new mongoose.Types.ObjectId(workspace),
        });

        const pagination = paginateWithCount(
            collectionDto,
            bookingRequests.length,
        );

        return {
            data: bookingRequestsWithPagination?.map((item) => {
                return {
                    _id: item._id,
                    service: item.service?.name,
                    status: item.status,
                    dateCreated: item.dateCreated,
                    workspace: item.workspace,
                    frequency: item.frequency?.title,
                    clientType: item.service?.clientType,
                    title: item.title,
                    price: item.price,
                };
            }),
            pagination: {
                ...pagination,
            },
        };
    }

    async findExistingUniqueBookingRequest(
        workspace: string,
        email: string,
        address: any,
        service: string,
        suitableDate: Date,
    ): Promise<BookingRequestDocument[]> {
        const found = await this.bookingRequestModel.aggregate([
            {
                $match: {
                    $and: [
                        { workspace: workspace },
                        { email: email },
                        { address: address },
                        { service: service },
                        { suitableDate: suitableDate },
                    ],
                },
            },
        ]);
        return found;
    }

    async findOne(id: string, workspace: string): Promise<any> {
        return await this.bookingRequestModel
            .findOne({
                _id: id,
                workspace: new mongoose.Types.ObjectId(workspace),
            })
            .populate([
                {
                    path: 'service',
                    populate: { path: 'clientType' }, // populate addons in service
                },
                {
                    path: 'frequency',
                    populate: 'discountFrequency',
                },
                'addons.addon',
                'extras.extra',
                {
                    path: 'address',
                    strictPopulate: false,
                },
            ])
            .exec();
    }

    async create(bookingRequest: BookingRequestDto): Promise<any> {
        try {
            const dateCreated = new Date();

            // Check client with email + workspace
            let client = await this.ClientsService.getClientByEmailWorkspace(
                bookingRequest.email,
                bookingRequest.workspace
            );

            if (!client) {
                // Create new client within transaction
                client = await this.ClientsService.createClient(
                    {
                        firstName: bookingRequest.firstName,
                        lastName: bookingRequest.lastName,
                        phone: [{ type: 'main', item: bookingRequest.phone }],
                        email: [{ type: 'main', item: bookingRequest.email }],
                        password: bookingRequest.phone,
                    },
                    bookingRequest.workspace,
                );
            }

            const address = await this.addressService.create({
                ...bookingRequest.address,
                coordinates: [
                    bookingRequest.address.coordinates.lng,
                    bookingRequest.address.coordinates.lat,
                ],
            });

            const timeStart = dayjs(bookingRequest.suitableDate).hour();
            const bookingTimeArrival = await this.bookingTimeArrivalService.create({
                timeStart,
                timeEnd: timeStart + 2,
            });

            const newBookingRequest = await this.bookingRequestModel.create({
                ...bookingRequest,
                address: address._id,
                service: new mongoose.Types.ObjectId(bookingRequest.service),
                frequency: new mongoose.Types.ObjectId(bookingRequest.frequency),
                workspace: new mongoose.Types.ObjectId(bookingRequest.workspace),
                arrival: bookingTimeArrival._id,
                title: `${bookingRequest.firstName} ${bookingRequest.lastName} - ${dateCreated}`,
                dateCreated,
            });

            // Get workspace details for email
            const workspaceObject = await this.WorkspaceService.findOne(bookingRequest.workspace);
            if (!workspaceObject) {
                throw new BadRequestException('Workspace not found');
            }

            const smtpConfig = await this.smtpConfigService.findLatest(bookingRequest.workspace);
            const companyEmail = smtpConfig?.fromEmail || process.env.EMAIL;
            const companyName = workspaceObject.name || process.env.COMPANY_NAME || "MaidProfit";

            // Get service and frequency details
            const bookingType = await this.BookingTypesService.getBookingType(bookingRequest.service);
            const frequency = await this.bookingFrequencyService.findOne(bookingRequest.frequency, bookingRequest.workspace);

            // Send confirmation email to client
            const confirmationEmailContent = BookingConfirmationEmail({
                firstName: bookingRequest.firstName,
                lastName: bookingRequest.lastName,
                phone: bookingRequest.phone,
                email: bookingRequest.email,
                address: `${address.street_one} ${address.city} ${address.state} ${address.zip || ''}`,
                service: bookingType.name,
                suitableDate: dayjs(bookingRequest.suitableDate).format('MMMM D, YYYY h:mm A'),
                frequency: frequency.title,
                price: bookingRequest.price,
                companyName: companyName,
                companyLogo: workspaceObject.logo || '',
                workspace: bookingRequest.workspace,
            });

            await this.SendEmailService.sendEmailGoogle(
                {
                    from: companyEmail,
                    toEmail: bookingRequest.email,
                    subject: `Booking Confirmation - ${companyName}`,
                    body: confirmationEmailContent,
                },
                bookingRequest.workspace,
            );

            if (bookingRequest.onlineBooking) {
                // Send notification to Admins
                const attachments = bookingRequest.attachments?.map((item) =>
                    this.storageService.getPublicUrl(item),
                );
                const admin = await this.usersService.findOwnerByWorkspace(
                    bookingRequest.workspace,
                );

                if (admin && admin.email) {
                    const bodyContent = NotificationEmailAdminBookingRequest({
                        firstName: bookingRequest.firstName,
                        lastName: bookingRequest.lastName,
                        phone: bookingRequest.phone,
                        email: bookingRequest.email,
                        address: `${address.street_one} ${address.city} ${address.state} ${address.zip || ''}`,
                        service: bookingType.name,
                        attachments: attachments,
                    });

                    await this.SendEmailService.sendEmailGoogle(
                        {
                            toEmail: admin.email,
                            body: bodyContent,
                            subject: 'Booking online created - ' + newBookingRequest.title,
                        },
                        bookingRequest.workspace,
                    );
                }
            }

            let newQuote = null;
            if (!bookingRequest.onlineBooking) {
                newQuote = await this.quotesService.create({
                    discountCode: bookingRequest.discountCode,
                    addons: bookingRequest.addons,
                    attachments: bookingRequest.attachments,
                    extras: bookingRequest.extras,
                    frequency: bookingRequest.frequency,
                    notes: bookingRequest.notes,
                    price: bookingRequest.price,
                    service: bookingRequest.service,
                    status: 'waiting',
                    suitableDate: bookingRequest.suitableDate,
                    video: bookingRequest.video,
                    workspace: bookingRequest.workspace,
                    bookingRequest: newBookingRequest.id,
                    hours: bookingRequest.hours,
                    duration: bookingRequest.duration,
                    endDate: bookingRequest.endDate,
                    timezone: bookingRequest.timezone,
                });
            }

            if (newBookingRequest._id) {
                return {
                    newBookingRequest,
                    newQuote,
                };
            }

            throw new BadRequestException(
                `Failed to create new booking request, try again`,
            );
        } catch (error) {
            throw new BadRequestException(error.message || 'Failed to create booking request');
        }
    }

    async createNewBooking(bookingDto: NewBookingRequestDto) {
        try {
            const { bookingRequest, property, workspace, paymentMethod } = bookingDto;
            const {
                email,
                service,
                firstName,
                lastName,
                phone,
                addons,
                extras,
                address,
                frequency,
                suitableDate,
                price
            } = bookingRequest;
            
            // Validate required fields
            if (!email || !service || !firstName || !lastName || !phone || !address || !frequency || !suitableDate) {
                throw new BadRequestException('Missing required booking information');
            }

            const bookingType = await this.BookingTypesService.getBookingType(service);
            
            // Check existing client with email + workspace
            let client = await this.ClientsService.getClientByEmailWorkspace(
                email,
                workspace
            );

            if (!client) {
                // Create new client within transaction
                client = await this.ClientsService.createClient(
                    {
                        firstName,
                        lastName,
                        phone: [{ item: phone, type: 'main' }],
                        email: [{ item: email, type: 'main' }],
                        password: phone,
                    },
                    workspace,
                );
            }

            let propertyExist = await this.propertyService.findPropertyByAddress(
                {
                    street_one: address.street_one,
                    city: address.city,
                    state: address.state,
                },
                workspace,
                (client as any)?.Id || (client as any)?.data?.id,
            );

            if (!propertyExist) {
                propertyExist = await this.propertyService.createProperty(
                    {
                        address: address,
                        half_bath: property.half_bath || 0,
                        bathroom: property.bathroom || 0,
                        bedroom: property.bedroom || 0,
                        square_ft: property.square_ft || 0,
                        service: service,
                        clientType: (bookingType.clientType as any)._id.toString(),
                        clientId: (client as any).Id || (client as any)?.data?.id,
                    },
                    workspace,
                );
            }

            const bookingResult = await this.create({
                ...bookingRequest,
                status: 'approved',
                attachments: [],
                jobRelated: '',
                transactionID: '',
                transactionType: '',
                video: '',
                workspace,
                square_ft: bookingRequest.square_ft ?? property.square_ft,
            });

            await this.userPaymentService.create({
                bookingRequest: bookingResult.newBookingRequest._id,
                client: client._id,
                paymentStatus: PaymentStatus.Pending,
                paymentMethod,
                amount: bookingRequest.price,
            });

            // Get workspace details for email
            const workspaceObject = await this.WorkspaceService.findOne(workspace);
            if (!workspaceObject) {
                throw new BadRequestException('Workspace not found');
            }

            const smtpConfig = await this.smtpConfigService.findLatest(workspace);
            const companyEmail = smtpConfig?.fromEmail || process.env.EMAIL;
            const companyName = workspaceObject.name || process.env.COMPANY_NAME || "MaidProfit";

            // Get frequency details
            const frequencyDetails = await this.bookingFrequencyService.findOne(frequency, workspace);
            if (!frequencyDetails) {
                throw new BadRequestException('Frequency not found');
            }

            // Send confirmation email to client
            const confirmationEmailContent = BookingConfirmationEmail({
                firstName,
                lastName,
                phone,
                email,
                address: `${address.street_one} ${address.city} ${address.state} ${address.zip || ''}`,
                service: bookingType.name,
                suitableDate: dayjs(suitableDate).format('MMMM D, YYYY h:mm A'),
                frequency: frequencyDetails.title,
                price: price,
                companyName: companyName,
                companyLogo: workspaceObject.logo || '',
                workspace: workspace,
            });

            await this.SendEmailService.sendEmailGoogle(
                {
                    from: companyEmail,
                    toEmail: email,
                    subject: `Booking Confirmation - ${companyName}`,
                    body: confirmationEmailContent,
                },
                workspace,
            );

            return {
                ...bookingResult,
                property: propertyExist,
                client: (client as any)?.data || client,
            };
        } catch (err) {
            throw new InternalServerErrorException(err.message);
        }
    }

    async update(
        id: string,
        bookingRequest: UpdateBookingRequestDto,
    ): Promise<any> {
        const checkExist = await this.bookingRequestModel.findById(id).exec();
        if (!checkExist) {
            throw new HttpException(
                'Data Booking not existed',
                HttpStatus.CONFLICT,
            );
        }

        const address = await this.addressService.create({
            ...bookingRequest.address,
            coordinates: [
                bookingRequest.address.coordinates.lng,
                bookingRequest.address.coordinates.lat,
            ],
        });

        const updatedRequest = await this.bookingRequestModel.findByIdAndUpdate(
            id,
            {
                ...bookingRequest,
                service: new mongoose.Types.ObjectId(bookingRequest.service),
                frequency: new mongoose.Types.ObjectId(
                    bookingRequest.frequency,
                ),
                address: address,
                workspace: new mongoose.Types.ObjectId(
                    bookingRequest.workspace,
                ),
            },
            { new: true, runValidators: true },
        );

        const bookingType = await this.BookingTypesService.getBookingType(
            bookingRequest.service,
        );
        const attachments = bookingRequest.attachments?.map((item) =>
            this.storageService.getPublicUrl(item),
        );

        const bodyContent = NotificationEmailAdminBookingRequest({
            firstName: updatedRequest.firstName,
            lastName: updatedRequest.lastName,
            phone: updatedRequest.phone,
            email: updatedRequest.email,
            address:
                address.street_one +
                ' ' +
                address.city +
                ' ' +
                address.state +
                (address.zip ? ' ' + address.zip : ''),
            service: bookingType.name,
            attachments: attachments,
        });

        const email = await this.SendEmailService.sendEmail({
            userID: process.env.ADMIN_USER_ID,
            fromEmailName: 'booking',
            subject: 'Booking request created - ' + updatedRequest.title,
            body: bodyContent,
            workspace: bookingRequest.workspace,
        });

        return {
            statusCode: HttpStatus.OK,
            message: 'Booking request updated successfully',
            data: email,
        };
    }

    async delete(id: string, workspace: string): Promise<any> {
        const checkExist = await this.bookingRequestModel.findById(id).exec();
        if (!checkExist) {
            throw new HttpException(
                'Data Booking not existed',
                HttpStatus.CONFLICT,
            );
        }

        const quotes = await this.quotesService.findByBookingRequestId(
            id,
            workspace,
        );
        if (quotes) {
            await this.quotesService.delete(quotes._id.toString(), workspace);
        }

        const jobs = await this.jobsService.findByBookingRequestId(
            id,
            workspace,
        );
        if (jobs) {
            await this.jobsService.delete(jobs._id.toString(), workspace);
        }

        const deletedRequest = await this.bookingRequestModel.findOneAndRemove({
            _id: new mongoose.Types.ObjectId(id),
            workspace: new mongoose.Types.ObjectId(workspace),
        });

        return {
            statusCode: HttpStatus.OK,
            message: 'Booking request deleted successfully',
            data: deletedRequest,
        };
    }
}
