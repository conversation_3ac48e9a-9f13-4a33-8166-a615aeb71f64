import { <PERSON><PERSON>, <PERSON>hem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema, Types } from 'mongoose';
import { BookingAddon } from 'src/booking-addon/booking-addon.schema';
import { BookingExtra } from 'src/booking-extra/booking-extra.schema';
import { BookingFrequency } from 'src/booking-frequency/booking-frequency.schema';
import { BookingTypes } from 'src/booking-types/booking-types.schema';
import { Workspace } from '../workspace/workspace.schema';
import { Type } from 'class-transformer';
import { Address } from '../address/address.schema';
import { BookingTimeArrival } from 'src/booking-time-arrival/booking-time-arrival.schema';
import { JobFrequency } from "../booking-frequency/booking-frequency.types";

@Schema()
export class BookingRequest {
    _id: MongooseSchema.Types.ObjectId;

    @Prop({ required: true })
    title: string;

    @Prop({ required: true })
    firstName: string;

    @Prop({ required: true })
    lastName: string;

    @Prop({ required: true })
    phone: string;

    @Prop({ required: true })
    email: string;

    @Prop({
        type: Types.ObjectId,
        ref: Address.name,
        required: true,
    })
    address: string;

    @Prop()
    video: string;

    @Prop()
    attachments: string[];

    @Prop({ required: true, type: Types.ObjectId, ref: BookingTypes.name })
    service: BookingTypes;

    @Prop({
        type: [
            {
                quantity: { type: Number, required: false },
                addon: { type: Types.ObjectId, ref: BookingAddon.name },
                value: { type: String, required: false },
            },
        ],
    })
    addons: { quantity?: number; addon: BookingAddon; value?: string }[];

    @Prop({
        type: [
            {
                quantity: { type: Number },
                extra: { type: Types.ObjectId, ref: BookingExtra.name },
            },
        ],
        default: []
    })
    extras: { quantity: number; extra: BookingExtra }[];

    @Prop({ type: Types.ObjectId, ref: BookingFrequency.name })
    frequency: BookingFrequency;

    @Prop({ type: Types.ObjectId, ref: BookingTimeArrival.name })
    arrival: BookingTimeArrival;

    @Prop()
    suitableDate: Date;

    @Prop({ default: '' })
    notes: string;

    @Prop()
    jobRelated: string;

    @Prop({ default: false })
    onlineBooking: boolean;

    @Prop({ default: 'pending' })
    status: 'pending' | 'approved' | 'rejected';

    @Prop({ required: true })
    dateCreated: Date;

    @Prop({ required: true, type: Types.ObjectId, ref: Workspace.name })
    @Type(() => Workspace)
    workspace: Workspace;

    @Prop({ required: true, default: 0 })
    price: number;

    @Prop({ required: true, default: 0 })
    hours: number;

    @Prop({ required: false, default: null })
    discountCode: string;

    @Prop({ required: true, type: Number, default: 0 })
    square_ft: number

    @Prop()
    duration: number;

    @Prop()
    endDate?: Date;

    @Prop()
    timezone?: string;
}

export type BookingRequestDocument = BookingRequest & Document;

export const BookingRequestSchema =
    SchemaFactory.createForClass(BookingRequest);
