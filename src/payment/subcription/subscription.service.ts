import {
    forwardRef,
    HttpException,
    HttpStatus,
    Inject,
    Injectable,
} from '@nestjs/common';
import mongoose, { Model } from 'mongoose';
import { Subscription, SubStatus } from './subcription.schema';
import { InjectModel } from '@nestjs/mongoose';
import { CreateSubscriptionDtoDto } from './DTO/CreateSubscriptionDto.dto';
import UpdateSubscriptionDtoDto from './DTO/UpdateSubscriptionDto.dto';
import { WorkspaceService } from '../../workspace/workspace.service';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { TrialService } from '../trial/trial.service';

@Injectable()
export class SubscriptionService {
    constructor(
        @InjectModel(Subscription.name)
        private readonly subscriptionModel: Model<Subscription>,
        @Inject(forwardRef(() => WorkspaceService))
        private readonly workspaceService: WorkspaceService,
        @Inject(forwardRef(() => TrialService))
        private readonly trialService: TrialService,
        private eventEmitter: EventEmitter2,
    ) {}

    async findByFilters(
        customerID?: string,
        created?: Date,
        trial_start?: Date,
        trial_end?: Date,
        cancel_at?: Date,
        page = 1,
        limit = 10,
        sort = 'created',
        order = 'asc',
    ): Promise<Subscription[]> {
        const filter = {};

        if (customerID) {
            filter['customer'] = customerID;
        }
        if (created) {
            filter['created'] = created;
        }
        if (trial_start) {
            filter['trial_start'] = trial_start;
        }
        if (trial_end) {
            filter['trial_end'] = trial_end;
        }
        if (cancel_at) {
            filter['cancel_at'] = cancel_at;
        }

        // Check if the provided sort parameter is a valid field
        const validSortFields = [
            'created',
            'trial_start',
            'trial_end',
            'cancel_at',
        ];
        if (!validSortFields.includes(sort)) {
            sort = 'created';
        }

        return this.subscriptionModel
            .find(filter)
            .sort({ [sort]: order === 'asc' ? 1 : -1 })
            .skip((page - 1) * limit)
            .limit(limit)
            .exec();
    }

    async getCurrentSubscription(workspace: string): Promise<Subscription> {
        const workspaceData = await this.workspaceService.findOne(workspace);
        if (!workspaceData) {
            throw new HttpException('Workspace not found', 404);
        }
        const customerID = workspaceData.stripeAccountId;
        return this.subscriptionModel
            .findOne({ customer: customerID, cancel_at: null })
            .exec();
    }

    async createSubscription(
        subscription: CreateSubscriptionDtoDto,
    ): Promise<Subscription> {
        console.log('CreateSubscriptionDtoDto', subscription);
        const createdSubscription = await this.subscriptionModel.create({
            ...subscription,
            created: subscription.created
                ? new Date(subscription.created)
                : null,
            start_date: subscription.start_date
                ? new Date(subscription.start_date)
                : null,
            trial_start: subscription.start_date
                ? new Date(subscription.trial_start)
                : null,
            trial_end: subscription.trial_end
                ? new Date(subscription.trial_end)
                : null,
            packages: subscription.packages,
        });

        const workspaceData = await this.workspaceService.findByStripeAccountId(
            subscription.customer,
        );
        await this.trialService.convertTrialToSubscription(
            workspaceData.id,
            createdSubscription.id,
        );
        return createdSubscription.save();
    }

    async cancelSubscription(
        subscriptionId: string,
        workspaceId: string,
    ): Promise<Subscription> {
        const subscription = await this.subscriptionModel.findById(
            new mongoose.Types.ObjectId(subscriptionId),
        );
        if (!subscription) {
            throw new HttpException(
                'Subscription not found',
                HttpStatus.NOT_FOUND,
            );
        }

        await this.trialService.revertTrialStatus(workspaceId, subscription.id);

        subscription.cancel_at = new Date();
        subscription.status = SubStatus.CANCELED;
        return subscription.save();
    }

    async updateSubscription(
        id: string,
        subscription: UpdateSubscriptionDtoDto,
    ): Promise<Subscription> {
        return this.subscriptionModel.findByIdAndUpdate(id, subscription);
    }

    async getSubscriptionById(subscriptionId: string): Promise<Subscription> {
        return this.subscriptionModel.findById(subscriptionId).exec();
    }

    async findByStripeSubId(stripeId: string): Promise<Subscription> {
        return this.subscriptionModel.findOne({ stripeId }).exec();
    }
}
