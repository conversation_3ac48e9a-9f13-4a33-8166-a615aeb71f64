import { forwardRef, Module } from '@nestjs/common';
import { SubscriptionService } from './subscription.service';
import { SubscriptionController } from './subscription.controller';
import { MongooseModule } from '@nestjs/mongoose';
import { Subscription, SubscriptionSchema } from './subcription.schema';
import { WorkspaceModule } from '../../workspace/workspace.module';
import { UsersModule } from '../../users/users.module';
import { ClientsModule } from '../../clients/clients.module';
import { TrialModule } from "../trial/trial.module";

@Module({
  imports:[
      MongooseModule.forFeature([{ name: Subscription.name, schema: SubscriptionSchema }]),
      forwardRef(()=> ClientsModule),
      forwardRef(()=> UsersModule),
      forwardRef(()=> TrialModule),
      forwardRef(() => WorkspaceModule)
  ],
  providers: [SubscriptionService],
  controllers: [SubscriptionController],
  exports: [SubscriptionService],
})
export class SubscriptionModule {}
