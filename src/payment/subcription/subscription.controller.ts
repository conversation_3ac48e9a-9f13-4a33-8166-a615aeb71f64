import {
    Controller,
    Delete,
    Get,
    HttpCode,
    HttpStatus,
    Param,
    Req,
    UseGuards,
} from '@nestjs/common';
import { SubscriptionService } from './subscription.service';
import ROLE_RESOURCES from '../../@core/constants/roles.resources';
import { AuthGuard } from '../../auth/auth.guard';
import { ACGuard, UseRoles } from 'nest-access-control';

@Controller(ROLE_RESOURCES.SUBSCRIPTION)
export class SubscriptionController {
    constructor(private readonly subscriptionService: SubscriptionService) {}

    @HttpCode(HttpStatus.OK)
    @UseGuards(AuthGuard, ACGuard)
    @UseRoles({
        resource: ROLE_RESOURCES.SUBSCRIPTION,
        action: 'read',
        possession: 'any',
    })
    @Get('/')
    async getSubscription(@Req() req: Request) {
        //@ts-ignore
        const workspace = req.user.workspace;
        return {
            status: HttpStatus.OK,
            data: await this.subscriptionService.getCurrentSubscription(
                workspace,
            ),
            message: 'Subscription fetched successfully',
        };
    }

    @HttpCode(HttpStatus.OK)
    @UseGuards(AuthGuard, ACGuard)
    @UseRoles({
        resource: ROLE_RESOURCES.SUBSCRIPTION,
        action: 'delete',
        possession: 'own',
    })
    @Delete(':id')
    async cancelSubscription(@Param('id') id: string, @Req() req: Request) {
        //@ts-ignore
        const workspace = req.user.workspace;
        return {
            status: HttpStatus.OK,
            data: await this.subscriptionService.cancelSubscription(
                id,
                workspace,
            ),
            message: 'Subscription successfully cancelled',
        };
    }
}
