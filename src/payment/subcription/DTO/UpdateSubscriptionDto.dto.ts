import { PartialType } from '@nestjs/mapped-types';
import { CreateSubscriptionDtoDto } from './CreateSubscriptionDto.dto';
import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString } from 'class-validator';

export default class UpdateSubscriptionDtoDto extends PartialType(
    CreateSubscriptionDtoDto,
) {
    @ApiProperty()
    @IsOptional()
    @IsString()
    @IsNotEmpty()
    readonly cancel_at?: string;

    @ApiProperty()
    @IsString()
    @IsNotEmpty()
    readonly latest_invoice: string;
}
