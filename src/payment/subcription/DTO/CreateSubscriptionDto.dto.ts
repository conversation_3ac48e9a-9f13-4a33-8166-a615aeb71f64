import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsString, ValidateNested } from 'class-validator';
import { SubStatus } from '../subcription.schema';

export class CreateSubscriptionDtoDto {
    @ApiProperty()
    @IsString()
    @IsNotEmpty()
    readonly stripeId: string;

    @ApiProperty()
    @IsEnum(SubStatus)
    @IsNotEmpty()
    readonly status: string;

    @ApiProperty()
    @IsString()
    @IsNotEmpty()
    readonly created: string;

    @ApiProperty()
    @IsString()
    @IsNotEmpty()
    readonly latest_invoice: string;

    @ApiProperty()
    @IsString()
    @IsNotEmpty()
    readonly customer: string;

    @ApiProperty()
    @IsString()
    @IsNotEmpty()
    readonly start_date: string;

    @ApiProperty()
    @IsString()
    @IsNotEmpty()
    readonly trial_start: string;

    @ApiProperty()
    @IsString()
    @IsNotEmpty()
    readonly trial_end: string;

    @ApiProperty()
    @IsNotEmpty()
    @ValidateNested()
    readonly packages: {
        subId: string;
        package: string;
        quantity: number;
        typeOfPackage?: string;
    }[];
}
