import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { v4 as uuidv4 } from 'uuid';
import { Users } from '../../users/users.schema';
import { Medias } from '../../medias/medias.schema';
import { Packages } from '../../packages/packages.schema';
/**
 * @fileoverview This file defines the Subscription schema using Mongoose and NestJS.
 */

export enum SubStatus {
    INCOMPLETE = 'incomplete',
    INCOMPLETE_EXPIRED = 'incomplete_expired',
    TRIALING = 'trialing',
    ACTIVE = 'active',
    PAST_DUE = 'past_due',
    CANCELED = 'canceled',
    UNPAID = 'unpaid',
    PAUSED = 'paused',
}

/**
 * @class Subscription
 * @extends {Document}
 *
 * @description A class representing a subscription.
 */
@Schema()
export class Subscription extends Document {
    @Prop({ type: String, default: () => uuidv4() })
    id: string;

    @Prop({ required: true })
    stripeId: string;

    @Prop({ required: true })
    status: SubStatus;

    @Prop({ required: true, type: Date })
    created: Date;

    @Prop({ required: true })
    customer: string;

    @Prop({ required: false })
    latest_invoice: string;

    @Prop({ required: true, type: Date })
    start_date: Date | null;

    @Prop({ type: Date, default: null })
    trial_start: Date | null;

    @Prop({ type: Date, default: null })
    trial_end: Date | null;

    @Prop({ type: Date, default: null })
    cancel_at: Date | null;

    @Prop({
        type: [
            {
                subId: String,
                package: { type: Types.ObjectId, ref: Packages.name },
                quantity: Number,
                typeOfPackage: {
                    type: String,
                    enum: ['member', 'base'],
                    default: 'base',
                },
            },
        ],
    })
    packages: {
        subId: string;
        package: Packages;
        quantity: number;
        typeOfPackage?: 'member' | 'base';
    }[];
}

export const SubscriptionSchema = SchemaFactory.createForClass(Subscription);
