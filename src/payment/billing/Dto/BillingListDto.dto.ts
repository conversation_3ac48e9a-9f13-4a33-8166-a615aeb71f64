import { IsOptional, IsInt, Min, IsDateString, IsIn } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class BillingListDto {

    @ApiProperty()
    @IsOptional()
    status?: string;

    @ApiProperty()
    @IsOptional()
    @IsDateString()
    created?: Date;

    @ApiProperty()
    @IsOptional()
    @IsInt()
    @Min(1)
    page: number = 1;

    @ApiProperty()
    @IsOptional()
    @IsInt()
    @Min(1)
    limit: number = 10;

    @ApiProperty()
    @IsOptional()
    @IsIn(['amount_paid', 'amount_due', 'created'])
    sort: string = 'created';

    @ApiProperty()
    @IsOptional()
    @IsIn(['asc', 'desc'])
    order: string = 'asc';
}