import { IsNotEmpty, <PERSON><PERSON><PERSON>ber, IsString, IsUrl, IsDate, IsOptional } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateBillingDto {
    @ApiProperty({ description: 'The ID of the invoice in Stripe.' })
    @IsNotEmpty()
    @IsString()
    stripeId: string;

    @ApiProperty({ description: 'The ID of the customer in Stripe.' })
    @IsNotEmpty()
    @IsString()
    customer: string;

    @ApiProperty({ description: 'The amount due for this invoice.' })
    @IsNotEmpty()
    @IsNumber()
    amount_due: number;

    @ApiProperty({ description: 'The amount that has been paid.' })
    @IsNotEmpty()
    @IsNumber()
    amount_paid: number;

    @ApiProperty({ description: 'The status of the invoice (e.g., \'paid\', \'unpaid\').' })
    @IsNotEmpty()
    @IsString()
    status: string;

    @ApiProperty({ description: 'The URL of the invoice PDF.' })
    @IsOptional()
    @IsUrl()
    invoice_pdf: string;

    @ApiProperty({ description: 'The date when the invoice was created.' })
    @IsNotEmpty()
    @IsDate()
    created: string;
}