import { <PERSON>, Get, HttpCode, HttpStatus, Param, Req, UseGuards } from '@nestjs/common';
import ROLE_RESOURCES from '../../@core/constants/roles.resources';
import { BillingService } from './billing.service';
import { AuthGuard } from '../../auth/auth.guard';
import { ACGuard, UseRoles } from 'nest-access-control';
import { BillingListDto } from './Dto/BillingListDto.dto';

@Controller(ROLE_RESOURCES.BILLING)
export class BillingController {
    constructor(
        private readonly billingService: BillingService,
    ) {}

    @HttpCode(HttpStatus.OK)
    @UseGuards(AuthGuard, ACGuard)
    @UseRoles({
        resource: ROLE_RESOURCES.BILLING,
        action: 'read',
        possession: 'any',
    })
    @Get('/')
    async getBillings(
        @Req() req: Request,
        @Param() BillingListDto: BillingListDto
    ) {
        //@ts-ignore
        const workspaceId = req?.user.workspace;
        try {
            const data = await this.billingService.getBillings({
                ...BillingListDto,
                workspaceId
            });
            return {
                status: HttpStatus.OK,
                data: data,
                message: 'Billings fetched successfully'
            }
        }catch (e){
            return {
                status: HttpStatus.BAD_REQUEST,
                message: e.message
            }
        }
    }
}
