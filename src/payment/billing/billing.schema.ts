import { <PERSON><PERSON>, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';

@Schema()
export class Billing extends Document {
    _id: MongooseSchema.Types.ObjectId;

    @Prop({ required: true })
    stripeId: string;

    @Prop({ required: true })
    customer: string;

    @Prop({ required: true })
    amount_due: number;

    @Prop({ required: true })
    amount_paid: number;

    @Prop({ required: true })
    status: string;

    @Prop({ required: false })
    invoice_pdf: string;

    @Prop({ required: true })
    created: Date;
}

export const BillingSchema = SchemaFactory.createForClass(Billing);