import { forwardRef, Module } from '@nestjs/common';
import { BillingService } from './billing.service';
import { BillingController } from './billing.controller';
import { MongooseModule } from '@nestjs/mongoose';
import { Billing, BillingSchema } from './billing.schema';
import { WorkspaceModule } from '../../workspace/workspace.module';
import { Workspace, WorkspaceSchema } from '../../workspace/workspace.schema';
import { UsersModule } from '../../users/users.module';
import { ClientsModule } from '../../clients/clients.module';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: Billing.name, schema: BillingSchema }]),
    MongooseModule.forFeature([{ name: Workspace.name, schema: WorkspaceSchema }]),
    forwardRef(()=> UsersModule),
    forwardRef(() => ClientsModule)
  ],
  providers: [BillingService],
  controllers: [BillingController],
  exports: [BillingService],
})
export class BillingModule {}
