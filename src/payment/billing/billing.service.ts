import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Billing } from './billing.schema';
import { Model } from 'mongoose';
import { CreateBillingDto } from './Dto/CreateBillingDto.dto';
import { UpdateBillingDto } from './Dto/UpdateBillingDto.dto';
import { WorkspaceService } from '../../workspace/workspace.service';
import { Workspace } from '../../workspace/workspace.schema';

@Injectable()
export class BillingService {
    constructor(
       @InjectModel(Billing.name) private readonly billingModel: Model<Billing>,
       @InjectModel(Workspace.name) private readonly workspaceModel: Model<Workspace>,
    ) {}

    async createBilling(billing: CreateBillingDto): Promise<Billing> {
        const createdBilling = new this.billingModel(billing);
        return createdBilling.save();
    }



    async getBillings(
        {
            status,
            created,
            workspaceId,
            page= 1,
            limit = 10,
            sort = 'created',
            order= 'asc'
        }:{
            status?: string,
            created?: Date,
            workspaceId?: string,
            page: number,
            limit: number,
            sort: string,
            order: string
        }
    ): Promise<Billing[]> {
        let filter = {};


        const workspace = await this.workspaceModel.findById(workspaceId).exec();

        if (status) {
            filter['status'] = status;
        }
        if (created) {
            filter['created'] = created;
        }

        if (workspace) {
            filter['customer'] = workspace.stripeAccountId;
        }

        // Check if the provided sort parameter is a valid field
        const validSortFields = ['amount_paid', 'amount_due', 'created'];
        if (!validSortFields.includes(sort)) {
            sort = 'created';
        }

        return this.billingModel.find(filter)
            .sort({ [sort]: order === 'asc' ? 1 : -1 })
            .skip((page - 1) * limit)
            .limit(limit)
            .exec();
    }

    async updateBilling(billingId: string,billing: UpdateBillingDto): Promise<Billing> {
        return this.billingModel.findOneAndUpdate({
            stripeId: billingId,
        },{...billing},{new: true}).exec();
    }

    async getBillingById(billingId: string): Promise<Billing> {
        return this.billingModel.findById(billingId).exec();
    }

    async getBillingByStripeId(stripeId: string): Promise<Billing> {
        return this.billingModel.findOne({stripeId}).exec();
    }

}
