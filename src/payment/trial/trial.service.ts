import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import mongoose, { Model, Mongoose } from 'mongoose';
import { TrialStatus, TrialSubscription } from './trial.schema';
import { CreateTrialDto } from './DTO/CreateTrial.dto';
import { TRIAL_EXPIRED_DAYS } from '../../@core/constants/trial';
import { OnEvent } from '@nestjs/event-emitter';
import UpdateTrialDto from './DTO/UpdateTrial.dto';

@Injectable()
export class TrialService {

    constructor(
        @InjectModel(TrialSubscription.name) private TrialSubscription: Model<TrialSubscription>,
    ) {}


    async getTrialInformation(workspaceId: string): Promise<TrialSubscription> {
        const trial = await this.TrialSubscription.findOne({ workspace: workspaceId });
        if (trial) {
            const updatedTrial = trial.checkAndExpire();
            await trial.save();
            return updatedTrial;
        }
        throw new HttpException('Trial not found', HttpStatus.NOT_FOUND);
    }

    async createTrial(createTrial: CreateTrialDto & {workspace: string}): Promise<TrialSubscription> {
        const newTrial = new this.TrialSubscription({
            workspace: new mongoose.Types.ObjectId(createTrial.workspace),
            trialStartDate: new Date(),
            trialEndDate: new Date(Date.now() + TRIAL_EXPIRED_DAYS * 24 * 60 * 60 * 1000),
            status: TrialStatus.ACTIVE,
            convertedToSubscription: createTrial.convertedToSubscription,
            conversionDate: createTrial.conversionDate,
            subscriptionId: createTrial.subscriptionId,
            cancelReason: createTrial.cancelReason,
        });

        return await newTrial.save();
    }

    async convertTrialToSubscription(workspaceId: string, subscriptionId: string): Promise<TrialSubscription> {
        const trial = await this.TrialSubscription.findOne({ workspace: workspaceId });
        if (trial && trial.status !== TrialStatus.CONVERTED) {
            trial.updateStatus(TrialStatus.CONVERTED, subscriptionId);
            await trial.save();
            return trial;
        }
        throw new HttpException('Trial not found', HttpStatus.NOT_FOUND);
    }

    async revertTrialStatus(workspaceId: string, subscriptionId: string): Promise<TrialSubscription> {
        const trial = await this.TrialSubscription.findOne({ workspace: workspaceId });
        if (!trial) throw new HttpException('Trial not found', HttpStatus.NOT_FOUND);
        if (trial.status !== TrialStatus.CONVERTED) {
            throw new HttpException("No converted trial", HttpStatus.NOT_FOUND);
        }
        trial.updateStatus(TrialStatus.EXPIRED, subscriptionId);
        await trial.save();
        return trial;
    }
}