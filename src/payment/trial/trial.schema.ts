import { Prop, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';
import { Workspace } from '../../workspace/workspace.schema';

export enum TrialStatus {
    ACTIVE = 'active',
    EXPIRED = 'expired',
    CONVERTED = 'converted'
}

@Schema({ timestamps: true })
export class TrialSubscription extends Document {
    _id: MongooseSchema.Types.ObjectId;

    @Prop({ type: MongooseSchema.Types.ObjectId, ref: Workspace.name, required: true })
    workspace: Workspace;

    @Prop({ required: true })
    trialStartDate: Date;

    @Prop({ required: true })
    trialEndDate: Date;

    @Prop({ default: TrialStatus.ACTIVE })
    status: TrialStatus;

    @Prop({ type: Object })
    limitations: {
        maxUsers?: number;
        maxStorage?: number;
        features: string[];
    };

    @Prop({ type: Object })
    usage: {
        lastAccessDate: Date;
        loginCount: number;
        storageUsed: number;
    };

    @Prop()
    convertedToSubscription: boolean;

    @Prop()
    conversionDate: Date;

    @Prop()
    subscriptionId: string;

    @Prop()
    cancelReason: string;

    updateStatus(newStatus: TrialStatus, subscriptionId?: string) {
        this.status = newStatus;
        if (newStatus === TrialStatus.CONVERTED) {
            this.convertedToSubscription = true;
            this.conversionDate = new Date();
            this.subscriptionId = subscriptionId;
        } else {
            this.convertedToSubscription = false;
            this.conversionDate = null;
            this.subscriptionId = null;
        }

        return this;
    }

    checkAndExpire() {
        if (this.status === TrialStatus.ACTIVE && new Date() > this.trialEndDate) {
            this.updateStatus(TrialStatus.EXPIRED);
        }
        return this;
    }
}


export const TrialSubscriptionSchema = SchemaFactory.createForClass(TrialSubscription);
TrialSubscriptionSchema.methods.updateStatus = TrialSubscription.prototype.updateStatus;
TrialSubscriptionSchema.methods.checkAndExpire = TrialSubscription.prototype.checkAndExpire;