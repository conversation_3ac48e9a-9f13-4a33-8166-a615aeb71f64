import { Body, Controller, Get, HttpCode, HttpStatus, Post, Req, UseGuards } from '@nestjs/common';
import { TrialService } from './trial.service';
import ROLE_RESOURCES from '../../@core/constants/roles.resources';
import { AuthGuard, UserRequest } from '../../auth/auth.guard';
import { ACGuard, UseRoles } from 'nest-access-control';
import { CreateTrialDto } from './DTO/CreateTrial.dto';

@Controller(ROLE_RESOURCES.TRIAL)
export class TrialController {
    constructor(
        private readonly trialService: TrialService,
    ) {}

    @HttpCode(HttpStatus.OK)
    @UseGuards(AuthGuard, ACGuard)
    @UseRoles({
        resource: ROLE_RESOURCES.TRIAL,
        action: 'read',
        possession: 'any',
    })
    @Get('/')
    async checkTrialInformation(
        @Req() req: UserRequest,
    ){
        const workspaceId = req.user.workspace;

        const data = await this.trialService.getTrialInformation(workspaceId);
        return {
            statusCode: HttpStatus.OK,
            data: data,
            message: 'Trial information fetched successfully'
        }
    }

    @HttpCode(HttpStatus.CREATED)
    @UseGuards(AuthGuard, ACGuard)
    @UseRoles({
        resource: ROLE_RESOURCES.TRIAL,
        action: 'create',
        possession: 'any',
    })
    @Post('/')
    async createTrial(
        @Req() req: UserRequest,
        @Body() body: CreateTrialDto
    ) {
        const workspaceId = req.user.workspace;

        try {
            const result = await this.trialService.createTrial({
                ...body,
                workspace:workspaceId
            });
            return {
                status: HttpStatus.CREATED,
                data: result,
                message: 'Trial created successfully'
            };
        } catch (e) {
            return {
                status: HttpStatus.BAD_REQUEST,
                message: e.message
            };
        }
    }
}