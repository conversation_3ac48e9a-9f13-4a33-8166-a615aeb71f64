import { IsDate, <PERSON>NotEmpty, IsOptional, IsString, IsBoolean, IsObject, IsEnum } from 'class-validator';
import { Type } from 'class-transformer';
import { TrialStatus } from '../trial.schema';

export class CreateTrialDto {

    @IsOptional()
    @IsDate()
    @Type(() => Date)
    trialStartDate: Date;

    @IsOptional()
    @IsDate()
    @Type(() => Date)
    trialEndDate: Date;

    @IsOptional()
    @IsEnum(TrialStatus)
    status?: string;

    @IsOptional()
    @IsObject()
    limitations?: {
        maxUsers?: number;
        maxStorage?: number;
        features: string[];
    };

    @IsOptional()
    @IsObject()
    usage?: {
        lastAccessDate: Date;
        loginCount: number;
        storageUsed: number;
    };

    @IsOptional()
    @IsBoolean()
    convertedToSubscription?: boolean;

    @IsOptional()
    @IsDate()
    @Type(() => Date)
    conversionDate?: Date;

    @IsOptional()
    @IsString()
    subscriptionId?: string;

    @IsOptional()
    @IsString()
    cancelReason?: string;
}