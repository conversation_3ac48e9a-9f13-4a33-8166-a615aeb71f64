import { forwardRef, Module } from '@nestjs/common';
import { TrialService } from './trial.service';
import { TrialController } from './trial.controller';
import { MongooseModule } from '@nestjs/mongoose';
import {  TrialSubscription, TrialSubscriptionSchema } from './trial.schema';
import { ClientsModule } from '../../clients/clients.module';
import { UsersModule } from '../../users/users.module';

@Module({
  imports: [
      MongooseModule.forFeature([
        {
          name: TrialSubscription.name,
          schema: TrialSubscriptionSchema,
        }
      ]),
    forwardRef(()=> ClientsModule),
    forwardRef(()=> UsersModule),
  ],
  providers: [TrialService],
  controllers: [TrialController],
  exports: [TrialService],
})
export class TrialModule {}
