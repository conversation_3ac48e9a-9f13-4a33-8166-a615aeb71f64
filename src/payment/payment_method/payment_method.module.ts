import { forwardRef, Module } from '@nestjs/common';
import { PaymentMethodService } from './payment_method.service';
import { PaymentMethodController } from './payment_method.controller';
import { MongooseModule } from '@nestjs/mongoose';
import { PaymentMethod, PaymentMethodSchema } from './payment_method.schema';
import { WorkspaceModule } from '../../workspace/workspace.module';
import { UsersModule } from '../../users/users.module';
import { ClientsModule } from '../../clients/clients.module';


@Module({
  imports: [
    MongooseModule.forFeature([{ name: PaymentMethod.name, schema: PaymentMethodSchema }]),
    forwardRef(() => WorkspaceModule),
    forwardRef(()=> UsersModule),
    forwardRef(() => ClientsModule)
  ],
  providers: [PaymentMethodService],
  controllers: [PaymentMethodController],
  exports: [PaymentMethodService]
})
export class PaymentMethodModule {}
