import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { PaymentMethod } from './payment_method.schema';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import Stripe from 'stripe';
import { CreatePaymentMethodDto } from './DTO/CreatePaymentMethodDto.dto';
import { UpdatePaymentMethodDto } from './DTO/UpdatePaymentMethodDto.dto';
import { WorkspaceService } from 'src/workspace/workspace.service';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY)

@Injectable()
export class PaymentMethodService {
    constructor(
        @InjectModel(PaymentMethod.name) private paymentMethodModel: Model<PaymentMethod>,
        @Inject(forwardRef(() => WorkspaceService))
        private readonly workspaceService: WorkspaceService
    ) {}


    async listPaymentMethods(workspace: string): Promise<any> {
        const workspaceData = await this.workspaceService.findOne(workspace)
        if (!workspaceData) {
            throw new Error('Workspace not found');
        }

        return await this.paymentMethodModel.find({
            customer: workspaceData.stripeAccountId
        }).exec();
    }

    async createPaymentMethod(paymentMethod: CreatePaymentMethodDto): Promise<PaymentMethod> {
        const createdPaymentMethod = new this.paymentMethodModel(paymentMethod);
        return createdPaymentMethod.save();
    }

    async updatePaymentMethod(paymentMethodId: string, paymentMethod: UpdatePaymentMethodDto): Promise<PaymentMethod> {
        return this.paymentMethodModel.findOneAndUpdate({
            stripeId: paymentMethodId,
        }, { ...paymentMethod }, { new: true }).exec();
    }
}
