import { <PERSON><PERSON>, <PERSON>hem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

@Schema()
export class PaymentMethod extends Document {
    @Prop({ required: true })
    stripeId: string;

    @Prop({ required: true })
    customer: string;

    @Prop({ required: true })
    type: string;

    @Prop({ required: true })
    brand: string;

    @Prop({ required: true })
    last4: string;

    @Prop({ required: true })
    exp_month: number;

    @Prop({ required: true })
    exp_year: number;
}

export const PaymentMethodSchema = SchemaFactory.createForClass(PaymentMethod);