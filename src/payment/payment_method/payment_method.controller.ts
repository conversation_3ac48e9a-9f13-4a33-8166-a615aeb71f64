import { Controller, Get, HttpCode, HttpStatus, Req, UseGuards } from '@nestjs/common';
import { PaymentMethodService } from './payment_method.service';
import { AuthGuard } from '../../auth/auth.guard';
import { ACGuard, UseRoles } from 'nest-access-control';
import ROLE_RESOURCES from '../../@core/constants/roles.resources';

@Controller(ROLE_RESOURCES.PAYMENT_METHOD)
export class PaymentMethodController {
    constructor(
        private readonly paymentMethodService: PaymentMethodService,
    ) {}


    @HttpCode(HttpStatus.OK)
    @UseGuards(AuthGuard, ACGuard)
    @UseRoles({
        resource: ROLE_RESOURCES.PAYMENT_METHOD,
        action: 'read',
        possession: 'any',
    })
    @Get('/')
    async getPaymentMethods(
        @Req() req: Request,
    ) {
        //@ts-ignore
        const workspaceId = req.user.workspace;

        try {
            const data = await this.paymentMethodService.listPaymentMethods(workspaceId);
            return {
                status: HttpStatus.OK,
                data: data,
                message: 'Payment methods fetched successfully'
            }
        }catch (e){
            return {
                status: HttpStatus.BAD_REQUEST,
                message: e.message
            }
        }
    }
}
