import { IsNotEmpty, <PERSON><PERSON><PERSON><PERSON>, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreatePaymentMethodDto {
    @ApiProperty({ description: 'The ID of the payment method in Stripe.' })
    @IsNotEmpty()
    @IsString()
    stripeId: string;

    @ApiProperty({ description: 'The ID of the customer in Stripe.' })
    @IsNotEmpty()
    @IsString()
    customer: string;

    @ApiProperty({ description: 'The type of the payment method.' })
    @IsNotEmpty()
    @IsString()
    type: string;

    @ApiProperty({ description: 'The brand of the payment method.' })
    @IsNotEmpty()
    @IsString()
    brand: string;

    @ApiProperty({ description: 'The last four digits of the card.' })
    @IsNotEmpty()
    @IsString()
    last4: string;

    @ApiProperty({ description: 'The expiration month of the card.' })
    @IsNotEmpty()
    @IsNumber()
    exp_month: number;

    @ApiProperty({ description: 'The expiration year of the card.' })
    @IsNotEmpty()
    @IsNumber()
    exp_year: number;
}