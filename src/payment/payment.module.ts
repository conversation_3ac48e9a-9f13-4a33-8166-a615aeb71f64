import { forwardRef, Module } from '@nestjs/common';
import { PaymentController } from './payment.controller';
import { PaymentService } from './payment.service';
import { SubscriptionModule } from './subcription/subscription.module';
import { BillingModule } from './billing/billing.module';
import { PaymentMethodModule } from './payment_method/payment_method.module';
import { WorkspaceModule } from '../workspace/workspace.module';
import { MongooseModule } from '@nestjs/mongoose';
import { Workspace, WorkspaceSchema } from '../workspace/workspace.schema';
import { UsersModule } from '../users/users.module';
import { ClientsModule } from '../clients/clients.module';
import { UserPaymentModule } from './user_payment/user_payment.module';
import { TrialModule } from './trial/trial.module';
import { PackagesModule } from "../packages/packages.module";
import { UserPackagesModule } from 'src/user-packages/user-packages.module';

@Module({
  imports: [
      MongooseModule.forFeature([
          {
              name: Workspace.name,
              schema: WorkspaceSchema,
          },
      ]),
      forwardRef(()=> UsersModule),
      forwardRef(() => ClientsModule),
      forwardRef(() => SubscriptionModule),
      forwardRef(() => BillingModule),
      forwardRef(() => PaymentMethodModule),
      forwardRef(() => TrialModule),
      forwardRef(() => PackagesModule),
      UserPaymentModule,
      UserPackagesModule
  ],
  controllers: [PaymentController],
  providers: [PaymentService]
})
export class PaymentModule {}
