import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { StripeWebhookHandler } from '@golevelup/nestjs-stripe';
import { SubscriptionService } from './subcription/subscription.service';
import { BillingService } from './billing/billing.service';
import { PaymentMethodService } from './payment_method/payment_method.service';
import { InjectModel } from '@nestjs/mongoose';
import { Workspace } from '../workspace/workspace.schema';
import { Model } from 'mongoose';
import { PackagesService } from '../packages/packages.service';
import { UserPackagesService } from 'src/user-packages/user-packages.service';

@Injectable()
export class PaymentService {
    constructor(
        @Inject(forwardRef(() => SubscriptionService))
        private readonly subscriptionService: SubscriptionService,
        @Inject(forwardRef(() => BillingService))
        private readonly billingService: BillingService,
        @Inject(forwardRef(() => PaymentMethodService))
        private readonly paymentMethodService: PaymentMethodService,
        private readonly packagesService: PackagesService,
        private readonly userPackagesService: UserPackagesService,
        @InjectModel(Workspace.name) private workspaceModel: Model<Workspace>,
    ) {}

    @StripeWebhookHandler('customer.subscription.created')
    async handlePaymentSubscriptionCreated(evt: any) {
        try {
            const subscriptionItems = evt.data.object.items.data;
            const subscription = evt.data.object;
            const packages = await Promise.all(
                subscriptionItems.map(async (item) => {
                    const subId = item.id;
                    const quantity = item.quantity;
                    const packageId = item.price.product;
                    const packageIdList =
                        await this.packagesService.getPackageByProductId(
                            packageId,
                        );
                    const packageType =
                        packageIdList.baseProductStripeID === packageId
                            ? 'base'
                            : 'member';
                    return {
                        subId,
                        package: packageIdList._id,
                        quantity,
                        typeOfPackage: packageType,
                    };
                }),
            );
            await this.subscriptionService.createSubscription({
                stripeId: subscription.id,
                status: subscription.status,
                created: subscription.created
                    ? new Date(subscription.created * 1000).toISOString()
                    : null,
                latest_invoice: subscription.latest_invoice,
                customer: subscription.customer,
                start_date: subscription.start_date
                    ? new Date(subscription.start_date * 1000).toISOString()
                    : null,
                trial_start: subscription.trial_start
                    ? new Date(subscription.trial_start * 1000).toISOString()
                    : null,
                trial_end: subscription.trial_end
                    ? new Date(subscription.trial_end * 1000).toISOString()
                    : null,
                packages,
            });

            if (subscription.metadata && subscription.metadata.userId) {
                const userId = subscription.metadata.userId;
                
                for (const packageItem of packages) {
                    const packageData = await this.packagesService.findById(packageItem.package);
                    if (packageData) {
                        const billingCycle = subscription.items.data[0].plan.interval === 'year' ? 'yearly' : 'monthly';
                        
                        await this.userPackagesService.recordPackagePurchase(
                            userId,
                            packageItem.package.toString(),
                            packageItem.typeOfPackage,
                            billingCycle,
                            subscription.id
                        );
                    }
                }
            }
        } catch (e) {
            throw new Error(e);
        }
    }

    @StripeWebhookHandler('customer.subscription.updated')
    async handleUpdateSubscription(evt: any) {
        try {
            const subscriptionItems = evt.data.object.items.data;
            const subscription = evt.data.object;
            const packages = await Promise.all(
                subscriptionItems.map(async (item) => {
                    const subId = item.id;
                    const quantity = item.quantity;
                    const packageId = item.price.product;
                    const packageIdList =
                        await this.packagesService.getPackageByProductId(
                            packageId,
                        );
                    console.log('packageIdList', packageIdList);
                    const packageType =
                        packageIdList.baseProductStripeID === packageId
                            ? 'base'
                            : 'member';
                    return {
                        subId,
                        package: packageIdList._id,
                        quantity,
                        typeOfPackage: packageType,
                    };
                }),
            );
            const existSubscription =
                await this.subscriptionService.findByStripeSubId(
                    subscription.id,
                );
            if (!existSubscription) {
                throw new Error('Subscription not found');
            }
            await this.subscriptionService.updateSubscription(
                existSubscription._id,
                {
                    packages,
                    latest_invoice: subscription.latest_invoice,
                },
            );
        } catch (e) {
            throw new Error(e);
        }
    }

    @StripeWebhookHandler('checkout.session.completed')
    async handleCheckoutSessionCompleted(evt: any) {
        try {
            const session = evt.data.object;
            const workspace = session.metadata.workspace;
            if (workspace) {
                await this.workspaceModel.findByIdAndUpdate(
                    workspace,
                    {
                        stripeAccountId: session.customer,
                        isFirstLogin: false,
                    },
                    { new: true },
                );
            }

            if (session.metadata && session.metadata.type === 'package_purchase') {
                const userId = session.metadata.userId;
                const packageId = session.metadata.packageId;
                const packageType = session.metadata.packageType;
                const billingCycle = session.metadata.billingCycle;
                
                if (userId && packageId) {
                    await this.userPackagesService.recordPackagePurchase(
                        userId,
                        packageId,
                        packageType,
                        billingCycle,
                        session.id
                    );
                }
            }
        } catch (e) {
            throw new Error(e);
        }
    }

    @StripeWebhookHandler('checkout.session.async_payment_failed')
    async handleCheckoutSessionAsyncPaymentFailed(evt: any) {
        try {
            const session = evt.data.object;
            const workspace = session.metadata.workspace;
            if (workspace) {
                await this.workspaceModel.findByIdAndUpdate(
                    workspace,
                    {
                        isFirstLogin: false,
                    },
                    { new: true },
                );
            }
        } catch (e) {
            throw new Error(e);
        }
    }

    @StripeWebhookHandler('invoice.created')
    async handlePaymentIntentCreated(evt: any) {
        await this.billingService
            .createBilling({
                stripeId: evt.data.object.id,
                status: evt.data.object.status,
                created: evt.data.object.created
                    ? new Date(evt.data.object.created * 1000).toISOString()
                    : null,
                customer: evt.data.object.customer,
                amount_due: evt.data.object.amount_due,
                invoice_pdf: evt.data.object.invoice_pdf,
                amount_paid: evt.data.object.amount_paid,
            })
            .then(() => false);
    }

    @StripeWebhookHandler('invoice.payment_succeeded')
    async handlePaymentIntentSucceeded(evt: any) {
        const billing = await this.billingService.getBillingByStripeId(
            evt.data.object.id,
        );
        if (billing) {
            await this.billingService
                .updateBilling(billing.stripeId, {
                    status: 'succeeded',
                })
                .then(() => false);
        }
    }

    @StripeWebhookHandler('invoice.payment_failed')
    async handlePaymentIntentFailed(evt: any) {
        const billing = await this.billingService.getBillingByStripeId(
            evt.data.object.id,
        );
        if (billing) {
            await this.billingService
                .updateBilling(billing.stripeId, {
                    status: 'failed',
                })
                .then(() => false);
        }
    }

    @StripeWebhookHandler('invoice.payment_action_required')
    async handlePaymentIntentActionRequired(evt: any) {
        const billing = await this.billingService.getBillingByStripeId(
            evt.data.object.id,
        );
        if (billing) {
            const status =
                evt.data.object.period_end < new Date().getTime() / 1000
                    ? 'overdue'
                    : 'failed';

            await this.billingService
                .updateBilling(billing.stripeId, {
                    status: status,
                })
                .then(() => false);
        }
    }

    @StripeWebhookHandler('payment_method.attached')
    async handlePaymentMethodCreated(evt: any) {
        try {
            await this.paymentMethodService
                .createPaymentMethod({
                    brand: evt.data.object.card.brand,
                    customer: evt.data.object.customer,
                    exp_month: evt.data.object.card.exp_month,
                    exp_year: evt.data.object.card.exp_year,
                    last4: evt.data.object.card.last4,
                    stripeId: evt.data.object.id,
                    type: evt.data.object.type,
                })
                .then(() => false);
        } catch (e) {
            console.log(e);
        }
    }

    @StripeWebhookHandler('payment_method.updated')
    async handlePaymentMethodUpdated(evt: any) {
        await this.paymentMethodService
            .updatePaymentMethod(evt.data.object.id, {
                brand: evt.data.object.card.brand,
                exp_month: evt.data.object.card.exp_month,
                exp_year: evt.data.object.card.exp_year,
                last4: evt.data.object.card.last4,
                type: evt.data.object.type,
            })
            .then(() => false);
    }
}
