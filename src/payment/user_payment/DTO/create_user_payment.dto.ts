import { IsNotEmpty, IsOptional, IsString, IsNumber, IsEnum, IsUUID, IsDate } from 'class-validator';
import { Schema } from 'mongoose';
import { PaymentMethod, PaymentStatus } from "../schema/user_payment.schema";

export class CreateUserPaymentDTO {
  @IsNotEmpty()
  bookingRequest: Schema.Types.ObjectId;

  @IsNotEmpty()
  client: Schema.Types.ObjectId;

  @IsEnum(PaymentMethod)
  @IsNotEmpty()
  paymentMethod: PaymentMethod;

  @IsOptional()
  @IsString()
  stripePaymentId?: string;

  @IsOptional()
  @IsString()
  cardLastFour?: string;

  @IsOptional()
  @IsString()
  cardBrand?: string;

  @IsNumber()
  @IsNotEmpty()
  amount: number;

  @IsString()
  @IsOptional()
  currency?: string;

  @IsEnum(PaymentStatus)
  @IsNotEmpty()
  paymentStatus: PaymentStatus;

  @IsOptional()
  @IsDate()
  paymentDate?: Date;

  @IsOptional()
  @IsString()
  stripeChargeId?: string;

  @IsOptional()
  @IsString()
  receiptUrl?: string;

  @IsOptional()
  @IsString()
  refundId?: string;
}