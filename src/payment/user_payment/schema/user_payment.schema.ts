import { Schem<PERSON>, <PERSON><PERSON>, SchemaFactory } from "@nestjs/mongoose";
import { Document, Schema as MongooseSchema } from "mongoose";
import { BookingRequest } from "../../../booking-request/booking-request.schema";
import { ClientData } from "../../../clients-data/client-data.schema";

export enum PaymentStatus {
  Pending = "Pending",
  Succeeded = "Succeeded",
  Failed = "Failed",
  Refunded = "Refunded",
  Cancelled = "Cancelled",
}

export enum PaymentMethod {
  Cash = "Cash",
  Card = "Card"
}

@Schema({ timestamps: { createdAt: "createdAt", updatedAt: "updatedAt" } })
export class UserPayment extends Document {
  @Prop({ type:  MongooseSchema.Types.ObjectId, ref: "BookingRequest" })
  bookingRequest: BookingRequest;

  @Prop({ type:  MongooseSchema.Types.ObjectId, ref: "ClientData" })
  client: ClientData;

  @Prop({ enum: PaymentMethod, required: true })
  paymentMethod: PaymentMethod;

  @Prop({ type: String, required: false })
  stripePaymentId?: string;

  @Prop({ type: String, required: false })
  cardLastFour?: string;

  @Prop({ type: String, required: false })
  cardBrand?: string;

  @Prop({ type: Number, required: true })
  amount: number;

  @Prop({ type: String, required: false })
  currency?: string;

  @Prop({ enum: PaymentStatus, required: true })
  paymentStatus: PaymentStatus;

  @Prop({ type: Date, required: false })
  paymentDate?: Date;

  @Prop({ type: String })
  stripeChargeId?: string;

  @Prop({ type: String })
  receiptUrl?: string;

  @Prop({ type: String })
  refundId?: string;

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'TipOption' })
  tipOption?: MongooseSchema.Types.ObjectId;

  @Prop({ default: 0 })
  tipAmount: number;

  @Prop({ type: Date, default: Date.now })
  createdAt: Date;

  @Prop({ type: Date, default: Date.now })
  updatedAt: Date;
}

export const UserPaymentSchema = SchemaFactory.createForClass(UserPayment);