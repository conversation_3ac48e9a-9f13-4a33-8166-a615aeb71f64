import { Module } from "@nestjs/common";
import { UserPaymentService } from "./user_payment.service";
import { MongooseModule } from "@nestjs/mongoose";
import { UserPayment, UserPaymentSchema } from "./schema/user_payment.schema";

@Module({
  imports: [MongooseModule.forFeature([{
    name: UserPayment.name,
    schema: UserPaymentSchema
  }])],
  providers: [UserPaymentService],
  exports: [UserPaymentService]
})
export class UserPaymentModule {
}
