import { Injectable } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import mongoose, { Model } from "mongoose";
import { UserPayment } from "./schema/user_payment.schema";
import { CreateUserPaymentDTO } from "./DTO/create_user_payment.dto";

@Injectable()
export class UserPaymentService {
  constructor(@InjectModel(UserPayment.name)
              private userPaymentModel: Model<UserPayment>) {
  }

  async create(userPayment: CreateUserPaymentDTO) {
    return this.userPaymentModel.create(userPayment);
  }

  findAll() {
    return `This action returns all userPayment`;
  }

  async findByBookingId(bookingId: string) {
    return this.userPaymentModel.findOne({
      bookingRequest: new mongoose.Types.ObjectId(bookingId)
    });
  }

  async findByUserId(userId: string) {
    return this.userPaymentModel.findOne({
      user: new mongoose.Types.ObjectId(userId)
    });
  }

  async update(id: string, userPayment: Partial<CreateUserPaymentDTO>) {
    return this.userPaymentModel.findByIdAndUpdate(id, userPayment, { new: true });
  }
}
