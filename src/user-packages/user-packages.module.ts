import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { UserPackagesService } from './user-packages.service';
import { UserPackage, UserPackageSchema } from './schemas/user-packages.schema';
import { Packages, PackagesSchema } from '../packages/packages.schema';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: UserPackage.name, schema: UserPackageSchema },
      { name: Packages.name, schema: PackagesSchema },
    ]),
  ],
  providers: [UserPackagesService],
  exports: [UserPackagesService],
})
export class UserPackagesModule {}