import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { UserPackage } from './schemas/user-packages.schema';
import { Packages } from '../packages/packages.schema';

@Injectable()
export class UserPackagesService {
  constructor(
    @InjectModel(UserPackage.name) private userPackageModel: Model<UserPackage>,
    @InjectModel(Packages.name) private packageModel: Model<Packages>,
  ) {}

  async recordPackagePurchase(
    userId: string, 
    packageId: string, 
    packageType: string,
    billingCycle: string,
    transactionId?: string
  ): Promise<UserPackage> {
    const packageData = await this.packageModel.findById(packageId);
    if (!packageData) {
      throw new Error('Package not found');
    }

    const startDate = new Date();
    const endDate = new Date();
    
    if (billingCycle === 'monthly') {
      endDate.setMonth(endDate.getMonth() + 1);
    } else if (billingCycle === 'yearly') {
      endDate.setFullYear(endDate.getFullYear() + 1);
    }

    const userPackage = new this.userPackageModel({
      userId: new Types.ObjectId(userId),
      packageId: new Types.ObjectId(packageId),
      packageType,
      billingCycle,
      packageSnapshot: {
        name: packageData.name,
        price: packageData.basePrice,
        features: packageData.features,
      },
      startDate,
      endDate,
      status: 'active',
      transactionId: transactionId ? new Types.ObjectId(transactionId) : undefined,
    });

    return userPackage.save();
  }

  async getUserActivePackage(userId: string): Promise<UserPackage> {
    return this.userPackageModel.findOne({
      userId: new Types.ObjectId(userId),
      status: 'active',
      endDate: { $gte: new Date() }
    });
  }

  async checkUserHasFeature(userId: string, featureName: string): Promise<boolean> {
    const activePackage = await this.getUserActivePackage(userId);
    
    if (!activePackage) {
      return false;
    }
    
    return activePackage.packageSnapshot.features.includes(featureName);
  }
}