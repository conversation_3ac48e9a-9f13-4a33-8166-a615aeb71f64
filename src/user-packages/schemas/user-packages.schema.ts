import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

@Schema({ timestamps: true })
export class UserPackage extends Document {
  @Prop({ type: Types.ObjectId, ref: 'User', required: true })
  userId: Types.ObjectId;

  @Prop({ type: Types.ObjectId, ref: 'Package', required: true })
  packageId: Types.ObjectId;

  @Prop({ required: true, enum: ['starter', 'growing', 'premium'] })
  packageType: string;

  @Prop({ required: true, enum: ['monthly', 'yearly'] })
  billingCycle: string;

  @Prop({ type: Object, required: true })
  packageSnapshot: {
    name: string;
    price: number;
    features: string[];
  };

  @Prop({ required: true })
  startDate: Date;

  @Prop({ required: true })
  endDate: Date;

  @Prop({ default: 'active', enum: ['active', 'expired', 'cancelled', 'trial'] })
  status: string;

  @Prop({ type: Types.ObjectId, ref: 'Transaction' })
  transactionId: Types.ObjectId;
}

export const UserPackageSchema = SchemaFactory.createForClass(UserPackage);