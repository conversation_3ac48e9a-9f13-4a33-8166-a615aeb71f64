import { Injectable } from "@nestjs/common";
import createTemplate, { InvoicePDFParams } from "./templates/invoice_pdf";
import createCleanerReportTemplate,{ CleanerReportPDFParams } from "./templates/cleaner_report";

@Injectable()
export class GeneratePdfService {
    constructor() {}

    async generatePdf(data:InvoicePDFParams):Promise<Buffer>  {
        const pdf = await createTemplate(data);
        return await this.streamToBuffer(pdf);
    }

    async generateCleanerReportPdf(data:CleanerReportPDFParams):Promise<Buffer>  {
        const pdf = await createCleanerReportTemplate(data);
        return await this.streamToBuffer(pdf);
    }

    private async streamToBuffer(readableStream: NodeJS.ReadableStream): Promise<Buffer> {
        const chunks: any[] = [];
        for await (const chunk of readableStream) {
            chunks.push(chunk);
        }
        return Buffer.concat(chunks);
    }
}