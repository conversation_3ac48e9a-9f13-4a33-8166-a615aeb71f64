import React from 'react';
import ReactPDF, {Page, Text, View, Document, StyleSheet, Image} from '@react-pdf/renderer';

// Create styles
const styles = StyleSheet.create({
  page: {
    paddingTop: 35,
    paddingBottom: 65,
    paddingHorizontal: 35,
  },
  section: {
    paddingTop: 10,
    flexGrow: 1,
    wrap: true,
  },
  image: {
    width: "150px"
  },
  title: {
    margin: "25px 0",
    marginBottom: "20px",
    fontSize: 25,
    fontWeight: 'bold',
  },
  globalFlex: {
    flexDirection: 'row',
    wrap: true,
  },
  text: {
    marginRight: "2px",
    marginBottom: "5px",
    fontSize: "12px",
    letterSpacing: "1px",
  },
  value: {
    marginLeft: "2px",
    fontSize: "12px",
    wrap: true,
    width: "70%",
    color: "#9e5408"
  },
  line: {
    width: '100%',
    height: 0.5,
    backgroundColor: 'gray',
    marginTop: "20px",
    marginBottom: "10px"
  },
  billContainer: {
    flexDirection: 'row',
    margin: "10px 0",
    wrap: true,
    minHeight: "50px"
  },
  billContent: {
    flexDirection: 'row',
    width: "50%"
  },

  description: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    margin: "7px 0",
    padding: "0 5px"
  },
  contentDesOne: {
    minHeight: "30px",
    maxWidth: "100%",
    width: "100%",
    backgroundColor: "#edf2f4",
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: "0 10px",
    alignItems: 'center',
    wrap: true,
  },
  contentDesTwo: {
    minHeight: "30px",
    width: "100%",
    maxWidth: "100%",
    backgroundColor: "#ffff",
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: "0 10px",
    alignItems: 'center',
  },
  textDes: {
    fontSize: "11px",
    padding: "5px",
    wrap: true,
  },
  containerAmount: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    margin: "10px 0"
  },
  contentAmount: {
    flexDirection: 'row',
    marginTop: "10px",
    marginRight: "10px"
  },
  textAmount: {
    fontSize: "14px",
  },
  price:{
    fontSize: "14px",
    marginLeft: "5px",
    color: "#c1121f"
  }
});


type itemType = {
  content: string;
  amount: number | string;
}

export type quotePDFParams = {
  quote_items: itemType[];
  quote_number: any;
  quote_date: string;
  quote_bill_to: string;
  quote_payable_to: string;
  quote_price: number;
}

// Create Document Component
const QuotePDF = ({quote_items, quote_number,quote_date,quote_bill_to,quote_payable_to,quote_price }:quotePDFParams) => (
  <Document>
    <Page size="A4" style={styles.page}>
      <View style={styles.section}>
        <Image
          style={styles.image}
          src="data:image/png;base64,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"
        />
        <Text style={styles.title}>QUOTES #{quote_number}</Text>
        <View style={styles.globalFlex}>
          <Text style={styles.text}>ISSUED:</Text> <Text style={styles.value}>{quote_date}</Text>
        </View>
        <View style={styles.line} />
        <View style={styles.billContainer}>
          <View style={styles.billContent}>
            <Text style={styles.text}>SEND TO:</Text> <Text style={styles.value}>{quote_bill_to}</Text>
          </View>
          <View style={styles.billContent}>
            <Text style={styles.text}>SEND FORM:</Text> <Text style={styles.value}>{quote_payable_to}</Text>
          </View>
        </View>
        <View style={styles.line} />
        <View style={styles.description} >
          <Text style={styles.text}>DESCRIPTION:</Text>
          <Text style={styles.text}>TOTAL</Text>
        </View>
        {
          quote_items?.map((item, index) => {
            return (
                <View key={index} style={index%2 === 0 ?styles.contentDesOne : styles.contentDesTwo}>
                  <Text style={styles.textDes}>{item.content}</Text>
                  <Text style={styles.textDes}>{item.amount}</Text>
                </View>
            )
          })
        }
        <View  style={styles.containerAmount}>
          <Text/>
          <View style={styles.contentAmount}>
            <Text style={styles.textAmount}>TOTAL AMOUNT:</Text> <Text style={styles.price}> ${quote_price}</Text>
          </View>
        </View>
      </View>
    </Page>
  </Document>
);

export default async (data:quotePDFParams) => {
  return await ReactPDF.renderToStream(<QuotePDF {...data}  />);
};
