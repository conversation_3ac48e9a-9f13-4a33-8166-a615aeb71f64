import React, { Fragment } from "react";
import ReactPDF, { Page, Text, View, Document, StyleSheet, Image } from "@react-pdf/renderer";


// Create styles
const styles = StyleSheet.create({
  page: {
    paddingTop: 35,
    paddingBottom: 65,
    paddingHorizontal: 35
  },
  section: {
    paddingTop: 10,
    flexGrow: 1,
    wrap: true
  },
  image: {
    width: "150px"
  },

  imageCleaner: {
    width: "100%",
    height: "100%",
    objectFit: "cover",
    borderRadius: "5px"
  },

  containerImg: {
    width: "100%",
    height: "150px"
  },

  title: {
    margin: "25px 0",
    marginBottom: "20px",
    fontSize: 22,
    fontWeight: "bold",
    textTransform: "uppercase"
  },

  name: {
    color: "#9e5408",
    fontSize: 25
  },

  globalFlex: {
    flexDirection: "row",
    wrap: true
  },
  text: {
    marginRight: "2px",
    marginBottom: "5px",
    fontSize: "12px",
    letterSpacing: "1px"
  },
  value: {
    marginLeft: "2px",
    fontSize: "13px",
    wrap: true,
    width: "70%",
    color: "#9e5408"
  },
  line: {
    width: "100%",
    height: 0.5,
    backgroundColor: "gray",
    marginTop: "20px",
    marginBottom: "20px"
  },

  lineColumn: {
    width: "1px",
    height: "100%",
    backgroundColor: "gray"
  },

  containerCleaner: {
    flexDirection: "column",
    gap: "50px"
  },

  containerItem: {
    width: "100%"
  },

  contentShowImage: {
    marginTop: "10px",
    flexDirection: "row",
    gap: "13px"
  },

  containerText: {
    flexDirection: "row"

  },

  containerImage: {
    width: "50%",
    flexDirection: "column",
    gap: "15px"
  },

  textStyle: {
    marginRight: "2px",
    marginBottom: "5px",
    fontSize: "12px",
    letterSpacing: "1px",
    textAlign: "center"
  }

});

type StaffMedia = {
  staffName: string;
  before: string[];
  after: string[];
}

export type CleanerReportPDFParams = {
  logo: string;
  clientName: string;
  street_one: string;
  city: string;
  state: string;
  dateServiced: string;
  staffMedias?: StaffMedia[];
};


// Create Document Component
export const CleanerReportPDF = ({ logo, city, clientName, dateServiced, staffMedias, state, street_one }: CleanerReportPDFParams) => (
  <Document title={`${clientName} - ${dateServiced} - ${street_one}, ${city}, ${state}`}>
    <Page size="A4" style={styles.page}>
      <View style={styles.section}>
        <Image
          style={styles.image}
          src={logo}
        />
        <Text style={styles.title}>
          <Text>Client&#39;s name:</Text> <Text style={styles.name}>{clientName}</Text>
        </Text>
        <View style={styles.globalFlex}>
          <Text style={styles.text}>Address:</Text> <Text style={styles.value}>{street_one}, {city}, {state}</Text>
        </View>
        <View style={styles.globalFlex}>
          <Text style={styles.text}>Completion Date: </Text> <Text style={styles.value}>{dateServiced}</Text>
        </View>
        {
          staffMedias?.map(({staffName, before, after}) => (
            <Fragment key={staffName}>
              <View style={styles.line} />
              {/*Show Image Before - After*/}
              <View style={styles.containerCleaner}>
                <View style={styles.containerItem}>
                  <View style={styles.containerText}>
                    <Text style={styles.text}>CLEANER:</Text>
                    <Text style={styles.value}>{staffName}</Text>
                  </View>
                  <View style={styles.contentShowImage}>
                    <View style={styles.containerImage}>
                      <Text style={styles.textStyle}>Before</Text>
                      {
                        before.map((url, index) => (
                          <View key={index} style={styles.containerImg}>
                            <Image
                              style={styles.imageCleaner}
                              src={url}
                            />
                          </View>
                        ))
                      }
                    </View>
                    <View style={styles.lineColumn} />
                    <View style={styles.containerImage}>
                      <Text style={styles.textStyle}>After</Text>
                      {
                        after.map((url, index) => (
                          <View key={index} style={styles.containerImg}>
                            <Image
                              style={styles.imageCleaner}
                              src={url}
                            />
                          </View>
                        ))
                      }
                    </View>
                  </View>
                </View>
              </View>
            </Fragment>
          ))
        }
      </View>
    </Page>
  </Document>
);
export default async (data: CleanerReportPDFParams) => {
  return await ReactPDF.renderToStream(<CleanerReportPDF {...data} />);
};
