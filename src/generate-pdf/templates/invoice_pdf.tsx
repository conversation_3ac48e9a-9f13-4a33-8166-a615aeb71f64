import React, { Fragment } from "react";
import ReactPDF, {
    Page,
    Text,
    View,
    Document,
    StyleSheet,
    Image,
} from '@react-pdf/renderer';

// Create styles
const styles = StyleSheet.create({
    page: {
        paddingTop: 35,
        paddingBottom: 65,
        paddingHorizontal: 35,
    },
    section: {
        paddingTop: 10,
        flexGrow: 1,
        wrap: true,
    },
    image: {
        width: '150px',
    },

    imageCleaner: {
        width: '100%',
        height: '100%',
        objectFit: 'cover',
        borderRadius: '5px',
    },

    containerImg: {
        width: '100%',
        height: '150px',
    },

    title: {
        margin: '25px 0',
        marginBottom: '20px',
        fontSize: 25,
        fontWeight: 'bold',
    },
    globalFlex: {
        flexDirection: 'row',
        wrap: true,
    },
    text: {
        marginRight: '2px',
        marginBottom: '5px',
        fontSize: '12px',
        letterSpacing: '1px',
    },
    value: {
        marginLeft: '2px',
        fontSize: '12px',
        wrap: true,
        width: '70%',
        color: '#9e5408',
    },
    line: {
        width: '100%',
        height: 0.5,
        backgroundColor: 'gray',
        marginTop: '20px',
        marginBottom: '20px',
    },

    lineColumn: {
        width: '1px',
        height: '100%',
        backgroundColor: 'gray',
    },

    billContainer: {
        flexDirection: 'row',
        margin: '10px 0',
        wrap: true,
        minHeight: '50px',
    },
    billContent: {
        flexDirection: 'row',
        width: '50%',
    },

    description: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        margin: '7px 0',
        padding: '0 5px',
    },
    contentDesOne: {
        minHeight: '30px',
        maxWidth: '100%',
        width: '100%',
        backgroundColor: '#edf2f4',
        flexDirection: 'row',
        justifyContent: 'space-between',
        padding: '0 10px',
        alignItems: 'center',
        wrap: true,
    },
    contentDesTwo: {
        minHeight: '30px',
        width: '100%',
        maxWidth: '100%',
        backgroundColor: '#ffff',
        flexDirection: 'row',
        justifyContent: 'space-between',
        padding: '0 10px',
        alignItems: 'center',
    },
    textDes: {
        fontSize: '11px',
        padding: '5px',
        wrap: true,
    },
    containerAmount: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        margin: '10px 0',
    },
    contentAmount: {
        flexDirection: 'row',
        marginTop: '10px',
        marginRight: '10px',
    },
    textAmount: {
        fontSize: '14px',
    },
    price: {
        fontSize: '14px',
        marginLeft: '5px',
        color: '#c1121f',
    },

    containerCleaner: {
        flexDirection: 'column',
        gap: '20px',
    },

    containerItem: {
        width: '100%',
    },

    contentShowImage: {
        marginTop: '10px',
        flexDirection: 'row',
        gap: '13px',
    },

    containerText: {
        flexDirection: 'row',
    },

    containerImage: {
        width: '50%',
        flexDirection: 'column',
        gap: '15px',
    },

    textStyle: {
        marginRight: '2px',
        marginBottom: '5px',
        fontSize: '12px',
        letterSpacing: '1px',
        textAlign: 'center',
    },
});

type itemType = {
    content: string;
    amount: number | string;
};

type StaffMedia = {
  staffName: string;
  before: string[] | Buffer[];
  after: string[] | Buffer[];
}

export type InvoicePDFParams = {
    logo: string;
    invoice_items: itemType[];
    invoice_number: any;
    invoice_date: string;
    invoice_due_date: string;
    invoice_bill_to: string;
    invoice_payable_to: string;
    invoice_price: number;
    street_one: string;
    city: string;
    state: string;
    zip: number;
    invoice_payable_address: string;
    dateServiced: string;
    staffMedias?: StaffMedia[];
};

const ParsedText = ({ html }) => {
    const lines = html.split('<br />');
    return (
        <>
            {lines.map((line, i) => (
                <Text key={i} style={styles.value}>
                    {line.trim()}
                </Text>
            ))}
        </>
    );
};

// Create Document Component
const InvoicePDF = ({
    logo,
    dateServiced,
    invoice_payable_address,
    invoice_items,
    invoice_number,
    invoice_date,
    invoice_due_date,
    invoice_bill_to,
    invoice_payable_to,
    invoice_price,
    street_one,
    city,
    state,
    zip,
    staffMedias,
}: InvoicePDFParams) => (
    <Document>
        <Page size="A4" style={styles.page}>
            <View style={styles.section}>
                <Image style={styles.image} src={logo} />
                <Text style={styles.title}>INVOICE #{invoice_number}</Text>
                <View style={styles.globalFlex}>
                    <Text style={styles.text}>SERVED DATE:</Text>{' '}
                    <Text style={styles.value}>{dateServiced}</Text>
                </View>
                <View style={styles.globalFlex}>
                    <Text style={styles.text}>ISSUED:</Text>{' '}
                    <Text style={styles.value}>{invoice_date}</Text>
                </View>
                <View style={styles.globalFlex}>
                    <Text style={styles.text}>DUE:</Text>{' '}
                    <Text style={styles.value}>{invoice_due_date}</Text>
                </View>
                <View style={styles.line} />
                <View style={styles.billContainer}>
                    <View style={styles.billContent}>
                        <Text style={styles.text}>BILL TO:</Text>
                        <Text style={styles.value}>
                            {invoice_bill_to +
                                '\n' +
                                street_one +
                                '\n' +
                                city +
                                state +
                                zip}
                        </Text>
                    </View>
                    <View style={styles.billContent}>
                        <Text style={styles.text}>PAYABLE TO:</Text>
                        <Text style={styles.value}>
                            {invoice_payable_to +
                                '\n' +
                                invoice_payable_address}
                        </Text>
                    </View>
                </View>
                {
                    staffMedias?.map(({staffName, before, after}) => (
                      <Fragment key={staffName}>
                          <View style={styles.line} />
                          {/*Show Image Before - After*/}
                          <View style={styles.containerCleaner}>
                              <View style={styles.containerItem}>
                                  <View style={styles.containerText}>
                                      <Text style={styles.text}>CLEANER:</Text>
                                      <Text style={styles.value}>{staffName}</Text>
                                  </View>
                                  <View style={styles.contentShowImage}>
                                      <View style={styles.containerImage}>
                                          <Text style={styles.textStyle}>Before</Text>
                                          {
                                              before.map((url, index) => (
                                                  <View key={index} style={styles.containerImg}>
                                                      <Image
                                                        style={styles.imageCleaner}
                                                        src={url}
                                                      />
                                                  </View>
                                              ))
                                          }
                                      </View>
                                      <View style={styles.lineColumn} />
                                      <View style={styles.containerImage}>
                                          <Text style={styles.textStyle}>After</Text>
                                          {
                                              after.map((url, index) => (
                                                  <View key={index} style={styles.containerImg}>
                                                      <Image
                                                        style={styles.imageCleaner}
                                                        src={url}
                                                      />
                                                  </View>
                                              ))
                                          }
                                      </View>
                                  </View>
                              </View>
                          </View>
                      </Fragment>
                    ))
                }
                <View style={styles.line} />
                <View style={styles.description}>
                    <Text style={styles.text}>DESCRIPTION:</Text>
                    <Text style={styles.text}>TOTAL</Text>
                </View>
                {invoice_items?.map((item, index) => {
                    return (
                        <View
                            key={index}
                            style={
                                index % 2 === 0
                                    ? styles.contentDesOne
                                    : styles.contentDesTwo
                            }
                        >
                            <Text style={styles.textDes}>{item.content}</Text>
                            <Text style={styles.textDes}>{item.amount}</Text>
                        </View>
                    );
                })}
                <View style={styles.containerAmount}>
                    <Text />
                    <View style={styles.contentAmount}>
                        <Text style={styles.textAmount}>AMOUNT DUE :</Text>{' '}
                        <Text style={styles.price}> ${invoice_price}</Text>
                    </View>
                </View>
            </View>
        </Page>
    </Document>
);

export default async (data: InvoicePDFParams) => {
    return await ReactPDF.renderToStream(<InvoicePDF {...data} />);
};
