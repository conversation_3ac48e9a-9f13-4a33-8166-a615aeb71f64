import { Module, forwardRef } from '@nestjs/common';
import { CommonController } from './common.controller';
import { CommonService } from './common.service';
import { BookingFrequencyModule } from '../booking-frequency/booking-frequency.module';
import { BookingTypesModule } from '../booking-types/booking-types.module';
import { UsersModule } from '../users/users.module';
import { ClientsModule } from '../clients/clients.module';
import { AddressModule } from '../address/address.module';

@Module({
    imports: [
        forwardRef(() => ClientsModule),
        forwardRef(() => UsersModule),
        forwardRef(() => BookingFrequencyModule),
        forwardRef(() => BookingTypesModule),
        forwardRef(() => AddressModule)
    ],
    controllers: [CommonController],
    providers: [CommonService],
})
export class CommonModule {}
