import {
  Body,
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  Param, Query, Req,
  UseGuards,
} from '@nestjs/common';
import { CommonService } from './common.service';
import { AuthGuard } from '../auth/auth.guard';
import { ACGuard } from 'nest-access-control';
import ROLE_RESOURCES from '../@core/constants/roles.resources';
import { ApiTags } from '@nestjs/swagger';

@Controller(ROLE_RESOURCES.COMMON)
@ApiTags('Common')
export class CommonController {
  constructor(private readonly commonService: CommonService) {}

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @Get('/works/:clientType')
  async getWorks(
    @Req() req: Request,
    @Param('clientType') clientType: string,
  ) {

    //@ts-ignore
    const workspace = req.user.workspace;

    return {
      statusCode: HttpStatus.OK,
      data: await this.commonService.getWorks(clientType, workspace),
      message: 'Get works data successfully',
    };
  }

  @HttpCode(HttpStatus.OK)
  @Get('/address')
  async getAddress(
      @Query('street_one') street_one:string,
      @Query('city') city:string,
      @Query('state') state:string,
  ) {

    const address = await this.commonService.getAddress({street_one,city,state})

    if (address){
      return {
        statusCode: HttpStatus.OK,
        data: await this.commonService.getAddress({street_one,city,state}),
        message: 'Get address data successfully',
      };
    }else{
        return {
            statusCode: HttpStatus.NOT_FOUND,
            message: 'Address not found',
        };
    }

  }

}
