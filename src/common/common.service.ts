import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { BookingFrequencyService } from "../booking-frequency/booking-frequency.service";
import { BookingTypesService } from "../booking-types/booking-types.service";
import { AddressService } from '../address/address.service';

@Injectable()
export class CommonService {
  constructor(
    private readonly bookingFrequencyService: BookingFrequencyService,
    private readonly bookingTypesService: BookingTypesService,
    @Inject(forwardRef(()=>AddressService) )
    private addressService: AddressService
  ) {}

  async getWorks(clientType: string, workspace: string) {
    const bookingFrequency = await this.bookingFrequencyService.findAll(workspace);
    const bookingTypes = await this.bookingTypesService.getBookingTypesByClientType(clientType,workspace);

    return {
      frequency:bookingFrequency,
      services:bookingTypes
    }

  }

  async getAddress(address: {street_one:string,city:string,state:string}){
      return this.addressService.getAddressByStreet(address)
  }
}
