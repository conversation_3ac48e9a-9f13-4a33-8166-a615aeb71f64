import { forwardRef, HttpException, HttpStatus, Inject, Injectable } from "@nestjs/common";
import { UsersService } from "src/users/users.service";
import { CreateUserDto } from "./Dto/CreateUserDto";
import { CollectionDto } from "@forlagshuset/nestjs-mongoose-paginate";
import UpdateUserDto, { UpdateWorkspaceUserDto } from "./Dto/UpdateUserDto";
import { WorkspaceService } from "../workspace/workspace.service";
import { Workspace } from "../workspace/workspace.schema";

@Injectable()
export class AuthService {
  constructor(
    private usersService: UsersService,
    @Inject(forwardRef(() => WorkspaceService))
    private workspaceService: WorkspaceService
  ) {
  }

  async createUser(createUserDto: CreateUserDto): Promise<any> {
    const user = await this.usersService.createUser(createUserDto);

    return {
      statusCode: 200,
      data: {
        user
      }
    };
  }

  async queryUsers(collectionDto: CollectionDto): Promise<any> {
    const users = await this.usersService.queryAll(collectionDto);

    return {
      statusCode: 200,
      data: {
        users
      }
    };
  }

  async getUser(userId: string, workspace: string): Promise<any> {
    const user = await this.usersService.findOne(userId, workspace);

    return {
      statusCode: 200,
      data: {
        user
      }
    };
  }

  async updateUser(id: string, updateUserDto: UpdateUserDto): Promise<any> {
    let objectUpdate = updateUserDto as any;
    if (updateUserDto?.address) {
      let coordinates = updateUserDto.address.coordinates;
      if (coordinates.lat && coordinates.lng) {
        coordinates = [coordinates.lng, coordinates.lat] as any;
      }
      objectUpdate = {
        ...objectUpdate,
        address: {
          ...objectUpdate.address,
          coordinates
        }
      };
    }

    const updatedUser = await this.usersService.updateOne(
      id,
      objectUpdate
    );

    if (!updatedUser) {
      throw new HttpException("User not found", HttpStatus.NOT_FOUND);
    }
    return {
      message: "Update user successfully",
      statusCode: HttpStatus.OK,
      data: updatedUser
    };
  }

  async createWorkspaceUser(id: string, updateWorkspaceUserDto: UpdateWorkspaceUserDto): Promise<any> {

    const workspace = await this.workspaceService.create(updateWorkspaceUserDto);

    const updatedUser = await this.usersService.createWorkspaceOne(
      id,
      workspace._id.toString()
    );

    if (!updatedUser) {
      throw new HttpException("User not found", HttpStatus.BAD_REQUEST);
    } else {
      return {
        data: updatedUser,
        message: "Update user successfully",
        status: HttpStatus.OK
      };
    }
  }

  async getWorkspaceUser(workspace: string): Promise<Workspace> {
    return this.workspaceService.findOne(workspace);
  }

  async deleteUser(userId: string, workspace: string): Promise<any> {
    const deletedUser = await this.usersService.deleteOne(userId, workspace);

    if (!deletedUser) {
      throw new HttpException("User not found", HttpStatus.BAD_REQUEST);
    } else {
      return {
        message: "Delete user successfully",
        status: HttpStatus.OK
      };
    }
  }

  async inviteUser(email: string[],workspace:string, userId: string){
    return this.usersService.inviteUser(email, workspace, userId);
  }

  async revokeInviteUser(email: string, workspace: string, userId: string){
    return this.usersService.revokeInviteUser(email, workspace, userId);
  }

  async updateRole(id: string, role: string) {
    return this.usersService.updateRole(id, role);
  }
}
