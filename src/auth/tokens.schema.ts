import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';

@Schema()
export class Tokens {
  _id: MongooseSchema.Types.ObjectId;

  @Prop({ required: true })
  email: string;

  @Prop({ required: true })
  token: string;

  @Prop({ required: true, expires: 180 })
  createdAt: Date;
}

export type TokensDocument = Tokens & Document;
export const TokensSchema = SchemaFactory.createForClass(Tokens);
