import {
  CanActivate,
  ExecutionContext,
  HttpException,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { UsersService } from '../users/users.service';
import * as jwt from 'jsonwebtoken';
import { ClientsService } from '../clients/clients.service';

@Injectable()
export class SecondAuthGuard implements CanActivate {
  constructor(
    private usersService: UsersService,
    private clientService: ClientsService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const token = request.headers['authorization'];
    const tokenSession = jwt.decode(token);

    if (!tokenSession?.['sub']) {
      throw new HttpException('Unauthorized', 401);
    }

    let user: { [x: string]: { toString: () => any } };
    if (tokenSession?.['type'] === 'client') {
      user = await this.clientService.getRootClient(
        tokenSession['id'].toString(),
      );
      user['roles'] = [
        '6541cb0979c25f3473a77204', // User role - client role ID
      ];
    } else {
      user = await this.usersService.findOne(tokenSession['sub'].toString());
    }
    if (!user) throw new UnauthorizedException();
    request['user'] = user;
    return true;
  }
}
