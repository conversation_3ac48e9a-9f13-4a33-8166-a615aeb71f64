import {
  <PERSON>,
  Req,
  Get,
  Post,
  HttpCode,
  HttpStatus,
  UseGuards,
  Body,
  Param,
  Delete,
  Patch,
  Query
} from "@nestjs/common";
import { ApiTags } from "@nestjs/swagger";
import { AuthService } from "./auth.service";
import { Request } from "express";
import { AuthGuard, UserRequest } from "./auth.guard";
import { CreateUserDto } from "./Dto/CreateUserDto";
import { CollectionDto } from "@forlagshuset/nestjs-mongoose-paginate";
import ROLE_RESOURCES from "../@core/constants/roles.resources";
import UpdateUserDto, { UpdateWorkspaceUserDto } from "./Dto/UpdateUserDto";
import { ACGuard, UseRoles } from "nest-access-control";
import jwt from "jsonwebtoken";
import { UsersService } from "../users/users.service";

@ApiTags("Authentication")
@UseGuards(AuthGuard)
@Controller(ROLE_RESOURCES.AUTH)
export class AuthController {
  constructor(private authService: AuthService, private usersService: UsersService) {
  }

  @HttpCode(HttpStatus.OK)
  @Post("users")
  createUser(@Body() createUserDto: CreateUserDto) {
    return this.authService.createUser(createUserDto);
  }

  @HttpCode(HttpStatus.OK)
  @Get("users")
  async getUsers(
    @Query() collectionDto: CollectionDto,
    @Req() req: Request
  ) {
    const token = req.headers?.authorization;
    let workspace = "";

    if (token && !workspace) {
      const decoded = jwt.decode(token);
      const user = await this.usersService.findOne(decoded["id"]);
      workspace = user?.workspace?.toString();
      // @ts-ignore
      collectionDto.filter = {
        ...collectionDto.filter,
        workspace: workspace
      };
    }
    return this.authService.queryUsers(collectionDto);
  }

  @HttpCode(HttpStatus.OK)
  @Get("users/:id")
  getUser(
    @Param("id") id: string,
    @Body("workspace") workspace: string
  ) {
    return this.authService.getUser(id, workspace);
  }

  @HttpCode(HttpStatus.OK)
  @Patch("users/:id")
  async updateClient(
    @Param("id") id: string,
    @Body() updateUserDto: UpdateUserDto
  ) {
    return this.authService.updateUser(id, updateUserDto);
  }

  @HttpCode(HttpStatus.OK)
  @Delete("users/:id")
  remove(
    @Param("id") id: string,
    @Body("workspace") workspace: string
  ) {
    return this.authService.deleteUser(id, workspace);
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.AUTH,
    action: "update",
    possession: "own"
  })
  @Patch("profile/me")
  async updateMe(
    @Req() req: Request,
    @Body() updateUserDto: UpdateUserDto
  ) {
    // @ts-ignore
    const response = await this.authService.updateUser(req.user.id, updateUserDto);
    return {
      ...response
    };
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.AUTH,
    action: "update",
    possession: "own"
  })
  @Patch("preference")
  async updateMePreference(
    @Req() req: Request,
    @Body() updateUserDto: UpdateUserDto
  ) {

    try {// @ts-ignore
      const response = await this.authService.updateUser(req.user.id, updateUserDto);
      delete response.status;

      return {
        ...response,
        statusCode: HttpStatus.OK
      };
    } catch (e) {
      return {
        statusCode: HttpStatus.BAD_REQUEST,
        message: e.message
      };
    }
  }


  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.AUTH,
    action: "update",
    possession: "own"
  })
  @Patch("profile/company")
  async updateCompany(
    @Req() req: Request,
    @Body() updateWorkspaceUserDto: UpdateWorkspaceUserDto
  ) {
    // @ts-ignore
    const response = await this.authService.createWorkspaceUser(req.user.id, updateWorkspaceUserDto);
    return {
      ...response
    };
  }


  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.AUTH,
    action: "read",
    possession: "any"
  })
  @Get("profile/me")
  async getMe(
    @Req() req: Request
  ) {
    const token = req.headers?.authorization;
    let workspace;
    if (token) {
      const decoded = jwt.decode(token);
      const user = await this.usersService.findOne(decoded["id"]);
      workspace = user?.workspace?.toString();
    }

    // @ts-ignore
    const response = await this.authService.getUser(req.user.id, workspace);
    return {
      ...response
    };
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.AUTH,
    action: "update",
    possession: "any"
  })
  @Post("invite")
  async inviteUser(
    @Req() req: UserRequest,
    @Body("emails") emails: string[]
  ) {
    const workspace = req.user.workspace;

    const response = await this.authService.inviteUser(emails, workspace, req.user.id);
    return {
      statusCode: HttpStatus.CREATED,
      data: response,
      message: "Invite users successfully"
    };
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.AUTH,
    action: "update",
    possession: "any"
  })
  @Delete("invite/revoke")
  async revokeInvitation(
    @Req() req: UserRequest,
    @Body("email") email: string
  ) {
    const workspace = req.user.workspace;

    const response = await this.authService.revokeInviteUser(email, workspace, req.user.id);
    return {
      statusCode: HttpStatus.OK,
      data: response,
      message: "Revoke invitation successfully"
    };
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.AUTH,
    action: "update",
    possession: "any"
  })
  @Patch("role")
  async updateRole(
    @Req() req: UserRequest,
    @Body("role") role: string
  ) {
    const response = await this.authService.updateRole(req.user.id, role);
    return {
      statusCode: HttpStatus.OK,
      data: response,
      message: "Update role successfully"
    };
  }

}
