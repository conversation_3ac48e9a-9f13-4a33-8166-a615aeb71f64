import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
    IsBoolean, IsDate,
    IsDateString,
    IsEnum,
    IsNotEmpty,
    IsOptional,
    IsString,
    ValidateNested
} from "class-validator";
import { UserSocial } from '../../users/users.schema';
import { CreateWorkSpaceDto } from '../../workspace/Dto/CreateWorkSpaceDto';

class Address {
    @ApiProperty()
    @IsString()
    readonly street_one: string;

    @ApiProperty()
    @IsOptional()
    readonly street_two?: string;

    @ApiProperty()
    @IsString()
    readonly city: string;

    @ApiProperty()
    @IsString()
    readonly state: string;

    @ApiProperty()
    @IsOptional()
    readonly zip?: string;

    @ApiProperty()
    @IsNotEmpty()
    readonly coordinates: {
        lat: number;
        lng: number;
    };
}
export class CreateUserDto {
    @ApiProperty()
    @IsOptional()
    readonly profileImage?: string;

    @ApiProperty()
    @IsOptional()
    readonly firstName?: string;

    @ApiProperty()
    @IsOptional()
    readonly lastName?: string;

    @ApiProperty()
    @IsNotEmpty()
    readonly email: string;

    @ApiProperty()
    @IsOptional()
    readonly password?: string;

    @ApiProperty()
    @IsNotEmpty()
    @IsEnum(UserSocial)
    readonly social: string;

    @ApiProperty()
    @IsOptional()
    readonly phoneNumber?: string;

    @ApiProperty()
    @ValidateNested()
    @IsOptional()
    @Type(() => Address)
    readonly address?: Address;

    @ApiProperty()
    @IsOptional()
    readonly role: string;

    @ApiProperty()
    @IsOptional()
    readonly workspace?: string;

    @ApiProperty()
    @IsOptional()
    @IsDate()
    readonly dateOfBirth?: Date;

    @ApiProperty()
    @IsOptional()
    @IsBoolean()
    readonly gender?: boolean;

    @ApiProperty()
    @IsOptional()
    @IsString()
    readonly userStatus?: string;

    @ApiProperty()
    @IsOptional()
    @IsString()
    readonly invitationToken?: string;
}

export class CreateUserSocialDto{
    @ApiProperty()
    @IsNotEmpty()
    readonly token: string;

    @ApiProperty()
    @IsNotEmpty()
    @IsEnum(UserSocial)
    readonly social: string;

    @ApiProperty()
    @IsOptional()
    readonly phoneNumber?: string;

    @ApiProperty()
    @ValidateNested()
    @IsOptional()
    @Type(() => Address)
    readonly address?: Address;

    @ApiProperty()
    @IsOptional()
    readonly workspace?: string;

    @ApiProperty()
    @IsOptional()
    @IsString()
    readonly invitationToken?: string;
}
