import { PartialType } from "@nestjs/mapped-types";
import { CreateUserDto } from './CreateUserDto';
import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsEmail, IsEnum, IsNotEmpty, IsOptional, IsPhoneNumber, IsString, IsUrl } from 'class-validator';
import { EDateFormat, ELang, ETimeFormat } from '../../users/users.schema';

export default class UpdateUserDto extends PartialType(CreateUserDto) {
    @ApiProperty()
    @IsOptional()
    @IsBoolean()
    readonly isFullInformation?: boolean;

    @ApiProperty()
    @IsEnum(EDateFormat)
    @IsOptional()
    readonly dateFormat: string;

    @ApiProperty()
    @IsEnum(ETimeFormat)
    @IsOptional()
    readonly timeFormat: string;

    @ApiProperty()
    @IsEnum(ELang)
    @IsOptional()
    readonly lang: string;

    @ApiProperty()
    @IsOptional()
    readonly deviceType?: string;

    @ApiProperty()
    @IsOptional()
    readonly deviceToken?: string;
}

export class UpdateWorkspaceUserDto {
    @ApiProperty()
    @IsNotEmpty()
    @IsString()
    readonly name: string;

    @ApiProperty()
    @IsNotEmpty()
    @IsString()
    readonly logo: string;

    @ApiProperty()
    @IsOptional()
    @IsString()
    readonly workspace?: string;

    @ApiProperty()
    @IsString()
    @IsOptional()
    readonly stripeAccountId: string;

    @ApiProperty()
    @IsBoolean()
    @IsOptional()
    readonly isFirstLogin: boolean;

    @ApiProperty()
    @IsBoolean()
    @IsOptional()
    readonly isTrial: boolean;

}