import {
    MiddlewareConsumer,
    Module,
    NestModule,
    RequestMethod,
    forwardRef,
} from '@nestjs/common';
import { AuthService } from './auth.service';
import { UsersModule } from '../users/users.module';
import { JwtModule } from '@nestjs/jwt';
import { AuthController } from './auth.controller';
import { jwtConstants } from './constants';
import { MongooseModule } from '@nestjs/mongoose';
import { Tokens, TokensSchema } from './tokens.schema';
import { ClerkExpressRequireAuth } from '@clerk/clerk-sdk-node';
import { ClientsModule } from '../clients/clients.module';
import { WorkspaceModule } from '../workspace/workspace.module';
@Module({
    imports: [
        MongooseModule.forFeature([
            { name: Tokens.name, schema: TokensSchema },
        ]),
        forwardRef(() => ClientsModule),
        forwardRef(() => UsersModule),
        forwardRef(() => WorkspaceModule),
        JwtModule.register({
            global: true,
            secret: jwtConstants.secret,
            signOptions: { expiresIn: '60s' },
        }),
    ],
    providers: [AuthService],
    controllers: [AuthController],
    exports: [AuthService],
})
export class AuthModule implements NestModule {
    configure(consumer: MiddlewareConsumer) {
        consumer.apply(ClerkExpressRequireAuth()).forRoutes('/auth/*');
    }
}
