import { S3 } from '@aws-sdk/client-s3';
import { Module } from '@nestjs/common';


@Module({
  providers: [
    {
      provide: 'S3_CLIENT',
      useValue: new S3({
        credentials: {
          accessKeyId: process.env.SPACES_KEY,
          secretAccessKey: process.env.SPACES_SECRET_KEY,
        },
        endpoint: 'https://sfo3.digitaloceanspaces.com',
        region: 'sfo3',
      }),
    },
  ],
  exports: [
    {
      provide: 'S3_CLIENT',
      useValue: new S3({
        credentials: {
          accessKeyId: process.env.SPACES_KEY,
          secretAccessKey: process.env.SPACES_SECRET_KEY,
        },
        endpoint: 'https://sfo3.digitaloceanspaces.com',
        region: 'sfo3',
      }),
    },
  ],
})
export class StorageModule {}
