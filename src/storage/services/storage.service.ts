import {
  CompleteMultipartUploadCommandInput,
  CompleteMultipartUploadCommandOutput,
  CreateMultipartUploadCommandInput,
  CreateMultipartUploadCommandOutput,
  DeleteObjectCommandOutput,
  GetObjectCommand,
  GetObjectCommandOutput,
  ListPartsCommandInput,
  ListPartsCommandOutput,
  PutObjectCommandInput,
  PutObjectCommandOutput,
  S3,
  UploadPartCommandInput,
  UploadPartCommandOutput,
} from '@aws-sdk/client-s3';
import { Inject } from '@nestjs/common';
import { IStorageService } from './storage';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';

export class StorageService implements IStorageService {
  constructor(@Inject('S3_CLIENT') private readonly s3Client: S3) {}

  upload(
    key: string,
    file: Express.Multer.File | Buffer,
    mimetype?: string,
    workspace?: string,
  ): Promise<PutObjectCommandOutput> {
    if (file instanceof Buffer) {
      return this.s3Client.putObject({
        Bucket: process.env.SPACES_BUCKET_NAME,
        Key: workspace ? `${process.env.SPACES_FOLDER_NAME}/${workspace}/${key}` : `${process.env.SPACES_FOLDER_NAME}/${key}`,
        Body: file,
        ACL: 'public-read',
        ContentType: mimetype,
      });
    }
    return this.s3Client.putObject({
      Bucket: process.env.SPACES_BUCKET_NAME,
      Key: workspace ? `${process.env.SPACES_FOLDER_NAME}/${workspace}/${key}` : `${process.env.SPACES_FOLDER_NAME}/${key}`,
      Body: file.buffer,
      ACL: 'public-read',
      ContentType: file.mimetype,
    });
  }

  getMedia(key: string, workspace?: string): Promise<GetObjectCommandOutput> {
    return this.s3Client.getObject({
      Bucket: process.env.SPACES_BUCKET_NAME,
      Key:  workspace ? `${process.env.SPACES_FOLDER_NAME}/${workspace}/${key}` :`${process.env.SPACES_FOLDER_NAME}/${key}`,
    });
  }

  deleteMedia(key: string,workspace?:string): Promise<DeleteObjectCommandOutput> {
    return this.s3Client.deleteObject({
      Bucket: process.env.SPACES_BUCKET_NAME,
      Key:  workspace ? `${process.env.SPACES_FOLDER_NAME}/${workspace}/${key}`  :`${process.env.SPACES_FOLDER_NAME}/${key}`,
    });
  }

  createMultipartUpload(
    params: CreateMultipartUploadCommandInput,
  ): Promise<CreateMultipartUploadCommandOutput> {
    return this.s3Client.createMultipartUpload(params);
  }

  uploadPart(params: UploadPartCommandInput): Promise<UploadPartCommandOutput> {
    return this.s3Client.uploadPart(params);
  }

  listParts(params: ListPartsCommandInput): Promise<ListPartsCommandOutput> {
    return this.s3Client.listParts(params);
  }

  completeMultipartUpload(
    params: CompleteMultipartUploadCommandInput,
  ): Promise<CompleteMultipartUploadCommandOutput> {
    return this.s3Client.completeMultipartUpload(params);
  }

  getPresignedUrl(params: PutObjectCommandInput) {
    const command = new GetObjectCommand(params);
    return getSignedUrl(this.s3Client, command, { expiresIn: 60 * 60 });
  }

  getPublicUrl(key: string) {
    return `${process.env.SPACES_URL}/${process.env.SPACES_FOLDER_NAME}/${key}`;
  }
}
