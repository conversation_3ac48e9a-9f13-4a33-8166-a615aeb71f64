import {
  CompleteMultipartUploadCommandInput,
  CompleteMultipartUploadCommandOutput,
  CreateMultipartUploadCommandInput,
  CreateMultipartUploadCommandOutput,
  GetObjectCommandOutput,
  ListPartsCommandInput,
  ListPartsCommandOutput,
  PutObjectCommandInput,
  PutObjectCommandOutput,
  UploadPartCommandInput,
  UploadPartCommandOutput,
} from '@aws-sdk/client-s3';

export interface IStorageService {
  upload(
    key: string,
    file: Express.Multer.File,
  ): Promise<PutObjectCommandOutput>;
  getMedia(key: string): Promise<GetObjectCommandOutput>;
  createMultipartUpload(
    params: CreateMultipartUploadCommandInput,
  ): Promise<CreateMultipartUploadCommandOutput>;
  uploadPart(params: UploadPartCommandInput): Promise<UploadPartCommandOutput>;
  listParts(params: ListPartsCommandInput): Promise<ListPartsCommandOutput>;
  completeMultipartUpload(
    params: CompleteMultipartUploadCommandInput,
  ): Promise<CompleteMultipartUploadCommandOutput>;
  getPresignedUrl(params: PutObjectCommandInput): Promise<string>;
}
