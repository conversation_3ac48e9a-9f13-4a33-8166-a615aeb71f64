import {
  Body,
  Controller,
  Get,
  Delete,
  HttpCode,
  HttpException,
  HttpStatus,
  Param,
  Post,
  Query,
  Patch,
  BadRequestException,
  UseInterceptors,
  UseGuards, Res, Req,
} from '@nestjs/common';
import { QuotesService } from './quotes.service';
import {
  CollectionDto,
  CollectionResponse,
} from '@forlagshuset/nestjs-mongoose-paginate';
import { Quotes } from './quotes.schema';
import CreateQuoteDto from './Dto/CreateQuoteDto';
import UpdateQuoteDto from './Dto/UpdateQuoteDto';
import { QuotesInterceptor } from './quotes.interceptor';
import { QueryByRangeDateDto } from './Dto/QueryByRangeDateDto';
import mongoose from 'mongoose';
import { AuthGuard, UserRequest } from '../auth/auth.guard';
import { ACGuard, UseRoles } from 'nest-access-control';
import ROLE_RESOURCES from '../@core/constants/roles.resources';
import { ApiTags } from '@nestjs/swagger';
import { Request, Response } from 'express';
import * as process from 'process';

@Controller(ROLE_RESOURCES.QUOTES)
@ApiTags('Quotes')
export class QuotesController {
  constructor(private quotesService: QuotesService) {}

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.QUOTES,
    action: 'read',
    possession: 'any',
  })
  @Get('/')
  @UseInterceptors(QuotesInterceptor)
  async getQuotes(
    @Req() req: UserRequest,
    @Query()
    collectionDto: CollectionDto,
  ): Promise<CollectionResponse<Quotes>> {
    const workspace = req.user.workspace.toString()
    if (!collectionDto?.filter) {
      collectionDto = { ...collectionDto, filter: {} };
    }
    collectionDto.filter['workspace'] = {
      $eq: new mongoose.Types.ObjectId(workspace),
    };
    return this.quotesService.findAll(collectionDto);
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.QUOTES,
    action: 'read',
    possession: 'any',
  })
  @Get('/clients/:id')
  async getQuotesByClientId(
    @Req() req: UserRequest, 
    @Query()
    collectionDto: CollectionDto,
    @Param('id') id: string
  ): Promise<CollectionResponse<Quotes>> {
    const workspace = req.user.workspace
    return this.quotesService.findAllByClientId(id, workspace, collectionDto);
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.QUOTES,
    action: 'read',
    possession: 'any',
  })
  @Get('/range-dates')
  async getQuotesByRangeDate(
    @Req() req: UserRequest,
    @Query()
    param: QueryByRangeDateDto
  ) {
    const workspace = req.user.workspace.toString()
    let quotes = await this.quotesService.findAllByRangeDate(param, workspace);
    if (!quotes) {
      throw new HttpException(
        'Not found base on range dates',
        HttpStatus.BAD_REQUEST,
      );
    }
    return {
      statusCode: HttpStatus.OK,
      message: 'Get quotes success',
      data: quotes,
    };
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.QUOTES,
    action: 'read',
    possession: 'any',
  })
  @Get(':id')
  async getQuote(
    @Param('id') id: string,
    @Req() req: UserRequest,
  ) {
    const workspace = req.user.workspace
    let quote = await this.quotesService.findOne(id, workspace);
    if (!quote) {
      throw new HttpException('Quote is not existed', HttpStatus.BAD_REQUEST);
    }
    return {
      statusCode: HttpStatus.OK,
      message: 'Get quote success',
      data: quote,
    };
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.QUOTES,
    action: 'create',
    possession: 'any',
  })
  @Post('/')
  async createQuote(
      @Req() req: UserRequest,
      @Body() quote: CreateQuoteDto
  ) {
    const workspace = req.user.workspace
    let itemCreated = await this.quotesService.create({ ...quote,workspace });
    if (!itemCreated) {
      throw new HttpException('Can not create Quote', HttpStatus.BAD_REQUEST);
    }
    return {
      statusCode: HttpStatus.OK,
      message: 'Create quote successfully',
      data: itemCreated,
    };
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.QUOTES,
    action: 'update',
    possession: 'any',
  })
  @Patch('/:id')
  async updateQuote(
      @Req() req: UserRequest,
      @Param('id') id: string,
      @Body() quote: UpdateQuoteDto
  ) {
    const workspace = req.user.workspace
    try {
      let itemUpdated = await this.quotesService.update(id, { ...quote,workspace });
      if (!itemUpdated) {
        throw new HttpException('Can not update Quote', HttpStatus.BAD_REQUEST);
      }

      return {
        statusCode: HttpStatus.OK,
        message: 'Update quote successfully',
        data: itemUpdated,
      };
    } catch (err) {
      throw new BadRequestException(err.message);
    }
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.QUOTES,
    action: 'delete',
    possession: 'any',
  })
  @Delete('/:id')
  async deleteQuote(
    @Req() req: UserRequest,
    @Param('id') id: string,
  ) {
    const workspace = req.user.workspace
    let itemDeleted = await this.quotesService.delete(id, workspace);
    if (!itemDeleted) {
      throw new HttpException('Can not delete Quote', HttpStatus.BAD_REQUEST);
    }

    return {
      statusCode: HttpStatus.OK,
      message: 'Delete quote successfully',
      data: itemDeleted,
    };
  }


  @HttpCode(HttpStatus.OK)
  @Get('/approve-reject/:id')
  async approveQuote(
      @Param('id') id: string,
      @Query('token') token: string,
      @Query('workspace') workspace: string,
      @Query('status') status: 'approved' | 'rejected',
      @Res() res: Response
  ) {

    await this.quotesService.approveQuote(id,token,status,workspace);

    res.redirect(process.env.WEB_APP_URL + '/thanks')
  }

  @HttpCode(HttpStatus.OK)
  @Get('/create-job-by-quote/:id')
    async createJobByQuote(
        @Param('id') id: string,
        @Query('token') token: string,
        @Query('workspace') workspace: string,
        @Res() res: Response
    ) {

        const job = await this.quotesService.createJobByQuote(id,token,workspace);

        res.redirect(process.env.WEB_APP_URL + '/work/jobs/' + job._id.toString())
    }

}
