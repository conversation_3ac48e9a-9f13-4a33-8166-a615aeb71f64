import { CollectionDto, DocumentCollector } from "@forlagshuset/nestjs-mongoose-paginate";
import { BadRequestException, forwardRef, HttpException, HttpStatus, Inject, Injectable } from "@nestjs/common";
import * as jwt from "jsonwebtoken";
import { Quotes, QuotesDocument } from "./quotes.schema";
import { InjectModel } from "@nestjs/mongoose";
import mongoose, { Model } from "mongoose";
import CreateQuoteDto from "./Dto/CreateQuoteDto";
import UpdateQuoteDto from "./Dto/UpdateQuoteDto";
import { QueryByRangeDateDto } from "./Dto/QueryByRangeDateDto";
import { ClientsService } from "src/clients/clients.service";
import { paginateWithCount } from "src/utils/helpers";
import { Quote_Email } from "./templates/quote_email";
import { SendEmailService } from "../send-email/send-email.service";
import { AuthenticationService } from "../iam/authentication/authentication.service";
import { TokenStorageService } from "../token-storage/token-storage.service";
import { Approve_Quote_Email } from "./templates/approve_quote_email";
import { Reject_Quote_Email } from "./templates/reject_quote_email";
import { JobsService } from "../jobs/jobs.service";
import { Jobs } from "../jobs/jobs.schema";
import { UsersService } from "../users/users.service";
import { WorkspaceService } from "../workspace/workspace.service";
import { MediasService } from '../medias/medias.service';

interface MatchType {
    status?: string;
    clientType?: string;
    dateCreated: {
        $gte: Date;
        $lte: Date;
    };
    workspace: mongoose.Types.ObjectId;
}

@Injectable()
export class QuotesService {
    constructor(
        @Inject(forwardRef(() => ClientsService))
        private ClientsService: ClientsService,
        @Inject(forwardRef(() => UsersService))
        private usersService: UsersService,
        @InjectModel(Quotes.name) private quotesModel: Model<QuotesDocument>,
        @Inject(forwardRef(() => SendEmailService))
        private SendEmailService: SendEmailService,
        @Inject(forwardRef(() => AuthenticationService))
        private AuthenticationService: AuthenticationService,
        @Inject(forwardRef(() => TokenStorageService))
        private TokenStorageService: TokenStorageService,
        @Inject(forwardRef(() => JobsService))
        private JobsService: JobsService,
        @Inject(forwardRef(() => WorkspaceService))
        private WorkspaceService: WorkspaceService,
        @Inject(forwardRef(() => MediasService))
        private mediasService: MediasService,
    ) {}

    async findAll(collectionDto: CollectionDto): Promise<any> {
        const collector = new DocumentCollector<QuotesDocument>(
            this.quotesModel,
        );
        if (collectionDto?.sort) {
            // @ts-ignore
            collectionDto.sort = JSON.parse(collectionDto.sort);
            // @ts-ignore
            collectionDto.sorter = collectionDto.sort;
        }
        return collector.find(collectionDto);
    }

    async findAllByClientId(
        id: string,
        workspace: string,
        collectionDto: CollectionDto,
    ): Promise<any> {
        const client = await this.ClientsService.getClient({
            id,
            workspace,
        });

        if (!client) {
            throw new HttpException('Client not existed', HttpStatus.CONFLICT);
        }

        let mainEmail = client.email.find((i) => i.type == 'main').item;

        let quotes: any = await this.quotesModel.aggregate([
            {
                $lookup: {
                    from: 'bookingtypes',
                    localField: 'service',
                    foreignField: '_id',
                    as: 'bookingTypes',
                },
            },
            {
                $lookup: {
                    from: 'bookingrequests',
                    localField: 'bookingRequest',
                    foreignField: '_id',
                    as: 'bookingRequest',
                },
            },
            {
                $lookup: {
                    from: 'bookingfrequencies',
                    localField: 'frequency',
                    foreignField: '_id',
                    as: 'frequency',
                },
            },
            {
                $set: {
                    _id: '$_id',
                    title: { $first: '$bookingRequest.address' },
                    service: { $first: '$bookingTypes.name' },
                    frequency: { $first: '$frequency.title' },
                    price: '$price',
                    dateCreated: '$dateCreated',
                },
            },
            {
                $addFields: {
                    email: { $first: '$bookingRequest.email' },
                },
            },
            {
                $match: {
                    email: mainEmail,
                    workspace: new mongoose.Types.ObjectId(workspace),
                },
            },
            {
                $unset: [
                    'bookingRequest',
                    'addons',
                    'extras',
                    'staffs',
                    'video',
                    'attachments',
                    'suitableDate',
                    '__v',
                    'bookingTypes',
                    'notes',
                    'email',
                ],
            },
            {
                $facet: {
                    data: [
                        {
                            $skip:
                                (collectionDto?.page || 0) *
                                (collectionDto?.limit || 50),
                        },
                        { $limit: collectionDto?.limit || 50 },
                    ],
                    pagination: [
                        {
                            $count: 'total',
                        },
                    ],
                },
            },
            {
                $addFields: {
                    pagination: {
                        $first: '$pagination',
                    },
                },
            },
        ]);

        let result = quotes[0];

        if (!result.pagination) {
            result.pagination = {
                total: 0,
            };
        }

        let pagination = paginateWithCount(
            collectionDto,
            result.pagination.total,
        );

        return {
            ...result,
            pagination: {
                ...pagination,
            },
        };
    }

    async findAllByRangeDate(
        param: QueryByRangeDateDto,
        workspace: string,
    ): Promise<any> {
        let { startDate, endDate, clientType, status } = param;

        let $match: MatchType = {
            dateCreated: {
                $gte: startDate,
                $lte: endDate,
            },
            workspace: new mongoose.Types.ObjectId(workspace),
        };

        if (clientType) {
            $match = {
                ...$match,
                clientType: clientType,
            };
        }

        if (status) {
            $match = {
                ...$match,
                status: status,
            };
        }

        let query = [
            {
                $lookup: {
                    from: 'bookingrequests',
                    localField: 'bookingRequest',
                    foreignField: '_id',
                    as: 'request',
                },
            },
            {
                $addFields: {
                    addressId: {
                        $toObjectId: {
                            $first: "$request.address"
                        }
                    }
                }
            },
            {
                $lookup: {
                    from: "addresses", // replace with your actual addresses collection name
                    localField: "addressId",
                    foreignField: "_id",
                    as: "addressContent"
                }
            },
            {
                $lookup: {
                    from: 'bookingtypes',
                    localField: 'service',
                    foreignField: '_id',
                    as: 'bookingTypes',
                },
            },
            {
                $lookup: {
                    from: 'bookingfrequencies',
                    localField: 'frequency',
                    foreignField: '_id',
                    as: 'frequency',
                },
            },
            {
                $addFields: {
                    clientType: { $first: '$bookingTypes.clientType' },
                },
            },
            {
                $addFields: {
                    status: '$status',
                },
            },
            {
                $set: {
                    _id: '$_id',
                    title: {
                        $concat: [
                            { $first: "$request.firstName" },
                            " ",
                            { $first: "$request.lastName" },
                        ]
                    },
                    address:{
                        $concat: [
                            { $first: "$addressContent.street_one" },
                            ", ",
                            { $first: "$addressContent.city" },
                            ", ",
                            { $first: "$addressContent.state" }
                        ]
                    },
                    service: { $first: '$bookingTypes.name' },
                    sentDate: '$dateCreated',
                    frequency: { $first: '$frequency.title' },
                    price: '$price',
                    suitableDate: '$suitableDate',
                    dateCreated: '$dateCreated',
                },
            },
            {
                $unset: [
                    'bookingRequest',
                    'request',
                    'addons',
                    'extras',
                    'staffs',
                    'video',
                    'attachments',
                    '__v',
                    'bookingTypes',
                ],
            },
            {
                $match,
            },
        ];
        return this.quotesModel.aggregate(query);
    }

    async findOne(id: string, workspace: string): Promise<Quotes> {
        let item = await this.quotesModel
            .findOne({
                _id: new mongoose.Types.ObjectId(id),
                workspace: new mongoose.Types.ObjectId(workspace),
            })
            .populate({
               path: 'bookingRequest',
               populate: [{ path: 'address' }, { path: 'service' }], // populate address in bookingRequest
            })
            .populate({
                path: 'service',
                populate: { path: 'clientType' } // populate addons in service
            })
            .populate({
                path: 'frequency',
                populate: { path: 'discountFrequency' } // populate discountFrequency in frequency
            })
            .populate('addons.addon')
            .populate('extras.extra')
            .populate('staffs')
            .populate('workspace')
        if (!item) {
            throw new HttpException('Quote is not existed', HttpStatus.NOT_FOUND);
        }

        const data = item.toObject();
        let addons = data.addons;
        let extras = data.extras;
        let addonsQuery = [];
        let extrasQuery = [];

        if (addons) {
            addonsQuery = await Promise.all(addons.map(async (addon) => {
                const svgContent = await this.mediasService.getMedia(addon.addon.icon);
                addon.addon = {
                    ...addon.addon,
                    //@ts-ignore
                    iconSVG: await svgContent.Body.transformToString()
                }
                return {
                    ...addon
                }
            }));
        }

        if (extras) {
            extrasQuery = await Promise.all(extras.map(async (extra) => {
                const svgContent = await this.mediasService.getMedia(extra.extra.icon);
                extra.extra = {
                    ...extra.extra,
                    //@ts-ignore
                    iconSVG: await svgContent.Body.transformToString()
                }
                return {
                    ...extra
                }
            }));
        }

        data.addons = addonsQuery;
        data.extras = extrasQuery;

        return {
            ...data
        };
    }

    async findByBookingRequestId(bookingRequestId: string, workspace: string): Promise<Quotes> {
        return this.quotesModel.findOne({
            bookingRequest: new mongoose.Types.ObjectId(bookingRequestId),
            workspace: new mongoose.Types.ObjectId(workspace),
        })
        .populate({
            path: 'bookingRequest',
            populate: [
                { path: 'address' },
                { path: 'service' }
            ]
        })
        .populate({
            path: 'service',
            populate: { path: 'clientType' }
        })
        .populate('addons.addon')
        .populate('extras.extra')
        .populate({
            path: 'frequency',
            populate: { path: 'discountFrequency' }
        })
        .populate('staffs')
        .populate('workspace')
        .lean()
        .exec()
    }

    async create(quotes: CreateQuoteDto & {workspace:string}): Promise<Quotes> {
        const newId = new mongoose.Types.ObjectId();
        const created = await this.quotesModel.create({
            _id: newId,
            bookingRequest: new mongoose.Types.ObjectId(quotes.bookingRequest),
            service: new mongoose.Types.ObjectId(quotes.service),
            addons: quotes.addons,
            extras: quotes.extras,
            frequency: new mongoose.Types.ObjectId(quotes.frequency),
            video: quotes?.video,
            attachments: quotes?.attachments,
            suitableDate: quotes?.suitableDate,
            status: quotes?.status || 'waiting',
            workspace: new mongoose.Types.ObjectId(quotes.workspace),
            price: quotes?.price,
            dateCreated: new Date(),
            hours: quotes?.hours,
            notes: quotes?.notes,
            discountCode: quotes?.discountCode,
            duration: quotes?.duration,
            endDate: quotes?.endDate,
            timezone: quotes?.timezone,
        });
        await created.populate({
            path: 'bookingRequest',
            populate: [
                { path: 'address' },
                { path: 'service' }
            ]
        });
        await created.populate({
            path: 'service',
            populate: { path: 'clientType' }
        });
        await created.populate('addons.addon');
        await created.populate('extras.extra');
        await created.populate({
            path: 'frequency',
            populate: { path: 'discountFrequency' }
        });
        await created.populate('workspace');

        // Send notification to Admins
        if (created) {
            const genQuoteNumber = this.genQuoteNumber(created, newId);
            created.quoteNumber = genQuoteNumber;
            await created.save();
            const token = await this.AuthenticationService.generateToken({
                email: created.bookingRequest.email
            });

            await this.TokenStorageService.createTokenStorage({token})
            const workspaceObject = await this.WorkspaceService.findOne(quotes.workspace);

            // Disable auto-send email
            // Quote_Email({
            //     data: created,
            //     token: token,
            //     workspace:quotes.workspace,
            //     logo: workspaceObject.logo,
            //     quoteNumber: genQuoteNumber
            // }).then((html) => {
            //     this.SendEmailService.sendEmailGoogle({
            //         from: created.workspace.name,
            //         body: html,
            //         subject: this.createEmailSubject(created, null, genQuoteNumber),
            //         toEmail: created.bookingRequest.email,
            //     }, quotes.workspace)
            // })

            return created;
        }
        throw new BadRequestException(`Failed to create new quotes, try again`);
    }

    async update(id: string, quotes: UpdateQuoteDto & {workspace:string}): Promise<any> {
        const checkExist = await this.quotesModel.findById(id).exec();
        if (!checkExist) {
            throw new HttpException(
                'Quote is not existed',
                HttpStatus.CONFLICT,
            );
        }

        let updateData = { ...quotes };

        try {
            ['service', 'frequency', 'workspace','bookingRequest'].forEach(key => {
                if (quotes?.[key]) {
                    updateData[key] = new mongoose.Types.ObjectId(quotes[key]);
                }
            });

            return this.quotesModel.findByIdAndUpdate(
              id,
              { ...updateData },
              { new: true, runValidators: true },
            )
            .populate({
                path: 'bookingRequest',
                populate: [
                    { path: 'address' },
                    { path: 'service' }
                ]
            })
            .populate({
                path: 'service',
                populate: { path: 'clientType' }
            })
            .populate('addons.addon')
            .populate('extras.extra')
            .populate({
                path: 'frequency',
                populate: { path: 'discountFrequency' }
            })
            .populate('staffs')
            .populate('workspace');
        } catch (error) {
            throw new Error(`Error converting to ObjectId: ${error}`);
        }
    }

    async delete(id: string, workspace: string): Promise<Quotes> {
        const qoutes = await this.quotesModel.findOne({
            _id: new mongoose.Types.ObjectId(id),
            workspace: new mongoose.Types.ObjectId(workspace)
        });
        if (!qoutes) {
            throw new HttpException("Quote is not existed", HttpStatus.NOT_FOUND);
        }
        const bookingRequestId = qoutes.bookingRequest;
        const jobRelated = await this.JobsService.findByBookingRequestId(bookingRequestId.toString(), workspace);
        if (jobRelated) {
            await this.JobsService.delete(jobRelated._id.toString(), workspace);
        }
        return qoutes.deleteOne();
    }

    async approveQuote(id: string, token: string, status: 'approved' | 'rejected',workspace:string) {

        const admin = await this.usersService.findOwnerByWorkspace(workspace);

        const isValidToken = await this.TokenStorageService.checkValidTokenStorage(token);

        const tokenAdmin  = await this.AuthenticationService.generateTokenForAdmin(admin.email)

        const workspaceObject = await this.WorkspaceService.findOne(workspace);

        await this.TokenStorageService.createTokenStorage({token: tokenAdmin})

        if (!isValidToken) {
            throw new HttpException('Invalid token', 401);
        }

        const tokenSession = jwt.decode(token);
        if (!tokenSession?.['sub']) {
            throw new HttpException('Unauthorized', 401);
        }

        let user: { [x: string]: { toString: () => any } };
        user = await this.ClientsService.getClient(
            {
                id: tokenSession['id'],
                workspace,
            }
        );

        if (!user) {
            throw new HttpException('Client not found', 401);
        }

        const quote = await this.quotesModel.findOne({
            _id: new mongoose.Types.ObjectId(id),
            workspace: new mongoose.Types.ObjectId(workspace),
        }).populate(['bookingRequest','service']).populate('workspace').populate({
            path: 'frequency',
            populate: { path: 'discountFrequency' } // populate discountFrequency in
        });

        if (!quote) {
            throw new HttpException('Quote is not existed', HttpStatus.NOT_FOUND);
        }

        // @ts-ignore
        if (quote.bookingRequest.email !== user.email.find((i) => i.type === 'main').item) {
            throw new HttpException('Quote is not for this user', HttpStatus.FORBIDDEN);
        }

        const genQuoteNumber = this.genQuoteNumber(quote);
        quote.status = status;
        quote.quoteNumber = genQuoteNumber;
        await quote.save();


        if (status === 'approved') {
            Approve_Quote_Email({
                data: quote,
                token: tokenAdmin,
                logo: workspaceObject.logo,
                name: workspaceObject.name,
                quoteNumber: genQuoteNumber,
            }).then((html) => {
                this.SendEmailService.sendEmailGoogle({
                    from: workspaceObject.name,
                    body: html,
                    subject: this.createEmailSubject(quote, 'Approved', genQuoteNumber),
                    toEmail: admin.email,
                }, workspace)
            })
        }

        if (status === 'rejected') {
            Reject_Quote_Email({
                data: quote,
                token: tokenAdmin,
                logo: workspaceObject.logo,
                name: workspaceObject.name,
                quoteNumber: genQuoteNumber,
            }).then((html) => {
                this.SendEmailService.sendEmailGoogle({
                    from: workspaceObject.name,
                    body: html,
                    subject: this.createEmailSubject(quote, 'Rejected', genQuoteNumber),
                    toEmail: admin.email,
                }, workspace)
            })
        }



        return quote;
    }

    async createJobByQuote(quoteId: string, token:string ,workspace: string):Promise<Jobs> {
        const isValidToken = await this.TokenStorageService.checkValidTokenStorage(token);

        if (!isValidToken) {
            throw new HttpException('Invalid token', 401);
        }

        const tokenSession = jwt.decode(token);
        if (!tokenSession?.['sub']) {
            throw new HttpException('Unauthorized', 401);
        }

        let user: { [x: string]: { toString: () => any } };
        user = await this.usersService.findOne(
            tokenSession['userId']
        );

        if (!user) {
            throw new HttpException('User not found', 401);
        }

        const quote = await this.quotesModel.findOne({
            _id: new mongoose.Types.ObjectId(quoteId),
            workspace: new mongoose.Types.ObjectId(workspace),
        }).populate(['bookingRequest','service']);

        if (!quote) {
            throw new HttpException('Quote is not existed', HttpStatus.NOT_FOUND);
        }

        return await this.JobsService.create({
            addons: quote.addons.map((i) => {
                return {
                    addon: i.addon.toString(),
                    quantity: i.quantity,
                };
            }),
            attachments: quote.attachments,
            bookingRequest: quote.bookingRequest._id.toString(),
            extras: quote.extras.map((i) => {
                return {
                    extra: i.extra.toString(),
                    quantity: i.quantity,
                };
            }),
            frequency: quote.frequency.toString(),
            hours: quote.hours,
            notes: quote.notes,
            price: quote.price,
            service: quote.service.toString(),
            staffs: quote?.staffs?.map((i) => i.toString()) || [],
            status: 'unassigned',
            suitableDate: quote.suitableDate,
            video: quote.video,
            workspace: quote.workspace.id,
            discountCode: quote?.discountCode,
            square_ft: quote.bookingRequest.square_ft,
            duration: quote.duration,
            endDate: quote.endDate,
            timezone: quote.timezone
        });

    }

    private createEmailSubject(quote: Quotes, status?: string, quoteNumber?: string) {
        let quoteSubject = `${quote.workspace.name} | Quote | ${quote.service.name}`;

        if (quoteNumber) {
            quoteSubject = `${quoteSubject} | ${quoteNumber}`;
        }

        if (status) {
            quoteSubject = `${quoteSubject} | ${status}`;
        }

        return quoteSubject
    }

    genQuoteNumber(quote: Quotes, newId?: mongoose.Types.ObjectId) {
        const quoteName = `${quote.bookingRequest.firstName[0]}${quote.bookingRequest.lastName[0]}`;
        if (newId) {
            return `${quoteName}${newId.toString().slice(-6).toLocaleUpperCase()}`
        }
        return `${quoteName}${quote._id.toString().slice(-6).toLocaleUpperCase()}`
    }
}
