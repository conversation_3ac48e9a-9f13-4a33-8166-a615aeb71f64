import { Module, forwardRef } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>roller } from './quotes.controller';
import { QuotesService } from './quotes.service';
import { MongooseModule } from '@nestjs/mongoose';
import { Quotes, QuotesSchema } from './quotes.schema';
import { BookingFrequencyModule } from '../booking-frequency/booking-frequency.module';
import { BookingTypesModule } from '../booking-types/booking-types.module';
import { BookingRequestModule } from '../booking-request/booking-request.module';
import { ClientsModule } from 'src/clients/clients.module';
import { UsersModule } from '../users/users.module';
import { SendEmailModule } from '../send-email/send-email.module';
import { IamModule } from '../iam/iam.module';
import { TokenStorageModule } from '../token-storage/token-storage.module';
import { JobsModule } from '../jobs/jobs.module';
import { WorkspaceModule } from '../workspace/workspace.module';
import { MediasModule } from '../medias/medias.module';
import { Medias, MediasSchema } from '../medias/medias.schema';

@Module({
    imports: [
        forwardRef(() => UsersModule),
        forwardRef(() => BookingRequestModule),
        forwardRef(() => BookingTypesModule),
        forwardRef(() => BookingFrequencyModule),
        forwardRef(() => ClientsModule),
        forwardRef(() => SendEmailModule),
        forwardRef(() => IamModule),
        forwardRef(() => TokenStorageModule),
        forwardRef(() => JobsModule),
        forwardRef(() => UsersModule),
        forwardRef(() => WorkspaceModule),
        forwardRef(() => MediasModule),
        MongooseModule.forFeature([
            { name: Medias.name, schema: MediasSchema },
            { name: Quotes.name, schema: QuotesSchema },
        ])
    ],
    controllers: [QuotesController],
    providers: [QuotesService],
    exports: [QuotesService],
})
export class QuotesModule {}
