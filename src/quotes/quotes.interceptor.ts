import { <PERSON><PERSON><PERSON><PERSON>, ExecutionContext, Injectable, NestInterceptor } from "@nestjs/common";
import { map, Observable } from "rxjs";
import { Quotes } from "./quotes.schema";
import { BookingRequestService } from "../booking-request/booking-request.service";
import { BookingTypesService } from "../booking-types/booking-types.service";
import { BookingFrequencyService } from "../booking-frequency/booking-frequency.service";

@Injectable()
export class QuotesInterceptor implements NestInterceptor {

  constructor(
    private BookingRequestService: BookingRequestService,
    private BookingTypesService: BookingTypesService,
    private BookingFrequencyService: BookingFrequencyService
  ) {}
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    return next.handle()
      .pipe(map(async (data) => {
         let result = data.data;
         if(result?.length){
            result = result.map(async (item:Quotes) => {
              let bookingRequest = item.bookingRequest
              let serviceID = item.service
              let frequencyID = item.frequency
              if(bookingRequest){
                item.bookingRequest = await this.BookingRequestService.findOne(bookingRequest.toString(),item.workspace.toString())
              }
              if (serviceID) {
                item.service = await this.BookingTypesService.getBookingType(serviceID.toString())
              }
              if (frequencyID) {
                 item.frequency = await this.BookingFrequencyService.findOne(frequencyID.toString(),item.workspace.toString())
              }
              return item
            })
            await Promise.all(result)
         }
        return { ...data }
      }))
  }
}
