import path from 'path';
import { TypeTemplate } from '../../@core/constants/enums';
import { readFile } from 'node:fs/promises';
import { Quotes } from '../quotes.schema';
import dayjs from 'dayjs';
import { convertParamsInTemplate } from './convertParamsInTemplate';

interface Address {
    street_one?: string;
    street_two?: string;
    city?: string;
    state?: string;
    zip?: string;
}

type JobEmailParams = {
    data: Quotes;
    token: string;
    workspace: string;
    logo: string;
    jobNumber: string;
}

export const Job_Email = async ({data, token, logo, jobNumber}: JobEmailParams):Promise<string> => {
    const template = path.join(
        __dirname,
        '..',
        '..',
        '..',
        'public',
        'templates',
        'job.html',
    );
    const public_url = `${process.env.SPACES_URL}/${process.env.SPACES_FOLDER_NAME}/`
    
    const image_item = data.attachments?.map((attachment) => {
        const workspaceId = data.workspace?._id?.toString();
        return `<img src="${public_url + (workspaceId ? workspaceId + '/' : '') + attachment}" alt="attachment" width="300" height="200" layout="responsive"></img>`;
    }).join('\n') || '';

    const freqDiscount = data.frequency?.discountFrequency?.discount || data.frequency?.discount || 0;
    const discount = freqDiscount ? (Math.round(((freqDiscount / 100) * (data.price || 0)) * 100) / 100) : 0;

    const items_raw = [
        {
            content: 'Service: ' + (data.service?.name || ''),
            amount: data.service?.price ? '$' + data.service.price : ''
        },
        ...(data.addons || []).map((addon) => {
            if(addon.addon?.typed === 'range'){
                const item = addon.addon.range?.[addon.quantity] || addon.addon.range?.find((range) => range.price == addon.quantity);
                return {
                    content: (addon.addon?.name || '') + ': ' + (item?.name || ''),
                    amount: item?.price ? '$' + item.price : ''
                }
            }
            return {
                content: (addon.quantity || '') + ' x ' + (addon.addon?.name || ''),
                amount: addon.addon?.price ? '$' + (addon.addon.price * (addon.quantity || 0)) : ''
            }
        }),
        ...(data.extras || []).map((extra) => {
            return {
                content: (extra.quantity || '') + ' x ' + (extra.extra?.name || ''),
                amount: extra.extra?.price ? '$' + (extra.extra.price * (extra.quantity || 0)) : ''
            }
        }),
    ];

    if (data.frequency?.title) {
        items_raw.push({
            content: 'Frequency: ' + data.frequency.title,
            amount: data.service?.name === 'Individual' ? '- $' + discount : ''
        });
    }

    const bookingAddress = (data.bookingRequest?.address || {}) as Address;
    const workspaceAddress = (data.workspace?.address || {}) as Address;

    return await convertParamsInTemplate(
        template,
        {
            logo: public_url + (logo || ''),
            quote_id: jobNumber,
            quote_issued: dayjs(data.dateCreated || new Date()).format('MMM DD YYYY'),
            name: (data.bookingRequest?.firstName || '') + ' ' + (data.bookingRequest?.lastName || ''),
            pay_to: data.workspace?.name || '',
            address: bookingAddress.street_one || '',
            city: bookingAddress.city || '',
            state: bookingAddress.state || '',
            zip_code: bookingAddress.zip || '',
            items: items_raw.map((item, index) => {
                if (index % 2 === 0) {
                    return `
                        <tr class="contentItem bgGray">
                            <td class="valueTable">${item.content || ''}</td>
                            <td class="valueTable valueTotal">${item.amount || ''}</td>
                        </tr>
                    `;
                }
                if (index % 2 !== 0) {
                    return `
                        <tr class="contentItem">
                            <td class="valueTable">${item.content || ''}</td>
                            <td class="valueTable valueTotal">${item.amount || ''}</td>
                        </tr>
                    `;
                }
            }).join('\n'),
            pay_to_address: workspaceAddress.street_one 
                ? `${workspaceAddress.street_one || ''} ${workspaceAddress.city || ''} ${workspaceAddress.state || ''} ${workspaceAddress.zip || ''}`
                : (data.workspace?.name || ''),
            images: data.attachments?.length ? 
                `<amp-carousel class="ampCarousel" width="300" height="200" layout="responsive" type="slides">
                    ${image_item}
                </amp-carousel>`
                : '',
            total: data.service?.name === 'Individual' ? ((data.price || 0) - discount).toString() : (data.price || 0).toString(),
        },
        TypeTemplate.file,
    );
}

