import path from 'path';
import { Quotes } from '../quotes.schema';
import { convertParamsInTemplate } from './convertParamsInTemplate';
import { TypeTemplate } from '../../@core/constants/enums';

type ApproveQuoteEmailParams = {
    data: Quotes;
    token: string;
    logo: string;
    name: string;
    quoteNumber: string;
}

export const Approve_Quote_Email = async ({data,token,logo,name}: ApproveQuoteEmailParams):Promise<string> => {
    const public_url = `${process.env.SPACES_URL}/${process.env.SPACES_FOLDER_NAME}/`
    const template = path.join(
        __dirname,
        '..',
        '..',
        '..',
        'public',
        'templates',
        'approve_quotes.html',
    );

   return await convertParamsInTemplate(
       template,
       {
           logo: public_url + logo,
           name: name,
           user:  data.bookingRequest.firstName + ' ' + data.bookingRequest.lastName,
           url_quote: process.env.WEB_APP_URL + '/work/quotes/' + data._id,
           create_job_api: process.env.API_ROOT_URL + '/quotes/create-job-by-quote/' + data._id.toString() + '?token=' + token + '&workspace=' + data.workspace.toString(),
       },
       TypeTemplate.file,
   )
}

