import { TypeTemplate } from '../../@core/constants/enums';
import { readFile } from 'node:fs/promises';

export const convertParamsInTemplate = async (template: string, params: { [key: string]: string},typeTemplate: TypeTemplate) => {
    switch (typeTemplate) {
        case TypeTemplate.database:
            for (let key in params) {
                template = template.replace(`{{${key}}}`, params[key]);
            }
            break;
        case TypeTemplate.file:
            let filePath = template;
            let htmlContent = await readFile(filePath, { encoding: 'utf8' });

            for (let key in params) {
                htmlContent = htmlContent.replace(`{{${key}}}`, params[key]);
            }

            return htmlContent;
        default:
            return template;
    }
}