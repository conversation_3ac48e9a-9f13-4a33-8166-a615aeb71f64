import path from 'path';
import { TypeTemplate } from '../../@core/constants/enums';
import { readFile } from 'node:fs/promises';
import { Quotes } from '../quotes.schema';
import dayjs from 'dayjs';
import { convertParamsInTemplate } from './convertParamsInTemplate';

type QuoteEmailParams = {
    data: Quotes;
    token: string;
    workspace: string;
    logo: string;
    quoteNumber: string;
}

export const Quote_Email = async ({data, token,logo, quoteNumber}: QuoteEmailParams):Promise<string> => {
    const template = path.join(
        __dirname,
        '..',
        '..',
        '..',
        'public',
        'templates',
        'quote.html',
    );
    const public_url = `${process.env.SPACES_URL}/${process.env.SPACES_FOLDER_NAME}/`
    const image_item = data.attachments?.map((attachment) => {
        return `<img src="${public_url + data.workspace._id.toString() + '/' + attachment}" alt="attachment" width="300" height="200" layout="responsive"></img>`;
    }).join('\n');
    const freqDiscount = data.frequency?.discountFrequency?.discount || data.frequency?.discount
    const discount = freqDiscount ? (Math.round(((freqDiscount / 100) * data.price ) * 100 ) / 100) : 0


    const items_raw = [
        {
            content: 'Service: ' + data.service.name,
            amount: data.service.price ? '$' +data.service.price : ''
        },
        ...data?.addons.map((addon) => {
            if(addon.addon.typed === 'range'){
                const item = addon.addon.range?.[addon.quantity] || addon.addon.range.find((range) => range.price == addon.quantity)
                return {
                    content: addon.addon?.name + ': ' + item?.name,
                    amount:  item.price ? '$' + item.price : ''
                }
            }

            return {
                content:  '' + addon.quantity + ' x ' + addon.addon.name,
                amount: addon.addon.price ? '$' +addon.addon.price*addon.quantity : ''
            }
        }),
        ...data?.extras.map((extra) => {
            return {
                content: '' + extra.quantity + ' x ' + extra.extra.name,
                amount:  extra.extra.price ? '$' + extra.extra.price*extra.quantity: ''
            }
        }),
    ];
    if (data.frequency){
      items_raw.push({
        content: 'Frequency: ' + data.frequency.title,
        amount: data.service.name === 'Individual' ?  '- $' + discount : ''
      })
    }



    return await convertParamsInTemplate(
        template,
        {
            logo: public_url + logo,
            quote_id: quoteNumber,
            quote_issued: dayjs(data.dateCreated).format('MMM DD YYYY'),
            name: data.bookingRequest.firstName + ' ' + data.bookingRequest.lastName,
            pay_to: data.workspace.name,
            //@ts-ignore
            address: data.bookingRequest.address.street_one,
            //@ts-ignore
            city: data.bookingRequest.address.city,
            //@ts-ignore
            state: data.bookingRequest.address.state,
            //@ts-ignore
            zip_code: data.bookingRequest.address.zip || '',
            items: items_raw.map((item,index) => {
                if (index%2 === 0) {
                   return `
                        <tr class="contentItem bgGray">
                            <td class="valueTable">${item.content}</td>
                            <td class= "valueTable valueTotal">${item.amount}</td>
                        </tr>
                   `
                }
                if (index%2 !== 0) {
                    return `
                        <tr class="contentItem">
                            <td class="valueTable">${item.content}</td>
                            <td class= "valueTable valueTotal">${item.amount}</td>
                        </tr>
                    `
                }

            }).join('\n'),
            pay_to_address:  data.workspace.address
                ? `${data.workspace.address.street_one} ${data.workspace.address.city} ${data.workspace.address.state} ${data.workspace.address.zip || ''}`
                : data.workspace.name,
            images: data.attachments?.length > 0 ?
                `<amp-carousel class="ampCarousel" width="300" height="200" layout="responsive" type="slides" >
                    ${image_item}
                </amp-carousel>`
                : '',
            total: data.service.name === 'Individual' ? (data.price - discount).toString(): data.price.toString(),
            api_url_approve: process.env.API_ROOT_URL + '/quotes/approve-reject/' + data._id.toString() + '?status=approved&token=' + token + '&workspace=' + data.workspace._id.toString(),
            api_url_reject: process.env.API_ROOT_URL + '/quotes/approve-reject/' + data._id.toString() + '?status=rejected&token=' + token + '&workspace=' + data.workspace._id.toString(),
        },
        TypeTemplate.file,
    );
}

