import { ApiProperty } from '@nestjs/swagger';
import {
  IsDate,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional, IsPositive,
  IsString,
  ValidateNested
} from "class-validator";
import { Type } from 'class-transformer';

class Addon {
  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  quantity: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  addon: string;
}

class Extra {
  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  quantity: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  extra: string;
}

export default class CreateQuoteDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  readonly bookingRequest: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  readonly service: string;

  @ApiProperty()
  @IsNotEmpty()
  @ValidateNested({ each: true })
  @Type(() => Addon)
  readonly addons: Addon[];

  @ApiProperty()
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => Extra)
  readonly extras?: Extra[];

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  readonly frequency: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  readonly video: string;

  @ApiProperty()
  @IsOptional()
  @IsString({ each: true })
  readonly attachments: string[];

  @ApiProperty()
  @IsOptional()
  @IsString()
  readonly notes: string;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  readonly price: number;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  readonly hours: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsDate()
  readonly suitableDate: Date;

  @ApiProperty()
  @IsOptional()
  @IsString()
  readonly discountCode: string;

  @ApiProperty()
  @IsOptional()
  @IsEnum(['waiting', 'approved','rejected'])
  readonly status: 'waiting' | 'approved' | 'rejected';

  @ApiProperty()
  @IsPositive()
  @IsNumber()
  duration: number;

  @ApiProperty()
  @IsOptional()
  @IsDate()
  readonly endDate?: Date;

  @ApiProperty()
  @IsOptional()
  @IsString()
  readonly timezone?: string;
}
