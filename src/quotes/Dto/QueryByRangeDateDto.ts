import { IsDate, IsEnum, <PERSON>NotEmpty, <PERSON><PERSON>ptional, IsString } from "class-validator";
import { ApiProperty } from "@nestjs/swagger";

export class QueryByRangeDateDto{
  @ApiProperty()
  @IsDate()
  @IsNotEmpty()
  startDate: Date;

  @ApiProperty()
  @IsDate()
  @IsNotEmpty()
  endDate: Date;

  @ApiProperty()
  @IsString()
  @IsOptional()
  clientType: string;

  @ApiProperty()
  @IsEnum(['waiting', 'approved'])
  @IsOptional()
  status: string;
}