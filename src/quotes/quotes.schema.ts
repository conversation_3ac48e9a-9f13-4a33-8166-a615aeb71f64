import { <PERSON><PERSON>, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema, Types } from 'mongoose';
import { BookingAddon } from 'src/booking-addon/booking-addon.schema';
import { BookingExtra } from 'src/booking-extra/booking-extra.schema';
import { BookingFrequency } from 'src/booking-frequency/booking-frequency.schema';
import { BookingRequest } from 'src/booking-request/booking-request.schema';
import { BookingTypes } from 'src/booking-types/booking-types.schema';
import { Users } from 'src/users/users.schema';
import { Workspace } from "../workspace/workspace.schema";
import { Type } from "class-transformer";

@Schema()
export class Quotes {
  _id: MongooseSchema.Types.ObjectId;

  @Prop({ required: true, type: Types.ObjectId, ref: BookingRequest.name })
  bookingRequest: BookingRequest;

  @Prop({ required: true, type: Types.ObjectId, ref: BookingTypes.name })
  service: BookingTypes;

  @Prop({
    type: [{ quantity: { type: Number }, addon: { type: Types.ObjectId, ref: BookingAddon.name } }],
  })
  addons: { quantity: number; addon: BookingAddon }[];

  @Prop({
    type: [{ quantity: { type: Number }, extra: { type: Types.ObjectId, ref: BookingExtra.name } }],
    default: [],
  })
  extras: { quantity: number; extra: BookingExtra }[];

  @Prop({ required: true, type: Types.ObjectId, ref: BookingFrequency.name })
  frequency: BookingFrequency;

  @Prop()
  video: string;

  @Prop()
  attachments: string[];

  @Prop()
  suitableDate: Date;

  @Prop({ default: '' })
  notes: string;

  @Prop({ type: [Types.ObjectId], ref: Users.name })
  staffs: Users[];

  @Prop({ default: 'waiting', type: String, enum: ['waiting', 'approved','rejected'] })
  status: string;

  @Prop({ required: true })
  dateCreated: Date;

  @Prop({ required: true, type: Types.ObjectId, ref: Workspace.name })
  @Type(() => Workspace)
  workspace: Workspace;

  @Prop({required: true, default: 0})
  price: number;

  @Prop({required: true, default: 0})
  hours: number;

  @Prop({ required: false, default: null })
  discountCode: string;

  @Prop({ type: String })
  quoteNumber?: string;

  @Prop()
  duration: number;

  @Prop()
  endDate?: Date;

  @Prop()
  timezone?: string;
}

export type QuotesDocument = Quotes & Document;

export const QuotesSchema = SchemaFactory.createForClass(Quotes);
