import { Module, forwardRef } from '@nestjs/common';
import { ClientTypeController } from './client-type.controller';
import { MongooseModule } from '@nestjs/mongoose';
import { ClientTypes, ClientTypesSchema } from './client-type.schema';
import { ClientTypeService } from './client-type.service';
import { UsersModule } from '../users/users.module';
import { ClientsModule } from '../clients/clients.module';

@Module({
    imports: [
        MongooseModule.forFeature([
            { name: ClientTypes.name, schema: ClientTypesSchema },
        ]),
        forwardRef(() => UsersModule),
        forwardRef(() => ClientsModule),
    ],
    controllers: [ClientTypeController],
    providers: [ClientTypeService],
    exports: [ClientTypeService],
})
export class ClientTypeModule {}
