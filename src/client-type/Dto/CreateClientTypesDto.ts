import { ApiProperty } from '@nestjs/swagger';
import {IsString} from "class-validator";

export class CreateClientTypesDto {

  @ApiProperty()
  @IsString()
  readonly name: string;

  @ApiProperty()
  @IsString()
  readonly slug: string;

  @ApiProperty()
  @IsString()
  readonly colorPrimary: string;

  @ApiProperty()
  @IsString()
  readonly colorSecondary: string;

  @ApiProperty()
  @IsString()
  readonly workspace: string;
}
