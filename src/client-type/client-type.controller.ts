import { Body, Controller, Delete, Get, HttpCode, HttpStatus, Param, Post, Req, UseGuards } from '@nestjs/common';
import { ClientTypeService } from './client-type.service';
import { CreateClientTypesDto } from './Dto/CreateClientTypesDto';
import { Patch } from '@nestjs/common/decorators/http/request-mapping.decorator';
import { AuthGuard } from '../auth/auth.guard';
import { ACGuard, UseRoles } from 'nest-access-control';
import ROLE_RESOURCES from '../@core/constants/roles.resources';
import { ApiTags } from '@nestjs/swagger';

@Controller(ROLE_RESOURCES.CLIENT_TYPE)
@ApiTags('Client-type')
export class ClientTypeController {
  constructor(private clientTypeService: ClientTypeService) {}

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.CLIENT_TYPE,
    action: 'read',
    possession: 'any',
  })
  @Get()
  getClientTypes(
      @Req() req: Request,
  ) {
    //@ts-ignore
    const workspace = req?.user.workspace
    return this.clientTypeService.getClientTypes(workspace);
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.CLIENT_TYPE,
    action: 'read',
    possession: 'any',
  })
  @Get(':slug')
  getClientType(
    @Param('slug') slug: string,
    @Body('workspace') workspace: string,
  ) {
    return this.clientTypeService.getClientType(slug, workspace);
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.CLIENT_TYPE,
    action: 'create',
    possession: 'any',
  })
  @Post()
  createClientTypes(@Body() createClientTypesDto: CreateClientTypesDto) {
    return this.clientTypeService.createClientType(createClientTypesDto);
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.CLIENT_TYPE,
    action: 'create',
    possession: 'own',
  })
  @Post('/multiple')
  async createMultipleClientTypes(
    @Req() req: Request,
    @Body() createClientTypesDto: CreateClientTypesDto[],
  ) {
    //@ts-ignore
    const workspace = req.user.workspace

    return this.clientTypeService.createMultipleClientTypes(createClientTypesDto,workspace);
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.CLIENT_TYPE,
    action: 'update',
    possession: 'any',
  })
  @Patch('/detail/:slug')
  updateClientType(
    @Param(':slug') slug: string,
    @Body() createClientTypesDto: CreateClientTypesDto,
  ) {
    return this.clientTypeService.updateClientType(slug, createClientTypesDto);
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.CLIENT_TYPE,
    action: 'delete',
    possession: 'any',
  })
  @Delete(':slug')
  deleteClientType(
    @Param('slug') slug: string,
    @Body('workspace') workspace: string,
  ) {
    return this.clientTypeService.deleteClientType(slug, workspace);
  }
}
