import { <PERSON><PERSON>, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema, Types } from "mongoose";
import { Workspace } from "../workspace/workspace.schema";
import { Type } from "class-transformer";

@Schema()
export class ClientTypes {
  _id: MongooseSchema.Types.ObjectId;

  @Prop({ required: true })
  name: string;

  @Prop({ required: true })
  slug: string;

  @Prop({ required: true })
  colorPrimary: string;

  @Prop({ required: true })
  colorSecondary: string;

  @Prop({ required: true, type: Types.ObjectId, ref: Workspace.name })
  @Type(() => Workspace)
  workspace: Workspace;
}

export type ClientTypesDocument = ClientTypes & Document;
export const ClientTypesSchema = SchemaFactory.createForClass(ClientTypes);
