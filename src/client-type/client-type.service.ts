import { BadRequestException, Injectable } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { ClientTypes, ClientTypesDocument } from "./client-type.schema";
import mongoose, { Model } from "mongoose";
import { CreateClientTypesDto } from "./Dto/CreateClientTypesDto";

@Injectable()
export class ClientTypeService {
  constructor(
    @InjectModel(ClientTypes.name) private clientTypeModel: Model<ClientTypesDocument>,
  ) {}

  async getClientTypes(workspace:string): Promise<any> {
    const clientTypes = await this.clientTypeModel.find({
      workspace: new mongoose.Types.ObjectId(workspace)
    }).exec();

    return {
      statusCode: 200,
      data: {
        clientTypes,
      },
    }
  }

  async getClientTypeById(id: string): Promise<ClientTypes> {
    let checkExist = await this.clientTypeModel.findById(id).exec()
    if (!checkExist) {
      throw new BadRequestException();
    }
    return checkExist
  }

  async getClientType(slug: string,workspace:string): Promise<any> {
    const clientType = await this.clientTypeModel.findOne({
      slug: slug,
      workspace: new mongoose.Types.ObjectId(workspace)
    }).exec();

    if (!clientType) {
      throw new BadRequestException();
    } else {
      return {
        statusCode: 200,
        data: clientType,
        message: 'Get client type successfully',
      };
    }
  }

  async createClientType(CreateClientTypesDto: CreateClientTypesDto): Promise<any> {
    const clientType = await this.clientTypeModel.create({
      ...CreateClientTypesDto,
      workspace: new mongoose.Types.ObjectId(CreateClientTypesDto.workspace)
    })

      return {
        statusCode: 200,
        data: clientType,
        message: 'Create client type successfully',
      };
  }

  async createMultipleClientTypes(CreateClientTypesDto: CreateClientTypesDto[],workspace:string): Promise<any> {
    const clientTypes = await this.clientTypeModel.insertMany(
      CreateClientTypesDto.map((item) => {
        return {
          ...item,
          workspace: new mongoose.Types.ObjectId(workspace),
        };
      }),
    )

    return {
      statusCode: 200,
      data: clientTypes,
      message: 'Create client types successfully',
    };
  }
  async updateClientType(slug: string, CreateClientTypesDto: CreateClientTypesDto): Promise<any> {
    const existedClientType = await this.clientTypeModel.findOne({
      slug: slug,
      workspace: new mongoose.Types.ObjectId(CreateClientTypesDto.workspace)
    }).exec();

    if (!existedClientType) {
      throw new BadRequestException();
    } else {
      await this.clientTypeModel.findOneAndUpdate(
          { slug: slug },
          {$set: {
            ...CreateClientTypesDto,
            workspace: new mongoose.Types.ObjectId(CreateClientTypesDto.workspace)
            }},
          {new: true}
      ).exec()

      return {
        statusCode: 200,
        message: 'Update client type successfully',
      };
    }
  }

  async deleteClientType(slug: string,workspace:string): Promise<any> {
    const existedClientType = await this.clientTypeModel.findOne({
      slug: slug,
      workspace: new mongoose.Types.ObjectId(workspace)
    }).exec();

    if (!existedClientType) {
      throw new BadRequestException();
    } else {
      await this.clientTypeModel.deleteOne({
        slug: slug,
        workspace: new mongoose.Types.ObjectId(workspace)
      })

      return {
        statusCode: 200,
        message: 'Delete client type successfully',
      };
    }
  }
}
