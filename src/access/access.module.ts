import { Module, forwardRef } from '@nestjs/common';
import { AccessControlModule, RolesBuilder } from 'nest-access-control';
import { RolesService } from '../roles/roles.service';
import { RolesModule } from '../roles/roles.module';

@Module({
    imports: [
        AccessControlModule.forRootAsync({
            imports: [forwardRef(() => RolesModule)],
            inject: [RolesService],
            useFactory: async (
                roleService: RolesService,
            ): Promise<RolesBuilder> => {
                const roles = await roleService.getAllRoles();
                return new RolesBuilder([].concat(...roles));
            },
        }),
    ],
})
export class AccessModule {}
