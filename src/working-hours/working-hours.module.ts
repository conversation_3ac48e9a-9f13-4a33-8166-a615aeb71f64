import { Module, forwardRef } from '@nestjs/common';
import { WorkingHoursController } from './working-hours.controller';
import { WorkingHoursService } from './working-hours.service';
import { UsersModule } from 'src/users/users.module';
import { MongooseModule } from '@nestjs/mongoose';
import { WorkingHours, WorkingHoursSchema } from './working-hours.schema';

@Module({
    imports: [
        forwardRef(() => UsersModule),
        MongooseModule.forFeature([
            { name: WorkingHours.name, schema: WorkingHoursSchema },
        ]),
    ],
    controllers: [WorkingHoursController],
    providers: [WorkingHoursService],
    exports: [WorkingHoursService],
})
export class WorkingHoursModule {}
