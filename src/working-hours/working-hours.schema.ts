import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Type } from 'class-transformer';
import { Document, Schema as MongooseSchema, Types } from 'mongoose';
import { BookingRequest } from 'src/booking-request/booking-request.schema';
import { Workspace } from 'src/workspace/workspace.schema';

@Schema({
    timestamps: {
        createdAt: 'created_at',
        updatedAt: 'updated_at',
    },
    toJSON: {
        getters: true,
        virtuals: true,
    },
})
export class WorkingHours {
    @Prop({ required: true, type: Date })
    suitableDate: Date;

    @Prop({ required: true, type: Number })
    start_time: number;

    @Prop({ required: true, type: Number })
    end_time: number;

    @Prop({ required: true, type: Number })
    frequency: number;

    @Prop({ required: true, type: String })
    user: string;

    @Prop({ required: true, type: String })
    job: string;

    @Prop({ required: true, type: Types.ObjectId, ref: Workspace.name })
    @Type(() => Workspace)
    workspace: Workspace;
}

export type WorkingHoursDocument = WorkingHours & Document;

export const WorkingHoursSchema = SchemaFactory.createForClass(WorkingHours);
