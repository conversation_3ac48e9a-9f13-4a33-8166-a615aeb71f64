import { forwardRef, HttpException, HttpStatus, Inject, Injectable, UnauthorizedException } from '@nestjs/common';
import { SignInDto } from './dto/sign-in.dto/sign-in.dto';
import { JwtService } from '@nestjs/jwt';
import { InjectModel } from '@nestjs/mongoose';
import { Clients, ClientsDocument } from '../../clients/clients.schema';
import { Model } from 'mongoose';
import { compare, genSalt, hash } from 'bcrypt';
import { SendEmailService } from '../../send-email/send-email.service';
import { ForgotPasswordDto } from './dto/forgot-password.dto/forgot-password.dto';
import * as path from 'path';
import { TypeTemplate } from '../../@core/constants/enums';
import { ResetPasswordDto } from './dto/reset-password.dto/reset-password.dto';
import { ClientData, ClientsDataDocument } from 'src/clients-data/client-data.schema';
import { GenerateTokenDto } from './dto/generate-token.dto/generate-token.dto';
import { UsersService } from '../../users/users.service';
import { CreateUserDto } from '../../auth/Dto/CreateUserDto';
import { RolesService } from '../../roles/roles.service';

@Injectable()
export class AuthenticationService {
    constructor(
        @InjectModel(Clients.name) private clientModel: Model<ClientsDocument>,
        @InjectModel(ClientData.name)
        private clientDataModel: Model<ClientsDataDocument>,
        @Inject(forwardRef(() => JwtService))
        private jwtService: JwtService,
        @Inject(forwardRef(() => SendEmailService))
        private sendEmailService: SendEmailService,
        @Inject(forwardRef(() => UsersService))
        private usersService: UsersService,
        @Inject(forwardRef(() => RolesService))
        private rolesService: RolesService,
    ) {}

    async signIn(signInDto: SignInDto) {
        const user = await this.clientModel.aggregate([
            {
                $match: {
                    email: signInDto.email,
                },
            },
        ]);
        if (!user?.length) {
            throw new UnauthorizedException('User does not exists');
        }

        const isEqual = await compare(signInDto.password, user[0].password);

        if (!isEqual) {
            throw new UnauthorizedException('Password does not match');
        }

        const accessToken = await this.jwtService.signAsync(
            // 👈
            {
                sub: user[0]._id.toString(),
                id: user[0].Id,
                type: 'client',
                email: user[0].email,
                name: user[0].email,
            },
            {
                audience: process.env.JWT_TOKEN_AUDIENCE,
                issuer: process.env.JWT_TOKEN_ISSUER,
                secret: process.env.JWT_SECRET,
                expiresIn: parseInt(
                    process.env.JWT_ACCESS_TOKEN_TTL ?? '3600',
                    10,
                ),
            },
        );

        if (user[0].firstSignedIn === null) {
            await this.clientModel
                .findOneAndUpdate(
                    { email: signInDto.email },
                    { $set: { firstSignedIn: new Date() } },
                )
                .lean()
                .exec();

            return {
                statusCode: HttpStatus.OK,
                data: {
                    accessToken,
                },
                message: 'Login success',
            };
        }

        return {
            statusCode: HttpStatus.OK,
            data: {
                accessToken,
            },
            message: 'Login success',
        };
    }

    async forgotPassword(forgotPasswordDto: ForgotPasswordDto) {
        let user = await this.clientModel.findOne(forgotPasswordDto);

        if (!user) {
            throw new UnauthorizedException('User does not exists');
        }

        const accessToken = await this.jwtService.signAsync(
            // 👈
            {
                sub: user._id.toString(),
                id: user.Id,
                type: 'client',
                action: 'forgot-password',
                email: forgotPasswordDto.email,
            },
            {
                audience: process.env.JWT_TOKEN_AUDIENCE,
                issuer: process.env.JWT_TOKEN_ISSUER,
                secret: process.env.JWT_SECRET,
                expiresIn: parseInt(
                    process.env.JWT_ACCESS_TOKEN_TTL ?? '3600',
                    10,
                ),
            },
        );

        if (!user) {
            throw new UnauthorizedException('User does not exists');
        }
        const template = path.join(
            __dirname,
            '..',
            '..',
            '..',
            'public',
            'templates',
            'forgot-password.html',
        );

        const emailBody = await this.sendEmailService.convertParamsInTemplate(
            template,
            {
                name: forgotPasswordDto.email,
                WEB_APP_URL: process.env.WEB_APP_URL,
                accessToken: accessToken,
            },
            TypeTemplate.file,
        );

        return await this.sendEmailService.sendEmailGoogle({
            toEmail: forgotPasswordDto.email,
            subject: 'Forgot password',
            body: emailBody,
        }, (user as any)?.workspace);
    }

    async resetPassword(resetPasswordDto: ResetPasswordDto) {
        const data = this.jwtService.decode(resetPasswordDto.accessToken);

        const salt = await genSalt();
        if (!data) {
            throw new UnauthorizedException('Invalid token');
        }

        if (data['action'] !== 'forgot-password') {
            throw new UnauthorizedException('Invalid token');
        }

        return this.clientModel.findOneAndUpdate(
            {
                Id: data['id'],
            },
            {
                password: await hash(resetPasswordDto.password, salt),
            },
        );
    }

    async generateToken(generateTokenDto:GenerateTokenDto): Promise<string> {
        const user = await this.clientModel.findOne({ 'email':generateTokenDto.email })
        if (user) {
            return await this.jwtService.signAsync(
                // 👈
                {
                    sub: user._id.toString(),
                    id: user.Id,
                    type: 'client',
                    email: user.email,
                    name: user.email,
                },
                {
                    audience: process.env.JWT_TOKEN_AUDIENCE,
                    issuer: process.env.JWT_TOKEN_ISSUER,
                    secret: process.env.JWT_SECRET,
                    expiresIn: parseInt(
                        process.env.JWT_ACCESS_TOKEN_TTL ?? '3600',
                        10,
                    ),
                },
            );
        }else{
            throw new HttpException('User not found', HttpStatus.NOT_FOUND);
        }
    }

    async generateTokenForAdmin(email:string): Promise<string> {

        const user = await this.usersService.findOneByEmail(email);
        if (user) {
            return await this.jwtService.signAsync(
                // 👈
                {
                    sub: user.id,
                    id: user.id,
                    type: 'user',
                    email: email,
                },
                {
                    audience: process.env.JWT_TOKEN_AUDIENCE,
                    issuer: process.env.JWT_TOKEN_ISSUER,
                    secret: process.env.JWT_SECRET,
                    expiresIn: parseInt(
                        process.env.JWT_ACCESS_TOKEN_TTL ?? '3600',
                        10,
                    ),
                },
            );
        }else {
            throw new HttpException('User not found', HttpStatus.NOT_FOUND);
        }

    }

    async signUpAdmin(createUserDto: CreateUserDto): Promise<any> {
        const existedUser = await this.usersService.findOneByEmail(createUserDto.email);
        if (existedUser) {
            if (existedUser.userStatus === 'invited') {
                // if (!createUserDto.invitationToken || existedUser.invitationToken !== createUserDto.invitationToken) throw new HttpException('Invalid invitation token', HttpStatus.BAD_REQUEST);
                const dataUpdate = {
                    firstName: createUserDto.firstName,
                    lastName: createUserDto.lastName,
                    social: createUserDto.social,
                    password: createUserDto.password,
                    phoneNumber: createUserDto.phoneNumber,
                    address: createUserDto.address,
                    dateOfBirth: createUserDto.dateOfBirth,
                    gender: createUserDto.gender,
                    profileImage: createUserDto.profileImage,
                    isFullInformation: true,
                }
                if (createUserDto.workspace){
                    dataUpdate['workspace'] = createUserDto.workspace
                }
                await this.usersService.updateOne(existedUser.id, dataUpdate);
                return {
                    user: existedUser,
                    accessToken: await this.jwtService.signAsync(
                      // 👈
                      {
                          sub: existedUser.id,
                          id: existedUser.id,
                          type: 'user',
                          email: existedUser.email,
                      },
                      {
                          audience: process.env.JWT_TOKEN_AUDIENCE,
                          issuer: process.env.JWT_TOKEN_ISSUER,
                          secret: process.env.JWT_SECRET,
                          expiresIn: parseInt(
                            process.env.JWT_ACCESS_TOKEN_TTL ?? '3600',
                            10,
                          ),
                      },
                    )
                }
            }
            throw new HttpException('User has already existed', HttpStatus.BAD_REQUEST);
        }
        const adminRole = await this.rolesService.getParticularRole('owner');

        const user = await this.usersService.createUser({
            email: createUserDto.email,
            firstName: createUserDto.firstName,
            lastName: createUserDto.lastName,
            social: createUserDto.social,
            password: createUserDto.password,
            phoneNumber: createUserDto.phoneNumber,
            address: createUserDto.address,
            role: adminRole[0].id,
            workspace: createUserDto.workspace,
            dateOfBirth: createUserDto.dateOfBirth,
            gender: createUserDto.gender,
            profileImage: createUserDto.profileImage,
            userStatus: 'registered'
        });
        return {
            user:user,
            accessToken:  await this.jwtService.signAsync(
                // 👈
                {
                    sub: user.id,
                    id: user.id,
                    type: 'user',
                    email: user.email,
                },
                {
                    audience: process.env.JWT_TOKEN_AUDIENCE,
                    issuer: process.env.JWT_TOKEN_ISSUER,
                    secret: process.env.JWT_SECRET,
                    expiresIn: parseInt(
                        process.env.JWT_ACCESS_TOKEN_TTL ?? '3600',
                        10,
                    ),
                },
            )
        }
    }

    async signInUser(signInDto: SignInDto) {
        const user = await this.usersService.findOneByEmail(signInDto.email);
        if (!user) {
            throw new UnauthorizedException('User does not exists');
        }

        const isEqual = await compare(signInDto.password, user.password);

        if (!isEqual) {
            throw new UnauthorizedException('Password does not match');
        }

        const userReturn = {...user._doc}

        delete userReturn.password

        return {
            user: userReturn,
            accessToken:  await this.jwtService.signAsync(
                // 👈
                {
                    sub: user.id,
                    id: user.id,
                    type: 'user',
                    email: user.email,
                },
                {
                    audience: process.env.JWT_TOKEN_AUDIENCE,
                    issuer: process.env.JWT_TOKEN_ISSUER,
                    secret: process.env.JWT_SECRET,
                    expiresIn: parseInt(
                        process.env.JWT_ACCESS_TOKEN_TTL ?? '3600',
                        10,
                    ),
                },
            )
        }
    }

}
