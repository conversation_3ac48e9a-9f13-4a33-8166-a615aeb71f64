import { Body, Controller, HttpCode, HttpStatus, Post } from '@nestjs/common';
import { SignInDto } from './dto/sign-in.dto/sign-in.dto';
import { AuthenticationService } from './authentication.service';
import { ForgotPasswordDto } from './dto/forgot-password.dto/forgot-password.dto';
import { ApiTags } from '@nestjs/swagger';
import { CreateUserDto } from '../../auth/Dto/CreateUserDto';

@Controller('authentication')
@ApiTags('Authentication')
export class AuthenticationController {
  constructor(private readonly authService: AuthenticationService) {}
  @HttpCode(HttpStatus.OK) // by default @Post does 201, we wanted 200 - hence using @HttpCode(HttpStatus.OK)
  @Post('client/sign-in')
  clientSignIn(@Body() signInDto: SignInDto) {
    return this.authService.signIn(signInDto);
  }

  @HttpCode(HttpStatus.OK)
  @Post('user/sign-up')
  async userSignUp(@Body() createUserDto: CreateUserDto) {
    const user = await this.authService.signUpAdmin(createUserDto);
    return {
      statusCode: HttpStatus.OK,
      message: 'Register successfully!',
      data: {
        ...user
      },
    };
  }


  @HttpCode(HttpStatus.OK)
  @Post('user/sign-in')
  async userSignIn(@Body() signInDto: SignInDto) {
    const user = await this.authService.signInUser(signInDto);
    return {
      statusCode: HttpStatus.OK,
      message: 'Register successfully!',
      data: {
        ...user
      },
    };
  }


  @HttpCode(HttpStatus.OK)
  @Post('client/forgot-password')
  clientForgotPassword(@Body() forgotPasswordDto: ForgotPasswordDto) {
    const sendEmail = this.authService.forgotPassword(forgotPasswordDto);
    return {
      statusCode: HttpStatus.OK,
      message: 'Sent email reset password!',
      data: sendEmail,
    };
  }

  @HttpCode(HttpStatus.OK)
  @Post('client/reset-password')
  clientResetPassword(@Body() resetPasswordDto: any) {
    const user = this.authService.resetPassword(resetPasswordDto);
    return {
      statusCode: HttpStatus.OK,
      message: 'Reset password success!',
      data: user,
    };
  }
}
