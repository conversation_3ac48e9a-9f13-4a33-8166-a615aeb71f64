import { Body, Controller, HttpCode, HttpStatus, Post } from '@nestjs/common';
import { SocialAuthenticationService } from './social-authentication.service';
import { CreateUserSocialDto } from '../../../auth/Dto/CreateUserDto';
import { SocialLoginDTO } from "../../../auth/Dto/SocialLoginDTO";

@Controller('social-authentication')
export class SocialAuthenticationController {
    constructor(private readonly socialAuthenticationService:SocialAuthenticationService) {}

    @HttpCode(HttpStatus.OK)
    @Post('client/sign-in')
    clientSignIn(
        @Body('token') token: string,
    ) {
        const accessToken =  this.socialAuthenticationService.authenticateClientSignIn(token)

        return {
            statusCode: HttpStatus.OK,
            message : 'Sign in successfully',
            data: {
                accessToken
            }
        }
    }

    @HttpCode(HttpStatus.OK)
    @Post('user/sign-up')
    async userSignUp(
        @Body() createUserDto: CreateUserSocialDto
    ) {
        const accessToken = await this.socialAuthenticationService.authenticateUserSignUp(createUserDto);
        return {
            statusCode: HttpStatus.OK,
            message: 'Register successfully!',
            data: {
                ...accessToken
            },
        };
    }

    @HttpCode(HttpStatus.OK)
    @Post('user/sign-in')
    async userSignIn(
        @Body() loginDTO: SocialLoginDTO,
    ){
        const accessToken = await this.socialAuthenticationService.authenticateUserSignIn(loginDTO);
        return {
            statusCode: HttpStatus.OK,
            message: 'Sign in successfully!',
            data: {
                ...accessToken
            },
        };
    }

}
