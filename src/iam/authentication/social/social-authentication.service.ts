import { forwardRef, HttpException, HttpStatus, Inject, Injectable, OnModuleInit } from "@nestjs/common";
import { OAuth2Client } from "google-auth-library";
import { UsersService } from "../../../users/users.service";
import { AuthenticationService } from "../authentication.service";
import { CreateUserSocialDto } from "../../../auth/Dto/CreateUserDto";
import { UserSocial } from "../../../users/users.schema";
import { RolesService } from "../../../roles/roles.service";
import { SocialLoginDTO } from "../../../auth/Dto/SocialLoginDTO";
import { getAuth } from "firebase-admin/auth";

@Injectable()
export class SocialAuthenticationService implements OnModuleInit{
    private oauthClient: OAuth2Client;

    constructor(
        @Inject(forwardRef(() => UsersService))
        private usersService: UsersService,
        @Inject(forwardRef(() => AuthenticationService))
        private authenticationService: AuthenticationService,
        @Inject(forwardRef(() => RolesService))
        private rolesService: RolesService,
    ) {}

    onModuleInit() {
        const clientId = process.env.GOOGLE_CLIENT_ID;
        const clientSecret = process.env.GOOGLE_CLIENT_SECRET;
        this.oauthClient = new OAuth2Client(clientId, clientSecret);
    }

    async authenticate(token: string) {

        const ticket = await this.oauthClient.verifyIdToken({
            idToken: token,
            audience: process.env.GOOGLE_CLIENT_ID,
        });
        return ticket.getPayload()
    }

    async authenticateFirebase(token: string) {
        const fbAuth = getAuth();
        return await fbAuth.verifyIdToken(token);
    }

    async authenticateClientSignIn(token: string,) {
        const {email} = await this.authenticate(token);
        return this.authenticationService.generateToken({email});
    }

    async authenticateUserSignIn(loginDTO: SocialLoginDTO) {
        const { token, social = 'google', deviceToken, deviceType } = loginDTO;
        let email = '';
        let phoneNumber = '';

        let signInProvider = '';

        if (social === 'google') {
          const googleCred = await this.authenticate(token);
          email = googleCred.email;
        }
        if (social === 'firebase') {
          const firebaseCred = await this.authenticateFirebase(token);
          email = firebaseCred.email;
          phoneNumber = firebaseCred.phone_number?.startsWith('+') ? firebaseCred.phone_number.slice(1) : firebaseCred.phone_number;
          signInProvider = firebaseCred.firebase.sign_in_provider
        }

        let user: any;
        if (signInProvider === 'phone' && phoneNumber) {
            user = await this.usersService.findOneByPhoneNumber(phoneNumber);
        }
        else {
            user = await this.usersService.findOneByEmail(email);
        }
        if (!user) {
            throw new HttpException('User not found', HttpStatus.NOT_FOUND);
        }

        if (deviceToken && deviceType) {
            await this.usersService.updateOne(user._id, {
                deviceType,
                deviceToken,
            });
        }

        return {
            user,
            accessToken: await this.authenticationService.generateTokenForAdmin(user.email)
        };
    }

    async authenticateUserSignUp(createUserDto:CreateUserSocialDto) {
        const adminRole = await this.rolesService.getParticularRole('owner')
        switch (createUserDto.social) {
            case UserSocial.GOOGLE:
                const {email,given_name,family_name} = await this.authenticate(createUserDto.token);
                const user = await this.usersService.findOneByEmail(email);
                if(user) {
                    if (user.userStatus !== "invited") throw new HttpException("User already exists", HttpStatus.BAD_REQUEST);
                    // if (!createUserDto.invitationToken || createUserDto.invitationToken !== user.invitationToken) throw new HttpException("Invalid invitation token", HttpStatus.BAD_REQUEST);
                    await this.usersService.updateOne(user._id, {
                        userStatus: "registered",
                        firstName: given_name,
                        lastName: family_name,
                        phoneNumber: createUserDto.phoneNumber,
                        address: createUserDto.address,
                        workspace: createUserDto.workspace,
                        social: UserSocial.GOOGLE,
                        isFullInformation: true
                    });
                    return {
                        user,
                        accessToken: await this.authenticationService.generateTokenForAdmin(email)
                    }
                }
                const newUser = await this.usersService.createUser({
                    email,
                    firstName: given_name,
                    lastName: family_name,
                    social: UserSocial.GOOGLE,
                    phoneNumber: createUserDto.phoneNumber,
                    address: createUserDto.address,
                    role: adminRole[0].id,
                    workspace: createUserDto.workspace,
                    userStatus: 'registered'
                });

                return {
                    user: newUser,
                    accessToken: await this.authenticationService.generateTokenForAdmin(email)
                }
            default:
                return false
        }
    }
}