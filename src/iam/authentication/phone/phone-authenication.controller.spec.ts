import { Test, TestingModule } from '@nestjs/testing';
import { PhoneAuthenticationController } from './phone-authentication.controller';

describe('PhoneAuthenticationController', () => {
  let controller: PhoneAuthenticationController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [PhoneAuthenticationController],
    }).compile();

    controller = module.get<PhoneAuthenticationController>(PhoneAuthenticationController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
