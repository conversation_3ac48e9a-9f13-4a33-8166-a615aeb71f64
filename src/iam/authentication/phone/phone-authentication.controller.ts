import { Body, Controller, HttpCode, HttpStatus, Post } from '@nestjs/common';
import { PhoneAuthenticationService } from './phone-authentication.service';

@Controller('phone-authentication')
export class PhoneAuthenticationController {
    constructor(
        private readonly phoneAuthenticationService: PhoneAuthenticationService,
    ) {}

    // Add methods here
    @Post('send-otp')
    @HttpCode(HttpStatus.OK)
    async sendOTP(
        @Body('phone') phone: string,
    ) {
        return await this.phoneAuthenticationService.sendOTP(phone);
    }

    @Post('verify-otp')
    @HttpCode(HttpStatus.OK)
    async verifyOTP(
        @Body('phone') phone: string,
        @Body('otp') otp: number,
    ){
        return await this.phoneAuthenticationService.verifyUserSignIn(phone, otp);
    }
}
