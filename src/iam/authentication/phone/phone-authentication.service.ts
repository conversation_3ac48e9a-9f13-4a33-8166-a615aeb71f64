import { forwardRef, HttpException, HttpStatus, Inject, Injectable, OnModuleInit } from '@nestjs/common';
import { UsersService } from '../../../users/users.service';
import { AuthenticationService } from '../authentication.service';
import { RolesService } from '../../../roles/roles.service';
// import { Twilio } from 'twilio';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';

@Injectable()
export class PhoneAuthenticationService implements OnModuleInit{
    private twilioSID: string;
    private twilioAuthToken: string;
    // private client: Twilio;

    constructor(
        @Inject(forwardRef(() => UsersService))
        private usersService: UsersService,
        @Inject(forwardRef(() => AuthenticationService))
        private authenticationService: AuthenticationService,
        @Inject(forwardRef(() => RolesService))
        private rolesService: RolesService,
        @Inject(CACHE_MANAGER) private cacheManager: Cache
    ) {}

    onModuleInit() {
        this.twilioAuthToken = process.env.TWILIO_AUTH_TOKEN;
        this.twilioSID = process.env.TWILIO_SID;
        // this.client = new Twilio(this.twilioSID,this.twilioAuthToken);
    }

    async generateOTP(phone: string) {
        const otp = Math.floor(100000 + Math.random() * 900000);
        await this.cacheManager.set(phone, otp);
        return otp;
    }

    async verifyOTP(phone: string, otp: number) {
        const cachedOTP = await this.cacheManager.get(phone);
        return cachedOTP === otp;
    }

   async sendOTP(phone: string) {
        let otp = await this.cacheManager.get(phone);

        const user = await this.usersService.findOneByPhone(phone);
        if (!otp) {
            otp = await this.generateOTP(phone);
        }

        if (!user) {
            throw new HttpException('User not found', HttpStatus.NOT_FOUND);
        }
        // TODO: Need to check with Jimmy about the key
        // return this.client.messages.create({
        //     body: `Your MaidProfit verification code is ${otp}. Please use this code to complete your verification process.`,
        //     from: 'MaidProfit',
        //     to: phone
        // })
   }

   async verifyUserSignIn(phone: string, otp: number) {
        const isVerified = await this.verifyOTP(phone, otp);
        if (!isVerified) {
            throw new Error('Invalid OTP');
        }
        return this.authenticateUserSignIn(phone);
   }

   async authenticateUserSignIn(phone: string) {
        const user = await this.usersService.findOneByPhone(phone);
        return {
            user,
            accessToken: await this.authenticationService.generateTokenForAdmin(user.email)
        };
    }


}

