import { Module, forwardRef } from '@nestjs/common';
import { AuthenticationController } from './authentication/authentication.controller';
import { AuthenticationService } from './authentication/authentication.service';
import { JwtModule } from '@nestjs/jwt';
import { MongooseModule } from '@nestjs/mongoose';
import { Clients, ClientsSchema } from '../clients/clients.schema';
import { SendEmailModule } from '../send-email/send-email.module';
import {
    ClientData,
    ClientsDataSchema,
} from 'src/clients-data/client-data.schema';
import { UsersModule } from '../users/users.module';
import { SocialAuthenticationService } from './authentication/social/social-authentication.service';
import { SocialAuthenticationController } from './authentication/social/social-authentication.controller';
import { RolesModule } from '../roles/roles.module';
import { PhoneAuthenticationController } from './authentication/phone/phone-authentication.controller';
import { PhoneAuthenticationService } from './authentication/phone/phone-authentication.service';
import { CacheModule } from '@nestjs/cache-manager';

@Module({
    imports: [
        MongooseModule.forFeature([
            { name: Clients.name, schema: ClientsSchema },
            { name: ClientData.name, schema: ClientsDataSchema },
        ]),
        JwtModule.register({
            global: true,
            secret: process.env.JWT_SECRET,
            signOptions: { expiresIn: '60s' },
        }),
        forwardRef(() => SendEmailModule),
        forwardRef(() => UsersModule),
        forwardRef(() => RolesModule),
        forwardRef(() => CacheModule.register({
                isGlobal: true,
        })),
    ],
    exports: [AuthenticationService],
    providers: [AuthenticationService, SocialAuthenticationService,PhoneAuthenticationService],
    controllers: [AuthenticationController, SocialAuthenticationController, PhoneAuthenticationController],
})
export class IamModule {}
