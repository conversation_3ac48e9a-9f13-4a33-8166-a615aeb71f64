import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema, Types } from "mongoose";
import { Workspace } from "../workspace/workspace.schema";
import { Type } from "class-transformer";

export enum STATUS {
    active = 'active',
    inactive = 'inactive'
}

export enum UNIT {
    percent = 'percent',
    amount = 'amount'
}

@Schema()
export class Discount {
    _id: MongooseSchema.Types.ObjectId;

    @Prop({ required: true})
    name: string;

    @Prop({ required: true})
    value: number;

    @Prop({ default: UNIT.percent, enum: UNIT})
    unit: string;

    @Prop({ default: STATUS.active, enum: STATUS })
    status: string;

    @Prop({ required: true })
    expiredDate: Date;

    @Prop({ required: true })
    dateCreated: Date;

    @Prop({ required: true, type: Types.ObjectId, ref: Workspace.name })
    @Type(() => Workspace)
    workspace: Workspace;
}

export type DiscountDocument = Discount & Document;

export const DiscountSchema = SchemaFactory.createForClass(Discount)