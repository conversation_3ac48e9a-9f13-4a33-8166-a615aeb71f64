import { Module, forwardRef } from '@nestjs/common';
import { DiscountService } from './discount.service';
import { DiscountController } from './discount.controller';
import { MongooseModule } from '@nestjs/mongoose';
import { Discount, DiscountSchema } from './discount.schema';
import { UsersModule } from '../users/users.module';
import { ClientsModule } from '../clients/clients.module';

@Module({
    imports: [
        forwardRef(() => UsersModule),
        forwardRef(() => ClientsModule),
        MongooseModule.forFeature([
            { name: Discount.name, schema: DiscountSchema },
        ]),
    ],
    providers: [DiscountService],
    exports: [DiscountService],
    controllers: [DiscountController],
})
export class DiscountModule {}
