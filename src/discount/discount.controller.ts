import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpException,
  HttpStatus,
  Param,
  Post,
  Query, Req,
  UseGuards,
} from '@nestjs/common';
import { DiscountService } from './discount.service';
import {
  CollectionDto,
  CollectionResponse,
} from '@forlagshuset/nestjs-mongoose-paginate';
import { Discount } from './discount.schema';
import CreateDiscountDto from './dto/CreateDiscountDto';
import UpdateDiscountDto from './dto/UpdateDiscountDto';
import { Patch } from '@nestjs/common/decorators/http/request-mapping.decorator';
import mongoose from 'mongoose';
import { AuthGuard } from '../auth/auth.guard';
import { ACGuard, UseRoles } from 'nest-access-control';
import ROLE_RESOURCES from '../@core/constants/roles.resources';
import { ApiTags } from '@nestjs/swagger';

@Controller(ROLE_RESOURCES.DISCOUNT)
@ApiTags('Discount')
export class DiscountController {
  constructor(private discountService: DiscountService) {}

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.DISCOUNT,
    action: 'read',
    possession: 'any',
  })
  @Get('/')
  async getDiscount(
    @Query()
    collectionDto: CollectionDto,
    @Req() req: Request,
  ): Promise<CollectionResponse<Discount>> {
    //@ts-ignore
    let workspace = req.user.workspace;
    //@ts-ignore
    collectionDto = {
        ...collectionDto,
      //@ts-ignore
        workspace: {
          $eq: workspace,
        },
    };
    return this.discountService.findAll(collectionDto);
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.DISCOUNT,
    action: 'read',
    possession: 'any',
  })
  @Get(':id')
  async getDiscountById(
    @Req() req: Request,
    @Param('id') id: string,
  ) {
    //@ts-ignore
    let workspace = req.user.workspace;
    let discount = await this.discountService.findOne(id, workspace);
    if (!discount) {
      throw new HttpException(
        'Data discount not existed',
        HttpStatus.BAD_REQUEST,
      );
    }
    return {
      statusCode: HttpStatus.OK,
      message: 'Get discount by Id success',
      data: discount,
    };
  }


  @HttpCode(HttpStatus.OK)
  @Get('/code/:code')
  async getDiscountByCode(
    @Param('code') code: string,
    @Query('workspace') workspace: string,
  ) {
    let discount = await this.discountService.findByCode(code,workspace);
    if (!discount) {
      throw new HttpException(
        'Data discount not existed',
        HttpStatus.BAD_REQUEST,
      );
    }
    return {
      statusCode: HttpStatus.OK,
      message: 'Get discount by code success',
      data: discount,
    };
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.DISCOUNT,
    action: 'create',
    possession: 'any',
  })
  @Post('/create')
  async createDiscount(
      @Req() req: Request,
      @Body() createDiscountDto: CreateDiscountDto
  ) {
    //@ts-ignore
    let workspace = req.user.workspace;
    let discountCreated = await this.discountService.create(createDiscountDto,workspace);
    if (!discountCreated) {
      throw new HttpException(
        'Can not create discount',
        HttpStatus.BAD_REQUEST,
      );
    }

    return {
      statusCode: HttpStatus.OK,
      message: 'Create discount successfully',
      data: discountCreated,
    };
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.DISCOUNT,
    action: 'update',
    possession: 'any',
  })
  @Patch(':id')
  async updateDiscount(
    @Req() req: Request,
    @Param('id') id: string,
    @Body() updateDiscountDto: UpdateDiscountDto,
  ) {
    //@ts-ignore
    let workspace = req.user.workspace;
    let discountUpdated = await this.discountService.update(
      id,
      updateDiscountDto,
      workspace
    );
    if (!discountUpdated) {
      throw new HttpException(
        'Can not update Discount',
        HttpStatus.BAD_REQUEST,
      );
    }

    return {
      statusCode: HttpStatus.OK,
      message: 'Update discount item successfully',
      data: discountUpdated,
    };
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.DISCOUNT,
    action: 'delete',
    possession: 'any',
  })
  @Delete(':id')
  async deleteDiscount(
    @Req() req: Request,
    @Param('id') id: string,
  ) {
    //@ts-ignore
    let workspace = req.user.workspace;
    let discountDeleted = await this.discountService.delete(id, workspace);
    if (!discountDeleted) {
      throw new HttpException(
        'Can not delete Discount',
        HttpStatus.BAD_REQUEST,
      );
    }

    return {
      statusCode: HttpStatus.OK,
      message: 'Delete discount item successfully',
      data: discountDeleted,
    };
  }
}
