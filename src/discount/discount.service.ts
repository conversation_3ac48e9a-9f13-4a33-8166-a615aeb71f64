import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import mongoose, { Model } from 'mongoose';
import { Discount, DiscountDocument } from './discount.schema';
import { CollectionDto, DocumentCollector } from '@forlagshuset/nestjs-mongoose-paginate';
import CreateDiscountDto from './dto/CreateDiscountDto';
import UpdateDiscountDto from './dto/UpdateDiscountDto';

@Injectable()
export class DiscountService {
    constructor(
        @InjectModel(Discount.name) private discountModel: Model<DiscountDocument>,
    ) {}

    async findAll(collectionDto: CollectionDto): Promise<any> {
        const collector = new DocumentCollector<DiscountDocument>(this.discountModel)
        return collector.find(collectionDto)
    }

    async findOne(id: string, workspace: string): Promise<Discount> {
        let discount = await this.discountModel.findOne({
            _id: new mongoose.Types.ObjectId(id),
            workspace: new mongoose.Types.ObjectId(workspace)
        })
        if(!discount) {
            throw new Error('Data discount not existed')
        }
        return discount
    }

    async findByCode(code: string, workspace: string): Promise<Discount> {
        return this.discountModel.findOne({
            name: code,
            workspace: new mongoose.Types.ObjectId(workspace)
        });
    }

    async create(createDiscountDto: CreateDiscountDto,workspace:string): Promise<Discount> {
        const checkExist = await this.discountModel.find({
            name: createDiscountDto.name,
            workspace: new mongoose.Types.ObjectId(workspace)
        })
        if(checkExist?.length) {
            throw new HttpException('Data Discount existed', HttpStatus.CONFLICT)
        }

        return await this.discountModel.create({
            name: createDiscountDto.name,
            value: createDiscountDto.value,
            unit: createDiscountDto.unit,
            status: createDiscountDto.status,
            expiredDate: new Date(createDiscountDto.expiredDate),
            workspace: new mongoose.Types.ObjectId(workspace),
            dateCreated: new Date()
        })
    }

    async update(id: string, updateDiscountDto: UpdateDiscountDto, workspace:string): Promise<Discount> {
        const checkExist = await this.discountModel.findById(id).exec()
        if (!checkExist) {
          throw new HttpException('Data Discount not existed', HttpStatus.CONFLICT);
        }

        return this.discountModel.findByIdAndUpdate(id, {
            ...updateDiscountDto,
            workspace: new mongoose.Types.ObjectId(workspace),
        })
    }

    async delete(id: string,workspace:string): Promise<Discount> {
        return this.discountModel.findOneAndRemove({
            _id: new mongoose.Types.ObjectId(id),
            workspace: new mongoose.Types.ObjectId(workspace)
        })
    }
}
