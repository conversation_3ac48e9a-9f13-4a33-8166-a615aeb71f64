import { ApiProperty } from "@nestjs/swagger";
import { IsDate, IsEnum, IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator';
import { STATUS, UNIT } from '../discount.schema';

export default class CreateDiscountDto {
    @ApiProperty()
    @IsNotEmpty()
    @IsString()
    readonly name: string;

    @ApiProperty()
    @IsNotEmpty()
    @IsNumber()
    readonly value: number;

    @ApiProperty()
    @IsNotEmpty()
    @IsDate()
    readonly expiredDate: Date;

    @ApiProperty()
    @IsOptional()
    @IsEnum(UNIT)
    readonly unit: string;

    @ApiProperty()
    @IsOptional()
    @IsEnum(STATUS)
    readonly status: string;
}