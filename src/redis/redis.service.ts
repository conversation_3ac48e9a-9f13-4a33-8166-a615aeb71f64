import { Inject, Injectable } from '@nestjs/common';
import * as Redis from 'ioredis';
import { RedisClient, RedisClientError } from './redis-client.provider';
import { REDIS_CLIENT } from './redis.constants';

@Injectable()
export class RedisService {
  constructor(@Inject(REDIS_CLIENT) public readonly redisClient: RedisClient) {}

  getClient(name?: string): Redis.Redis {
    if (!name) {
      name = this.redisClient.defaultKey;
    }
    if (!this.redisClient.clients.has(name)) {
      throw new RedisClientError(`client ${name} does not exist`);
    }
    return this.redisClient.clients.get(name);
  }

  getClients(): Map<string, Redis.Redis> {
    return this.redisClient.clients;
  }
}
