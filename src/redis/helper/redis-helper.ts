import { Injectable } from "@nestjs/common";
import { RedisService } from "../redis.service";
import { Redis } from "ioredis";

@Injectable()
export class RedisHelper {
    constructor(
        private readonly redisService: RedisService,
    ) {
        this.redisClient = this.redisService.getClient('COMMON_CACHE_NAME');
    }
    private redisClient: Redis;

    private async clearCache(key: string) {
        try {
            const keys = await this.redisClient.keys(key);
            if (keys.length > 0) {
                await this.redisClient.del(keys);
            }
            return keys;
        } catch (error) {
            console.log(error);
            throw error;
        }
    }
}