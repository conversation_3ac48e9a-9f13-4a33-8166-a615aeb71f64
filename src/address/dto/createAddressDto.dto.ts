import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty, IsOptional, IsString } from "class-validator";

export default class CreateAddressDto{

    @ApiProperty()
    @IsNotEmpty()
    @IsString()
    readonly street_one: string;

    @ApiProperty()
    @IsOptional()
    readonly street_two?: string;

    @ApiProperty()
    @IsNotEmpty()
    @IsString()
    readonly city: string;

    @ApiProperty()
    @IsNotEmpty()
    @IsString()
    readonly state: string;

    @ApiProperty()
    @IsOptional()
    readonly zip?: string;

    @ApiProperty()
    @IsNotEmpty()
    readonly coordinates: [number, number];
}