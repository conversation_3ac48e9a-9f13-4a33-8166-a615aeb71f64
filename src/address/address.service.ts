import { HttpException, HttpStatus, Injectable } from "@nestjs/common";
import { Address } from "./address.schema";
import { Model } from "mongoose";
import { InjectModel } from "@nestjs/mongoose";
import CreateAddressDto from "./dto/createAddressDto.dto";

@Injectable()
export class AddressService {

  constructor(
    @InjectModel(Address.name) private addressModel: Model<Address>,
  ) {}

  async create(data: CreateAddressDto): Promise<Address> {
    const checkExist = await this.addressModel.findOne({
      street_one: data.street_one,
      city: data.city,
      state: data.state
    });

    if(checkExist) {
      return checkExist;
    }

    const newAddress = new this.addressModel(data);
    return newAddress.save();
  }

  async getAddress(id: string): Promise<any> {
    const address = await this.addressModel.findById(id);
    if (!address) {
      throw new HttpException('Address not found', HttpStatus.NOT_FOUND);
    }
    return address;
  }

  async getAddressByStreet(address: {street_one:string,city:string,state:string}): Promise<Address> {
    return this.addressModel.findOne({
      street_one: address.street_one,
      city: address.city,
      state: address.state
    });
  }
}
