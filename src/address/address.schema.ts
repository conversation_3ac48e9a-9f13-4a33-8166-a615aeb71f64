import { <PERSON><PERSON>, <PERSON>hem<PERSON>, SchemaFactory } from "@nestjs/mongoose";
import { Document, Schema as MongooseSchema } from "mongoose";

@Schema()

export class Address extends Document{

  _id: MongooseSchema.Types.ObjectId;

  @Prop({ required: true })
  street_one: string;

  @Prop()
  street_two?: string;

  @Prop({ required: true })
  city: string;

  @Prop({ required: true })
  state: string;

  @Prop()
  zip?: string;

  @Prop({
    type: [Number], // an array of two numbers
    required: true,
    index: '2dsphere' // ensure the data in this field is indexed with a 2dsphere index
  })
  coordinates: [number, number];
}

export const AddressSchema = SchemaFactory.createForClass(Address);