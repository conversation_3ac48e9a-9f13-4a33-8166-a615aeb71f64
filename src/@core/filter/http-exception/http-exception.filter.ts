import { ArgumentsHost, Catch, ExceptionFilter, HttpException } from "@nestjs/common";

@Catch(HttpException)
export class HttpExceptionFilter<T extends HttpException> implements ExceptionFilter {
  catch(exception: T, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse();

    const status = exception.getStatus();
    const exceptionResponse = exception.getResponse();

    const error = typeof exceptionResponse === 'string' ? { message: exceptionResponse } : (exceptionResponse as object);
    response.status(status).json({
      statusCode: status,
      error,
      ...(error instanceof Object && Object.keys(error).length > 0 && { message: exception.name }),
      timestamp: new Date().toISOString(),
    })
  }
}
