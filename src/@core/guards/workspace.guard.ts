import { Injectable, CanActivate, ExecutionContext, HttpException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import * as jwt from 'jsonwebtoken';
import { UsersService } from '../../users/users.service';
import { ClientsService } from '../../clients/clients.service';
import { IS_PUBLIC_KEY } from '../decorators/public.decorator';

@Injectable()
export class WorkspaceGuard implements CanActivate {
    constructor(
        private readonly usersService: UsersService,
        private readonly clientService: ClientsService,
        private readonly reflector: Reflector,
    ) {}

    async canActivate(context: ExecutionContext): Promise<boolean> {
        const isPublic = this.reflector.getAllAndOverride<boolean>(IS_PUBLIC_KEY, [
            context.getHandler(),
            context.getClass(),
        ]);

        if (isPublic) {
            return true;
        }

        const request = context.switchToHttp().getRequest();
        const response = context.switchToHttp().getResponse();

        const publicRoutes = [
            !request.originalUrl.startsWith('/auths/profile'),
            !request.originalUrl.startsWith('/issues'),
            !request.originalUrl.startsWith('/booking-time-arrival'),
            !request.originalUrl.startsWith('/booking-frequency'),
            !request.originalUrl.startsWith('/booking-extra'),
            !request.originalUrl.startsWith('/booking-addon'),
            !request.originalUrl.startsWith('/booking-types'),
            !request.originalUrl.startsWith('/booking-request'),
            !request.originalUrl.startsWith('/notification'),
            !request.originalUrl.startsWith('/quotes/approve-reject'),
            !request.originalUrl.startsWith('/quotes/create-job-by-quote'),
        ];

        const token = request.headers['authorization'];
        
        if (!token && !publicRoutes.some((item) => item === false)) {
            throw new HttpException('Unauthorized', 401);
        }

        if (!request.body?.['workspace'] && token) {
            const tokenSession = jwt.decode(token);
            if (!tokenSession?.['sub']) {
                throw new HttpException('Unauthorized', 401);
            }

            let user: { [x: string]: { toString: () => any } };
            if (tokenSession?.['type'] === 'client') {
                user = await this.clientService.getClient(
                    tokenSession['id'].toString(),
                );
            } else {
                user = await this.usersService.findOne(
                    tokenSession['sub'].toString(),
                );
            }
            
            if (!user) {
                throw new HttpException('User not found', 401);
            }
            
            request.body['workspace'] = user['workspace'].toString();
        }

        return true;
    }
}