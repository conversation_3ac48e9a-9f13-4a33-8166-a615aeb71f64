// raw-body.middleware.ts
import { Request, Response, NextFunction } from 'express';

// Extend the Request interface to include rawBody
interface CustomRequest extends Request {
  rawBody?: string;
}

export function rawBodyMiddleware(req: CustomRequest, res: Response, next: NextFunction) {
  req.setEncoding('utf8');
  req.rawBody = '';
  req.on('data', chunk => {
    req.rawBody += chunk;
  });
  req.on('end', () => {
    next();
  });
}