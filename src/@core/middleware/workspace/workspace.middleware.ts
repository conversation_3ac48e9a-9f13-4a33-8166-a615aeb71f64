import { Injectable, NestMiddleware, HttpException } from '@nestjs/common';
import * as jwt from 'jsonwebtoken';
import { UsersService } from '../../../users/users.service';
import { ClientsService } from '../../../clients/clients.service';
@Injectable()
export class WorkspaceMiddleware implements NestMiddleware {
    constructor(
        private readonly UsersService: UsersService,
        private readonly ClientService: ClientsService,
    ) {}

    async use(req: any, res: any, next: () => void) {
        // Check if this is a public external API endpoint
        if (req.originalUrl.includes('/external/')) {
            return next();
        }

        const publicRoutes = [
            // !req.originalUrl.startsWith('/workspace'),
            !req.originalUrl.startsWith('/auths/profile'),
            !req.originalUrl.startsWith('/issues'),
            !req.originalUrl.startsWith('/booking-time-arrival'),
            !req.originalUrl.startsWith('/booking-frequency'),
            !req.originalUrl.startsWith('/booking-extra'),
            !req.originalUrl.startsWith('/booking-addon'),
            !req.originalUrl.startsWith('/booking-types'),
            !req.originalUrl.startsWith('/booking-request'),
            !req.originalUrl.startsWith('/notification'),
            !req.originalUrl.startsWith('/quotes/approve-reject'),
            !req.originalUrl.startsWith('/quotes/create-job-by-quote'),
        ];

        const token = req.headers['authorization'];
        console.log(token);
        if (!token && !publicRoutes.some((item) => item === false)) {

            throw new HttpException('Unauthorized', 401);
        }
        if (!req.body?.['workspace'] && token) {
            const tokenSession = jwt.decode(token);
            if (!tokenSession?.['sub']) {
                throw new HttpException('Unauthorized', 401);
            }

            let user: { [x: string]: { toString: () => any } };
            if (tokenSession?.['type'] === 'client') {
                user = await this.ClientService.getClient(
                    tokenSession['id'].toString(),
                );
            } else {
                user = await this.UsersService.findOne(
                    tokenSession['sub'].toString(),
                );
            }
            if (!user) {
                throw new HttpException('User not found', 401);
            }
            req.body['workspace'] = user['workspace'].toString();
        }

        next();
    }
}
