<!doctype html>
<html ⚡4email data-css-strict>
<head>
    <meta charset="utf-8">
    <script async src="https://cdn.ampproject.org/v0.js"></script>
    <script async custom-element="amp-carousel" src="https://cdn.ampproject.org/v0/amp-carousel-0.1.js"></script>
    <script async custom-element="amp-bind" src="https://cdn.ampproject.org/v0/amp-bind-0.1.js"></script>
    <script async custom-element="amp-form" src="https://cdn.ampproject.org/v0/amp-form-0.1.js"></script>
    <style amp4email-boilerplate>body{visibility:hidden}</style>

    <style amp-custom>
        body {
            padding: 50px;
            font-family: "Roboto", sans-serif;
            font-weight: 100;
            font-style: normal;
        }
        .container{
            max-width: 1140px;
            margin: 0 auto
        }
        h1 {
            font-size: 45px;
            margin: 20px 0;
            word-wrap: break-word;
        }
        .isssed{
            margin: 10px 0;
        }
        .line{
            height: 1px;
            width: 100%;
            background: #000;
            opacity: 0.2;
            margin: 25px 0;
        }
        .addressBill{
            display: grid;
            grid-template-columns: repeat(2, minmax(0, 1fr));
            gap: 4px;
            min-height: 100px
        }
        .subTitle{
            font-size: 17px;
            font-weight: 600
        }
        .value{
            color: #9e5408;
            font-size: 16px;
        }
        table , thead , tbody, tr{
            width: 100%
        }
        .headingTable{
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }
        .contentItem{
            display: flex;
            flex-wrap: wrap;
            align-items: center
        }
        .bgGray{
            background: #f1f1f1;
        }
        .valueTable{
            width: 60%;
            flex: 0 0 60%;
            max-width: 60%;
            padding: 10px;
            box-sizing: border-box;
            margin: 0;
            font-size: 16px;
            text-align: left
        }
        .valueTotal{
            text-align: right;
            width: 40%;
            flex: 0 0 40%;
            max-width: 40%;
            padding: 10px;
            box-sizing: border-box;
            margin: 0;
        }
        .amountDue{
            text-align: right;
            padding-right: 10px;
            font-size: 18px;
            margin-top: 10px;
        }
        .titleAmount{
            font-weight: 600
        }
        .valueAmount{
            color: #c1121f
        }
        .buttonStyle{
            display: flex;
            margin-top: 20px;
        }
        .containerBtn{
            display: flex;
            margin: 0 auto;
            justify-content: center;
        }
        a.button{
            display: block;
            text-align: center;
            width: 100px;
            padding: 10px 15px;
            color: white;
            font-weight: 600;
            cursor: pointer;
            border: none;
            border-radius: 5px;
            margin: 0 15px;
            text-decoration: none;
        }
        .bgGreen{
            background: #2EC68B
        }
        .buttonRed{
            background: #E93F4C
        }

        .contauineRousel{
            width	: 100%;
        }
        .ampCarousel{
            height : 100%;
        }
        .imageStyle img{
            object-fit: cover
        }


        @media only screen and (max-width: 800px){
            h1 {
                font-size: 35px;
                box-sizing: border-box;
            }
            .contauineRousel{
                height : 100%;
            }

        }

        @media only screen and (max-width: 600px) {
            body {
                padding: 25px;
            }
            .container-image{
                display: flex;
                justify-content: center
            }
            h1 {
                font-size: 25px;
                box-sizing: border-box;
            }
            .addressBill{
                display: grid;
                grid-template-columns: repeat(1, minmax(0, 1fr));
                gap: 8px;
            }
            .subTitle{
                font-size: 16px;
                margin-bottom: 8px;
            }
            .contentItem{
                font-size: 14px
            }
            .valueTable{
                width: 70%;
                flex: 0 0 70%;
                max-width: 70%;
            }
            .valueTotal{
                text-align: right;
                width: 30%;
                flex: 0 0 30%;
                max-width: 30%;
            }
            .containerBtn{
                width: 100%
            }
            button{
                padding: 8px 12px;
            }
            .contauineRousel{
                height : 100%;
            }
        }

    </style>
</head>
<body>
<div class="container">
    <div class="container-image">
        <img
            src="{{logo}}"
            alt="logo"/>
    </div>
    <div>
        <h2>
            QUOTE #<span>{{quote_id}}</span>
        </h2>
    </div>
    <div class="line"></div>
    <div class="addressBill">
        <div>
            <span class="subTitle">ISSUED: </span> <span class="value">{{quote_issued}}</span>
        </div>
        <div>
            <span class="subTitle">QUOTE TO: </span> <span class="value">{{name}}</span><br/>
            <span class="value">{{address}}</span><br/>
            <span class="value">{{city}}, {{state}}, {{zip_code}}</span>
        </div>
        <div>
            <span class="subTitle">PAYABLE TO: </span> <span class="value">{{pay_to}}</span>
            <br/>
            <span class="value">{{pay_to_address}}</span>
        </div>
    </div>
    <div class="line"></div>
    <div class="contauineRousel">
        {{images}}
    </div>
    <div class="line"></div>
    <div>
        <table>
            <thead>
            <tr class="headingTable">
                <th class="subTitle">DESCRIPTION</th>
                <th class="subTitle">TOTAL</th>
            </tr>
            </thead>
            <tbody>
            {{items}}
            </tbody>
        </table>
    </div>
    <div class="amountDue">
        <span class="titleAmount">AMOUNT DUE: </span> <span class="valueAmount">${{total}}</span>
    </div>
    <div class="buttonStyle">
        <div class="containerBtn">
            <a href="{{api_url_approve}}"  class="button bgGreen">
                APPROVE
            </a>
            <a href="{{api_url_reject}}" class="button buttonRed">
                REJECT
            </a>
        </div>
    </div>
</div>
</body>
</html>