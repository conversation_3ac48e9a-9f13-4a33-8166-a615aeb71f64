{"name": "web-app-be", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "NODE_ENV=development nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "NODE_ENV=production node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@aws-sdk/client-s3": "^3.388.0", "@aws-sdk/s3-request-presigner": "^3.398.0", "@clerk/clerk-sdk-node": "^4.10.12", "@forlagshuset/nestjs-mongoose-paginate": "^1.2.7", "@golevelup/nestjs-stripe": "^0.7.0", "@hapi/joi": "^17.1.1", "@nestjs/cache-manager": "^2.2.2", "@nestjs/common": "^10.3.0", "@nestjs/config": "^3.1.1", "@nestjs/core": "^10.3.0", "@nestjs/event-emitter": "^2.1.1", "@nestjs/jwt": "^10.1.1", "@nestjs/mapped-types": "^2.0.2", "@nestjs/microservices": "^10.3.0", "@nestjs/mongoose": "^10.0.1", "@nestjs/platform-express": "^9.0.0", "@nestjs/platform-socket.io": "^10.4.6", "@nestjs/schedule": "^4.0.0", "@nestjs/swagger": "^7.0.4", "@nestjs/websockets": "^10.4.6", "@react-pdf/renderer": "^3.3.8", "@sendgrid/mail": "^7.7.0", "@types/cookie-parser": "^1.4.4", "@types/mongoose-sequence": "^3.0.7", "@types/multer": "^1.4.7", "amqp-connection-manager": "^4.1.14", "amqplib": "^0.10.3", "bcrypt": "^5.1.0", "cache-manager": "^5.7.6", "cache-manager-redis-store": "^3.0.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "cookie-parser": "^1.4.6", "dayjs": "^1.11.10", "dayjs-plugin-utc": "^0.1.2", "dotenv": "^16.3.1", "express-session": "^1.17.3", "fetch-blob": "^4.0.0", "google-auth-library": "^9.7.0", "googleapis": "^128.0.0", "ioredis": "^5.5.0", "mongodb": "^5.7.0", "mongoose": "^7.4.2", "mongoose-sequence": "^5.3.1", "nest-access-control": "^2.2.0", "nodemailer": "^6.9.7", "react": "^18.2.0", "reflect-metadata": "^0.1.13", "rrule": "^2.7.2", "rxjs": "^7.2.0", "socket.io": "^4.8.1", "stripe": "^15.3.0", "svix": "^1.4.12", "twilio": "^5.2.2", "uuid": "^9.0.0"}, "devDependencies": {"@nestjs/cli": "^9.0.0", "@nestjs/schematics": "^9.0.0", "@nestjs/testing": "^9.0.0", "@types/bcrypt": "^5.0.0", "@types/express": "^4.17.13", "@types/hapi__joi": "^17.1.11", "@types/jest": "29.5.1", "@types/lodash": "^4.14.202", "@types/node": "18.16.12", "@types/nodemailer": "^6.4.14", "@types/react": "^18.2.63", "@types/stripe": "^8.0.417", "@types/supertest": "^2.0.11", "@types/uuid": "^9.0.4", "@typescript-eslint/eslint-plugin": "^5.0.0", "@typescript-eslint/parser": "^5.0.0", "eslint": "^8.0.1", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "firebase-admin": "^12.0.0", "jest": "29.5.0", "moment": "^2.30.1", "prettier": "^2.3.2", "source-map-support": "^0.5.20", "supertest": "^6.1.3", "ts-jest": "29.1.0", "ts-loader": "^9.2.3", "ts-node": "^10.0.0", "tsconfig-paths": "4.2.0", "typescript": "^5.0.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "optionalDependencies": {"bufferutil": "^4.0.8", "utf-8-validate": "^6.0.4"}}