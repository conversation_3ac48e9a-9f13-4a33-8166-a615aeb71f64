import mongoose from "mongoose";
import dotenv from "dotenv";

dotenv.config()
const MONGO_URL = process.env.DB_STRING;

const ROLE_RESOURCES = {
  // AUTH: 'auth',
  ACTIVITIES_WORKING: 'activities-working',
  BOOKING_ADDON: 'booking-addon',
  BOOKING_EXTRA: 'booking-extra',
  BOOKING_FREQUENCY: 'booking-frequency',
  BOOKING_REQUEST: 'booking-request',
  // BOOKING_TIME_ARRIVAL: 'booking-time-arrival',
  BOOKING_TYPES: 'booking-types',
  CLIENT_TYPE: 'client-type',
  CLIENTS: 'clients',
  DISCOUNT: 'discount',
  GRANTS: 'grants',
  INVOICES: 'invoices',
  ISSUES: 'issues',
  JOBS: 'jobs',
  // MEDIAS: 'medias',
  QUOTES: 'quotes',
  ROLES: 'roles',
  SEND_EMAIL: 'send-email',
  TAX: 'tax',
  WORKSPACE: 'workspace',
  NOTIFICATION: 'notification',
}

const ACTIONS = [
  'create:any',
  'read:any',
  'update:any',
  'delete:any',
  'create:own',
  'read:own',
  'update:own',
  'delete:own',
]

export default ROLE_RESOURCES;

const getDb = async () => {
  return await mongoose.connect(MONGO_URL);
}


const insertsHandle = async () => {
  const db = await getDb();
  const grants = await db.connection.collection('grants').find({
    action: {$in: ['create:any', 'read:any', 'update:any', 'delete:any']}
  }).toArray()

  const role = await db.connection.collection('roles').updateMany({
    name: {
      $in: ['Staff', 'Cleaner']
    }
  },{
    $set: {
      grants: []
    }
  },{new: true})

  console.log(role);
}

insertsHandle();