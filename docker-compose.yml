version: "3.9"
services:
  caching:
    image: bitnami/redis:latest
    restart: on-failure
    environment:
      - ALLOW_EMPTY_PASSWORD=yes
    logging:
      options:
        max-size: '100k'
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/bitnami/redis/data

  mongodb:
    image: mongo:latest
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
    environment:
      - MONGO_INITDB_ROOT_USERNAME=root
      - MONGO_INITDB_ROOT_PASSWORD=password

volumes:
  redis_data:
  mongodb_data: